#!/usr/bin/env python3
"""
Cowboys @ Giants DraftKings Projections
Elite-level projections using team ratings and market data
"""

import pandas as pd
import numpy as np

def get_team_edges():
    """Get matchup edges from team ratings"""
    try:
        df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
        
        # Find Cowboys and Giants
        cowboys_row = df_holes_levers[df_holes_levers['team'].str.contains('Cowboys|DAL', case=False, na=False)]
        giants_row = df_holes_levers[df_holes_levers['team'].str.contains('Giants|NYG', case=False, na=False)]
        
        if not cowboys_row.empty and not giants_row.empty:
            cowboys = cowboys_row.iloc[0]
            giants = giants_row.iloc[0]
            
            # Cowboys advantages (from our earlier analysis)
            # Cowboys have elite pass protection (+1.42σ) and penalty discipline issues (+3.09σ hole)
            cowboys_protection_edge = cowboys.get('lever_protection', 1.42) - giants.get('hole_pressure', 0.3)
            cowboys_pass_edge = cowboys.get('lever_explosive_pass', 0.3) - giants.get('hole_pass_eff', 0.5)
            cowboys_rz_edge = cowboys.get('lever_rz', 0.2) - giants.get('hole_rz', 0.3)
            
            # Giants advantages
            # Giants have rush efficiency holes (+1.81σ) vs Cowboys protection
            giants_pass_edge = giants.get('lever_explosive_pass', 0.1) - cowboys.get('hole_pass_eff', 0.2)
            giants_rush_edge = giants.get('lever_ppd', 0.1) - cowboys.get('hole_rush_eff', 0.84)
            
            return {
                'cowboys_protection_edge': cowboys_protection_edge,
                'cowboys_pass_edge': cowboys_pass_edge,
                'cowboys_rz_edge': cowboys_rz_edge,
                'cowboys_penalty_hole': 3.09,  # Major penalty discipline issue
                'giants_pass_edge': giants_pass_edge,
                'giants_rush_edge': giants_rush_edge,
                'giants_rush_hole': 1.81  # Giants weak vs rush
            }
    except Exception as e:
        print(f"Using default edges: {e}")
    
    # Default edges based on team analysis
    return {
        'cowboys_protection_edge': 1.2,  # Cowboys elite pass protection
        'cowboys_pass_edge': 0.5,
        'cowboys_rz_edge': 0.3,
        'cowboys_penalty_hole': 3.0,    # Major penalty issues
        'giants_pass_edge': -0.2,       # Giants weaker passing
        'giants_rush_edge': 0.8,        # Giants vs Cowboys rush D
        'giants_rush_hole': 1.8         # Giants weak vs rush
    }

def get_mock_market_data():
    """Mock market data for Cowboys @ Giants"""
    return {
        'game_total': 44.5,
        'spread': -6.0,  # Cowboys favored by 6 at home
        'cowboys_implied': 25.25,
        'giants_implied': 19.25,
        'market_strength': 0.70,  # Moderate market confidence
        'market_uncertainty': 0.30,
        'player_props': {
            'Dak Prescott': {
                'pass_yards': 265.5,
                'pass_tds': 2.5,
                'rush_yards': 8.5
            },
            'CeeDee Lamb': {
                'rec_yards': 85.5,
                'receptions': 7.5,
                'rec_tds': 0.5
            },
            'Javonte Williams': {
                'rush_yards': 65.5,
                'rush_tds': 0.5
            },
            'Russell Wilson': {
                'pass_yards': 225.5,
                'pass_tds': 1.5,
                'rush_yards': 15.5
            },
            'Malik Nabers': {
                'rec_yards': 75.5,
                'receptions': 6.5
            },
            'Tyrone Tracy Jr.': {
                'rush_yards': 55.5,
                'rush_tds': 0.5
            }
        }
    }

def create_projections():
    """Create elite DraftKings projections"""
    
    edges = get_team_edges()
    market_data = get_mock_market_data()
    
    # DraftKings scoring
    scoring = {
        'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
        'rush_yard': 0.1, 'rush_td': 6,
        'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
        'fg_made': 3, 'xp_made': 1,
        'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
        'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4, 'dst_pts_14_20': 1,
        'dst_pts_21_27': 0, 'dst_pts_28_34': -1
    }
    
    # Model team totals with edges
    cowboys_base = {'pass_yds': 260, 'pass_tds': 2.2, 'rush_yds': 105, 'rush_tds': 1.1, 'points': 25}
    giants_base = {'pass_yds': 220, 'pass_tds': 1.6, 'rush_yds': 85, 'rush_tds': 0.8, 'points': 19}
    
    # Apply edges
    cowboys_adj = {
        'pass_yds': cowboys_base['pass_yds'] * (1 + 0.06 * edges['cowboys_pass_edge'] + 0.04 * edges['cowboys_protection_edge']),
        'pass_tds': cowboys_base['pass_tds'] * (1 + 0.08 * edges['cowboys_rz_edge']),
        'rush_yds': cowboys_base['rush_yds'] * (1 + 0.05 * 0.5),  # Moderate rush advantage
        'rush_tds': cowboys_base['rush_tds'] * (1 + 0.07 * edges['cowboys_rz_edge']),
        'points': cowboys_base['points']
    }
    
    giants_adj = {
        'pass_yds': giants_base['pass_yds'] * (1 + 0.06 * edges['giants_pass_edge']),
        'pass_tds': giants_base['pass_tds'] * (1 + 0.08 * (-0.2)),  # Slight RZ disadvantage
        'rush_yds': giants_base['rush_yds'] * (1 + 0.05 * edges['giants_rush_edge']),
        'rush_tds': giants_base['rush_tds'] * (1 + 0.07 * edges['giants_rush_edge']),
        'points': giants_base['points']
    }
    
    # Blending factor (α = 0.35 + market strength adjustment)
    alpha = 0.35 + 0.25 * market_data['market_strength']
    
    projections = {}
    
    # COWBOYS PLAYERS
    
    # Dak Prescott - Elite protection, home game
    model_dak_pass = cowboys_adj['pass_yds'] * 0.85
    prop_dak_pass = market_data['player_props']['Dak Prescott']['pass_yards']
    final_dak_pass = (1 - alpha) * model_dak_pass + alpha * prop_dak_pass
    
    projections['Dak Prescott'] = (
        final_dak_pass * scoring['pass_yard'] +
        cowboys_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        0.8 * scoring['pass_int'] +  # INTs
        10 * scoring['rush_yard']  # Minimal rushing
    )
    
    # CeeDee Lamb - Elite WR1 with target share
    model_ceedee_yds = final_dak_pass * 0.32  # 32% target share
    prop_ceedee_yds = market_data['player_props']['CeeDee Lamb']['rec_yards']
    final_ceedee_yds = (1 - alpha) * model_ceedee_yds + alpha * prop_ceedee_yds
    
    projections['CeeDee Lamb'] = (
        8 * scoring['reception'] +
        final_ceedee_yds * scoring['rec_yard'] +
        cowboys_adj['pass_tds'] * 0.4 * scoring['rec_td']
    )
    
    # Javonte Williams - RB1 vs Giants weak rush defense
    model_javonte_rush = cowboys_adj['rush_yds'] * 0.65
    prop_javonte_rush = market_data['player_props']['Javonte Williams']['rush_yards']
    final_javonte_rush = (1 - alpha) * model_javonte_rush + alpha * prop_javonte_rush
    
    projections['Javonte Williams'] = (
        final_javonte_rush * scoring['rush_yard'] +
        cowboys_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        3 * scoring['reception'] + 22 * scoring['rec_yard']
    )
    
    # GIANTS PLAYERS
    
    # Russell Wilson - Veteran QB on road
    model_russ_pass = giants_adj['pass_yds'] * 0.85
    prop_russ_pass = market_data['player_props']['Russell Wilson']['pass_yards']
    final_russ_pass = (1 - alpha) * model_russ_pass + alpha * prop_russ_pass
    
    projections['Russell Wilson'] = (
        final_russ_pass * scoring['pass_yard'] +
        giants_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        1.1 * scoring['pass_int'] +  # Higher INTs on road
        18 * scoring['rush_yard'] +  # Mobile veteran
        0.15 * scoring['rush_td']
    )
    
    # Malik Nabers - Rookie WR1 with upside
    model_nabers_yds = final_russ_pass * 0.30
    prop_nabers_yds = market_data['player_props']['Malik Nabers']['rec_yards']
    final_nabers_yds = (1 - alpha) * model_nabers_yds + alpha * prop_nabers_yds
    
    projections['Malik Nabers'] = (
        6.5 * scoring['reception'] +
        final_nabers_yds * scoring['rec_yard'] +
        giants_adj['pass_tds'] * 0.35 * scoring['rec_td']
    )
    
    # Tyrone Tracy Jr. - RB1 opportunity
    model_tracy_rush = giants_adj['rush_yds'] * 0.60
    prop_tracy_rush = market_data['player_props']['Tyrone Tracy Jr.']['rush_yards']
    final_tracy_rush = (1 - alpha) * model_tracy_rush + alpha * prop_tracy_rush
    
    projections['Tyrone Tracy Jr.'] = (
        final_tracy_rush * scoring['rush_yard'] +
        giants_adj['rush_tds'] * 0.5 * scoring['rush_td'] +
        2.5 * scoring['reception'] + 18 * scoring['rec_yard']
    )
    
    # SUPPORTING PLAYERS
    
    # Cowboys
    projections['Jake Ferguson'] = (
        4 * scoring['reception'] + 42 * scoring['rec_yard'] + 0.25 * scoring['rec_td']
    )
    
    projections['Jalen Tolbert'] = (
        3.5 * scoring['reception'] + 45 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )

    projections['George Pickens'] = (
        4.5 * scoring['reception'] + 58 * scoring['rec_yard'] + 0.3 * scoring['rec_td']
    )
    
    projections['Miles Sanders'] = (
        25 * scoring['rush_yard'] + 0.3 * scoring['rush_td'] +
        2 * scoring['reception'] + 15 * scoring['rec_yard']
    )
    
    projections['KaVontae Turpin'] = (
        2.5 * scoring['reception'] + 32 * scoring['rec_yard'] + 0.15 * scoring['rec_td']
    )
    
    # Giants
    projections['Wan\'Dale Robinson'] = (
        4.5 * scoring['reception'] + 48 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Darius Slayton'] = (
        3 * scoring['reception'] + 38 * scoring['rec_yard'] + 0.2 * scoring['rec_td']
    )
    
    projections['Theo Johnson'] = (
        3.5 * scoring['reception'] + 35 * scoring['rec_yard'] + 0.25 * scoring['rec_td']
    )
    
    projections['Devin Singletary'] = (
        20 * scoring['rush_yard'] + 0.2 * scoring['rush_td'] +
        2.5 * scoring['reception'] + 18 * scoring['rec_yard']
    )
    
    # DEFENSE/SPECIAL TEAMS
    
    # Cowboys DST - Home vs Giants offense
    projections['Cowboys'] = (
        2.5 * scoring['dst_sack'] +
        0.8 * scoring['dst_int'] +
        0.6 * scoring['dst_fumble_rec'] +
        0.2 * scoring['dst_td'] +
        scoring['dst_pts_14_20']  # Giants likely score 19-20
    )
    
    # Giants DST - Road vs Cowboys offense
    projections['Giants'] = (
        2.0 * scoring['dst_sack'] +
        0.6 * scoring['dst_int'] +
        0.4 * scoring['dst_fumble_rec'] +
        0.1 * scoring['dst_td'] +
        scoring['dst_pts_21_27']  # Cowboys likely score 25
    )
    
    # KICKERS (estimated from team points)
    projections['Brandon McManus'] = (
        2.2 * scoring['fg_made'] + 3.0 * scoring['xp_made']
    )
    
    projections['Graham Gano'] = (
        1.8 * scoring['fg_made'] + 2.5 * scoring['xp_made']
    )
    
    return projections

def main():
    """Generate and output projections"""
    projections = create_projections()
    
    # Sort by projection descending
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)
    
    print("Player,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

if __name__ == "__main__":
    main()
