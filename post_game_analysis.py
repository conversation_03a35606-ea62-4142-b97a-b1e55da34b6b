#!/usr/bin/env python3
"""
Post-Game Analysis: WAS @ GB Results vs Projections
Analyzing actual outcomes vs our props-driven projections to improve future accuracy.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple


class PostGameAnalyzer:
    """Analyze game results vs projections to identify improvement opportunities."""
    
    def __init__(self):
        # Game Results (from gamebook)
        self.actual_results = {
            # Final Score: WAS 18, GB 27
            'final_score': {'WAS': 18, 'GB': 27},
            'total_points': 45,  # Under the 49.0 total
            
            # Passing Stats
            'jayden_daniels': {
                'pass_att': 42, 'pass_cmp': 24, 'pass_yds': 200, 'pass_tds': 2, 'pass_ints': 0,
                'rush_att': 7, 'rush_yds': 17, 'rush_tds': 0,
                'fantasy_points': 0  # Will calculate
            },
            'jordan_love': {
                'pass_att': 31, 'pass_cmp': 19, 'pass_yds': 292, 'pass_tds': 2, 'pass_ints': 0,
                'rush_att': 3, 'rush_yds': 12, 'rush_tds': 0,
                'fantasy_points': 0  # Will calculate
            },
            
            # Rushing Stats
            'josh_jacobs': {
                'rush_att': 23, 'rush_yds': 84, 'rush_tds': 1,
                'rec': 0, 'rec_yds': 0, 'rec_tds': 0,
                'fantasy_points': 0
            },
            'austin_ekeler': {
                'rush_att': 8, 'rush_yds': 17, 'rush_tds': 0,
                'rec': 2, 'rec_yds': 7, 'rec_tds': 0,
                'fantasy_points': 0
            },
            
            # Receiving Stats
            'deebo_samuel': {
                'rec': 7, 'rec_yds': 44, 'rec_tds': 1,
                'rush_att': 0, 'rush_yds': 0, 'rush_tds': 0,
                'fantasy_points': 0
            },
            'terry_mclaurin': {
                'rec': 5, 'rec_yds': 48, 'rec_tds': 0,
                'fantasy_points': 0
            },
            'romeo_doubs': {
                'rec': 3, 'rec_yds': 28, 'rec_tds': 1,
                'fantasy_points': 0
            },
            'tucker_kraft': {
                'rec': 6, 'rec_yds': 124, 'rec_tds': 1,
                'fantasy_points': 0
            },
            'jayden_reed': {
                'rec': 0, 'rec_yds': 0, 'rec_tds': 0,  # Injured early
                'fantasy_points': 0
            },
            'zach_ertz': {
                'rec': 6, 'rec_yds': 64, 'rec_tds': 1,
                'fantasy_points': 0
            },
            
            # Kickers
            'brandon_mcmanus': {
                'fg_made': 2, 'fg_att': 3, 'xp_made': 3, 'xp_att': 3,
                'fantasy_points': 9  # 2 FGs (3+3) + 3 XPs = 9
            },
            'matt_gay': {
                'fg_made': 1, 'fg_att': 3, 'xp_made': 1, 'xp_att': 1,
                'fantasy_points': 4  # 1 FG (3) + 1 XP = 4
            },
            
            # Defense (estimated from game flow)
            'packers_dst': {
                'sacks': 6, 'ints': 0, 'fumbles': 0, 'def_tds': 0,
                'points_allowed': 18,  # 1-6 range = +7 pts
                'fantasy_points': 13  # 6 sacks + 7 for points allowed
            },
            'commanders_dst': {
                'sacks': 4, 'ints': 0, 'fumbles': 0, 'def_tds': 0,
                'points_allowed': 27,  # 21-27 range = 0 pts
                'fantasy_points': 4  # 4 sacks + 0 for points allowed
            }
        }
        
        # DraftKings Scoring
        self.dk_scoring = {
            'pass_yards': 0.04, 'pass_tds': 4.0, 'pass_ints': -1.0,
            'rush_yards': 0.1, 'rush_tds': 6.0,
            'rec_yards': 0.1, 'receptions': 1.0, 'rec_tds': 6.0,
            'pass_300_bonus': 3.0, 'rush_100_bonus': 3.0, 'rec_100_bonus': 3.0
        }
    
    def calculate_actual_fantasy_points(self):
        """Calculate actual DraftKings fantasy points for each player."""
        
        # Jayden Daniels
        jd = self.actual_results['jayden_daniels']
        jd['fantasy_points'] = (
            jd['pass_yds'] * self.dk_scoring['pass_yards'] +
            jd['pass_tds'] * self.dk_scoring['pass_tds'] +
            jd['pass_ints'] * self.dk_scoring['pass_ints'] +
            jd['rush_yds'] * self.dk_scoring['rush_yards'] +
            jd['rush_tds'] * self.dk_scoring['rush_tds']
        )
        
        # Jordan Love  
        jl = self.actual_results['jordan_love']
        jl['fantasy_points'] = (
            jl['pass_yds'] * self.dk_scoring['pass_yards'] +
            jl['pass_tds'] * self.dk_scoring['pass_tds'] +
            jl['pass_ints'] * self.dk_scoring['pass_ints'] +
            jl['rush_yds'] * self.dk_scoring['rush_yards'] +
            jl['rush_tds'] * self.dk_scoring['rush_tds']
        )
        
        # Josh Jacobs
        jj = self.actual_results['josh_jacobs']
        jj['fantasy_points'] = (
            jj['rush_yds'] * self.dk_scoring['rush_yards'] +
            jj['rush_tds'] * self.dk_scoring['rush_tds'] +
            jj['rec'] * self.dk_scoring['receptions'] +
            jj['rec_yds'] * self.dk_scoring['rec_yards'] +
            jj['rec_tds'] * self.dk_scoring['rec_tds']
        )
        
        # Austin Ekeler
        ae = self.actual_results['austin_ekeler']
        ae['fantasy_points'] = (
            ae['rush_yds'] * self.dk_scoring['rush_yards'] +
            ae['rush_tds'] * self.dk_scoring['rush_tds'] +
            ae['rec'] * self.dk_scoring['receptions'] +
            ae['rec_yds'] * self.dk_scoring['rec_yards'] +
            ae['rec_tds'] * self.dk_scoring['rec_tds']
        )
        
        # Receivers
        for player in ['deebo_samuel', 'terry_mclaurin', 'romeo_doubs', 'tucker_kraft', 'zach_ertz']:
            p = self.actual_results[player]
            p['fantasy_points'] = (
                p['rec'] * self.dk_scoring['receptions'] +
                p['rec_yds'] * self.dk_scoring['rec_yards'] +
                p['rec_tds'] * self.dk_scoring['rec_tds']
            )
            
            # Add 100+ yard bonus if applicable
            if p['rec_yds'] >= 100:
                p['fantasy_points'] += self.dk_scoring['rec_100_bonus']
        
        # Jayden Reed (injured, 0 points)
        self.actual_results['jayden_reed']['fantasy_points'] = 0
    
    def load_our_projections(self) -> Dict[str, float]:
        """Load our final projections for comparison."""
        try:
            proj_df = pd.read_csv('csvs/was_gb_COMPLETE_DK_projections_20250911_2208.csv')
            projections = {}
            
            for _, row in proj_df.iterrows():
                player_name = row['player_name'].lower().replace(' ', '_').replace('dst', 'dst')
                projections[player_name] = row['dk_projection']
            
            return projections
            
        except Exception as e:
            print(f"Error loading projections: {e}")
            return {}
    
    def analyze_projection_accuracy(self) -> Dict[str, Any]:
        """Analyze how accurate our projections were."""
        self.calculate_actual_fantasy_points()
        projections = self.load_our_projections()
        
        analysis = {
            'player_comparisons': [],
            'overall_accuracy': {},
            'major_misses': [],
            'major_hits': []
        }
        
        # Player-by-player comparison
        player_mapping = {
            'jayden_daniels': 'jayden_daniels',
            'jordan_love': 'jordan_love', 
            'josh_jacobs': 'josh_jacobs',
            'austin_ekeler': 'austin_ekeler',
            'deebo_samuel': 'deebo_samuel',
            'terry_mclaurin': 'terry_mclaurin',
            'romeo_doubs': 'romeo_doubs',
            'tucker_kraft': 'tucker_kraft',
            'jayden_reed': 'jayden_reed',
            'zach_ertz': 'zach_ertz',
            'brandon_mcmanus': 'brandon_mcmanus',
            'matt_gay': 'matt_gay',
            'packers_dst': 'packers_dst',
            'commanders_dst': 'commanders_dst'
        }
        
        total_error = 0
        comparisons = 0
        
        for actual_key, proj_key in player_mapping.items():
            if actual_key in self.actual_results and proj_key in projections:
                actual = self.actual_results[actual_key]['fantasy_points']
                projected = projections[proj_key]
                error = abs(actual - projected)
                error_pct = (error / max(actual, 1)) * 100
                
                comparison = {
                    'player': actual_key.replace('_', ' ').title(),
                    'projected': projected,
                    'actual': actual,
                    'error': error,
                    'error_pct': error_pct,
                    'hit_type': self.classify_accuracy(error_pct)
                }
                
                analysis['player_comparisons'].append(comparison)
                total_error += error
                comparisons += 1
                
                # Identify major misses and hits
                if error_pct > 50 and actual > 5:  # Major miss on significant player
                    analysis['major_misses'].append(comparison)
                elif error_pct < 15:  # Very accurate
                    analysis['major_hits'].append(comparison)
        
        # Overall accuracy metrics
        if comparisons > 0:
            analysis['overall_accuracy'] = {
                'mean_absolute_error': total_error / comparisons,
                'total_comparisons': comparisons,
                'accuracy_distribution': self.get_accuracy_distribution(analysis['player_comparisons'])
            }
        
        return analysis
    
    def classify_accuracy(self, error_pct: float) -> str:
        """Classify prediction accuracy."""
        if error_pct < 15:
            return 'Excellent'
        elif error_pct < 30:
            return 'Good'
        elif error_pct < 50:
            return 'Fair'
        else:
            return 'Poor'
    
    def get_accuracy_distribution(self, comparisons: List[Dict]) -> Dict[str, int]:
        """Get distribution of accuracy classifications."""
        distribution = {'Excellent': 0, 'Good': 0, 'Fair': 0, 'Poor': 0}
        
        for comp in comparisons:
            distribution[comp['hit_type']] += 1
        
        return distribution
    
    def analyze_prop_accuracy(self) -> Dict[str, Any]:
        """Analyze how our prop predictions performed vs actual results."""
        
        # Load our prop analysis
        try:
            props_df = pd.read_csv('csvs/was_gb_PROP_REVIEW_20250911_2202.csv')
        except:
            return {'error': 'Could not load prop analysis'}
        
        prop_results = []
        
        # Key prop comparisons
        prop_comparisons = [
            ('Jayden Daniels', 'player_pass_yds', 227.2, 200),  # Our line vs actual
            ('Jayden Daniels', 'player_rush_yds', 46.2, 17),
            ('Jordan Love', 'player_pass_yds', 237.8, 292),
            ('Josh Jacobs', 'player_rush_yds', 78.3, 84),
            ('Tucker Kraft', 'player_reception_yds', 40.5, 124),
            ('Terry McLaurin', 'player_reception_yds', 51.5, 48),
            ('Deebo Samuel', 'player_reception_yds', 51.7, 44),
            ('Romeo Doubs', 'player_reception_yds', 42.5, 28)
        ]
        
        for player, market, line, actual in prop_comparisons:
            # Find our recommendation
            player_props = props_df[
                (props_df['player_name'].str.contains(player, case=False, na=False)) &
                (props_df['market'] == market)
            ]
            
            if not player_props.empty:
                our_number = player_props.iloc[0]['our_final_number']
                recommendation = player_props.iloc[0]['recommendation']
                
                # Determine if our recommendation was correct
                if recommendation == 'OVER':
                    correct = actual > line
                elif recommendation == 'UNDER':
                    correct = actual < line
                else:
                    correct = abs(actual - line) <= line * 0.05  # Within 5%
                
                prop_results.append({
                    'player': player,
                    'market': market,
                    'line': line,
                    'our_number': our_number,
                    'actual': actual,
                    'recommendation': recommendation,
                    'correct': correct,
                    'our_error': abs(our_number - actual),
                    'line_error': abs(line - actual)
                })
        
        return {
            'prop_results': prop_results,
            'correct_recommendations': sum(1 for r in prop_results if r['correct']),
            'total_recommendations': len(prop_results),
            'accuracy_rate': sum(1 for r in prop_results if r['correct']) / len(prop_results) if prop_results else 0
        }
    
    def identify_key_learnings(self, projection_analysis: Dict, prop_analysis: Dict) -> List[str]:
        """Identify key learnings for future improvements."""
        learnings = []
        
        # Projection learnings
        major_misses = projection_analysis.get('major_misses', [])
        for miss in major_misses:
            if 'daniels' in miss['player'].lower():
                learnings.append("Jayden Daniels rushing projection was too high - need to be more conservative on mobile QB rushing in road games")
            elif 'kraft' in miss['player'].lower():
                learnings.append("Tucker Kraft massively outperformed - need to weight target share props more heavily for TEs")
            elif 'reed' in miss['player'].lower():
                learnings.append("Jayden Reed injury risk not factored - need injury probability adjustments")
        
        # Prop learnings
        prop_results = prop_analysis.get('prop_results', [])
        for prop in prop_results:
            if not prop['correct'] and prop['our_error'] > prop['line_error']:
                if 'pass_yds' in prop['market']:
                    learnings.append(f"Passing yards projection for {prop['player']} was less accurate than betting line - reconsider sharp money weighting")
                elif 'rush_yds' in prop['market']:
                    learnings.append(f"Rushing yards for {prop['player']} - game script impact was underestimated")
        
        # General learnings
        accuracy_dist = projection_analysis.get('overall_accuracy', {}).get('accuracy_distribution', {})
        if accuracy_dist.get('Poor', 0) > accuracy_dist.get('Excellent', 0):
            learnings.append("Overall projection accuracy needs improvement - consider adjusting prop weighting from 50% to 40%")
        
        return learnings


def main():
    """Run complete post-game analysis."""
    analyzer = PostGameAnalyzer()
    
    print("=== POST-GAME ANALYSIS: WAS @ GB ===")
    print("Final Score: WAS 18, GB 27 (Total: 45 - UNDER 49.0)")
    
    # Projection accuracy analysis
    print("\n📊 PROJECTION ACCURACY ANALYSIS:")
    proj_analysis = analyzer.analyze_projection_accuracy()
    
    print(f"\nOverall Accuracy:")
    overall = proj_analysis.get('overall_accuracy', {})
    print(f"  Mean Absolute Error: {overall.get('mean_absolute_error', 0):.1f} points")
    print(f"  Total Comparisons: {overall.get('total_comparisons', 0)}")
    
    accuracy_dist = overall.get('accuracy_distribution', {})
    print(f"  Excellent (<15% error): {accuracy_dist.get('Excellent', 0)}")
    print(f"  Good (15-30% error): {accuracy_dist.get('Good', 0)}")
    print(f"  Fair (30-50% error): {accuracy_dist.get('Fair', 0)}")
    print(f"  Poor (>50% error): {accuracy_dist.get('Poor', 0)}")
    
    # Major hits and misses
    print(f"\n🎯 MAJOR HITS:")
    for hit in proj_analysis.get('major_hits', []):
        print(f"  {hit['player']}: {hit['projected']:.1f} proj vs {hit['actual']:.1f} actual ({hit['error']:.1f} error)")
    
    print(f"\n❌ MAJOR MISSES:")
    for miss in proj_analysis.get('major_misses', []):
        print(f"  {miss['player']}: {miss['projected']:.1f} proj vs {miss['actual']:.1f} actual ({miss['error']:.1f} error)")
    
    # Prop accuracy analysis
    print(f"\n🎲 PROP RECOMMENDATION ACCURACY:")
    prop_analysis = analyzer.analyze_prop_accuracy()
    
    if 'prop_results' in prop_analysis:
        correct = prop_analysis['correct_recommendations']
        total = prop_analysis['total_recommendations']
        accuracy = prop_analysis['accuracy_rate']
        
        print(f"  Correct Recommendations: {correct}/{total} ({accuracy:.1%})")
        
        print(f"\n  Individual Prop Results:")
        for prop in prop_analysis['prop_results']:
            status = "✅" if prop['correct'] else "❌"
            print(f"    {status} {prop['player']} {prop['market']}: {prop['recommendation']} "
                  f"(Line: {prop['line']}, Actual: {prop['actual']})")
    
    # Key learnings
    print(f"\n🧠 KEY LEARNINGS FOR FUTURE:")
    learnings = analyzer.identify_key_learnings(proj_analysis, prop_analysis)
    for i, learning in enumerate(learnings, 1):
        print(f"  {i}. {learning}")
    
    return proj_analysis, prop_analysis


if __name__ == "__main__":
    main()
