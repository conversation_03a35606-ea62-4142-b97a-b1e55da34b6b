#!/usr/bin/env python3
"""
WAS @ GB Player Props Fetcher and Projections Generator
Fetches live player props from The Odds API and creates projections for the Washington @ Green Bay game.
"""

import os
import sys
import pandas as pd
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append('src')

from proj.fetch_odds import (
    get_totals_spreads,
    get_player_props,
    find_game_event_id,
    process_player_props_response
)

load_dotenv()


class WasGBPropsAnalyzer:
    """Analyzer for Washington @ Green Bay player props and projections."""
    
    def __init__(self):
        self.api_key = os.getenv('ODDS_API_KEY')
        if not self.api_key or self.api_key == 'your_odds_api_key_here':
            print("⚠️  WARNING: No valid ODDS_API_KEY found in .env file")
            print("   You'll need to manually gather player props data")
            self.api_key = None
        
        self.home_team = "Green Bay Packers"
        self.away_team = "Washington Commanders"
        self.game_info = {
            'home_team': 'GB',
            'away_team': 'WAS',
            'spread': None,  # Will be fetched
            'total': None,   # Will be fetched
        }
        
    def fetch_game_odds(self) -> bool:
        """Fetch basic game odds (spread/total) for WAS @ GB."""
        if not self.api_key:
            print("No API key - using default odds")
            self.game_info.update({
                'spread': -3.5,  # GB favored by 3.5
                'total': 49.0,
                'gb_implied_total': 26.25,
                'was_implied_total': 22.75
            })
            return False
            
        try:
            print("Fetching game odds from The Odds API...")
            odds_data = get_totals_spreads(self.api_key)
            
            # Find WAS @ GB game
            for game in odds_data:
                home = game.get('home_team', '').lower()
                away = game.get('away_team', '').lower()
                
                if (('green bay' in home or 'packers' in home) and 
                    ('washington' in away or 'commanders' in away)):
                    
                    self.game_info.update({
                        'spread': game.get('spread', -3.5),
                        'total': game.get('total', 49.0),
                        'gb_implied_total': game.get('home_implied_total', 26.25),
                        'was_implied_total': game.get('away_implied_total', 22.75),
                        'event_id': game.get('id')
                    })
                    print(f"✅ Found game: {away.title()} @ {home.title()}")
                    print(f"   Spread: GB {self.game_info['spread']}")
                    print(f"   Total: {self.game_info['total']}")
                    return True
            
            print("❌ WAS @ GB game not found in current odds")
            return False
            
        except Exception as e:
            print(f"❌ Error fetching odds: {e}")
            return False
    
    def fetch_player_props(self) -> Optional[pd.DataFrame]:
        """Fetch player props for WAS @ GB game."""
        if not self.api_key:
            print("❌ Cannot fetch player props without API key")
            return None
            
        event_id = self.game_info.get('event_id')
        if not event_id:
            # Try to find event ID
            print("Searching for WAS @ GB event ID...")
            event_id = find_game_event_id(self.api_key, self.home_team, self.away_team)
            
        if not event_id:
            print("❌ Could not find event ID for WAS @ GB game")
            return None
            
        try:
            print(f"Fetching player props for event {event_id}...")
            props_data = get_player_props(self.api_key, event_id)
            
            if not props_data:
                print("❌ No player props data returned")
                return None
                
            # Process into DataFrame
            props_df = process_player_props_response(props_data)
            
            if props_df.empty:
                print("❌ No player props found after processing")
                return None
                
            print(f"✅ Found {len(props_df)} player props")
            print(f"   Markets: {sorted(props_df['market'].unique())}")
            print(f"   Players: {len(props_df['player_name'].unique())} unique players")
            
            return props_df
            
        except Exception as e:
            print(f"❌ Error fetching player props: {e}")
            return None
    
    def analyze_props_coverage(self, props_df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze the coverage and quality of props data."""
        if props_df.empty:
            return {'status': 'no_data'}
        
        analysis = {
            'total_props': len(props_df),
            'unique_players': len(props_df['player_name'].unique()),
            'unique_markets': len(props_df['market'].unique()),
            'bookmakers': props_df['bookmaker'].unique().tolist(),
            'markets_by_count': props_df['market'].value_counts().to_dict(),
            'players_by_prop_count': props_df['player_name'].value_counts().head(10).to_dict()
        }
        
        # Check for key players from DraftKings data
        dk_players = ['Jayden Daniels', 'Josh Jacobs', 'Jordan Love', 'Terry McLaurin', 
                     'Jayden Reed', 'Austin Ekeler', 'Romeo Doubs', 'Tucker Kraft']
        
        found_players = []
        missing_players = []
        
        for player in dk_players:
            if any(player.lower() in name.lower() for name in props_df['player_name'].unique()):
                found_players.append(player)
            else:
                missing_players.append(player)
        
        analysis.update({
            'key_players_found': found_players,
            'key_players_missing': missing_players,
            'coverage_score': len(found_players) / len(dk_players) if dk_players else 0
        })
        
        return analysis
    
    def save_props_data(self, props_df: pd.DataFrame, filename: str = None) -> str:
        """Save props data to CSV for manual review."""
        if filename is None:
            filename = f"csvs/was_gb_player_props_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        
        props_df.to_csv(filename, index=False)
        print(f"💾 Props data saved to: {filename}")
        return filename
    
    def run_analysis(self) -> Dict[str, Any]:
        """Run complete props analysis for WAS @ GB."""
        print("=== WAS @ GB PLAYER PROPS ANALYSIS ===")
        
        # Step 1: Get basic game odds
        odds_success = self.fetch_game_odds()
        
        # Step 2: Try to fetch player props
        props_df = self.fetch_player_props()
        
        results = {
            'game_info': self.game_info,
            'odds_fetched': odds_success,
            'props_available': props_df is not None,
            'api_key_valid': self.api_key is not None
        }
        
        if props_df is not None:
            # Step 3: Analyze props coverage
            analysis = self.analyze_props_coverage(props_df)
            results['props_analysis'] = analysis
            
            # Step 4: Save data
            filename = self.save_props_data(props_df)
            results['props_file'] = filename
            
            # Step 5: Display summary
            print(f"\n📊 PROPS SUMMARY:")
            print(f"   Total Props: {analysis['total_props']}")
            print(f"   Unique Players: {analysis['unique_players']}")
            print(f"   Markets: {analysis['unique_markets']}")
            print(f"   Key Players Found: {len(analysis['key_players_found'])}/8")
            
            if analysis['key_players_missing']:
                print(f"   Missing: {', '.join(analysis['key_players_missing'])}")
        
        return results


def main():
    """Main execution function."""
    analyzer = WasGBPropsAnalyzer()
    results = analyzer.run_analysis()
    
    if not results['api_key_valid']:
        print("\n🔧 NEXT STEPS:")
        print("1. Get a free API key from https://the-odds-api.com/")
        print("2. Add it to your .env file: ODDS_API_KEY=your_actual_key")
        print("3. Re-run this script to fetch live props")
        print("4. Or manually gather props data and save as CSV")
        
    elif not results['props_available']:
        print("\n⚠️  FALLBACK OPTIONS:")
        print("1. Check if the game is available in The Odds API")
        print("2. Manually gather props from sportsbooks")
        print("3. Use existing DraftKings data for projections")
        
    else:
        print("\n✅ SUCCESS! Player props data fetched and saved.")
        print("   You can now create projections using this data.")
    
    return results


if __name__ == "__main__":
    main()
