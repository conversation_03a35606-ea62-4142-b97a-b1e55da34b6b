#!/usr/bin/env python3
"""
Test script for the enhanced projection engine.
"""

import sys
sys.path.append('src')

from proj.enhanced_projections import EnhancedProjectionEngine

def main():
    """Test the enhanced projection engine."""
    print("=== TESTING ENHANCED PROJECTION ENGINE ===")
    
    # Initialize engine
    engine = EnhancedProjectionEngine()
    
    # Load data
    try:
        engine.load_data(
            depth_file='data/depth_week1.parquet',
            props_file='data/player_props.parquet', 
            odds_file='data/odds_week1.json'
        )
        print("✓ Data loaded successfully")
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return
    
    # Test DET vs GB projections
    try:
        print("\n=== Testing DET vs GB Projections ===")
        projections = engine.generate_projections(['DET', 'GB'], 'test_enhanced_det_gb.csv')
        print("✓ DET vs GB projections generated successfully")
        
        # Show top projections
        print("\nTop 10 projections:")
        top_10 = projections.head(10)
        for _, player in top_10.iterrows():
            print(f"  {player['player_name']} ({player['position']}): {player['proj_mean']}")
            
    except Exception as e:
        print(f"✗ Error generating DET vs GB projections: {e}")
        return
    
    # Test all target teams
    try:
        print("\n=== Testing All Target Teams ===")
        all_teams = ['DET', 'GB', 'TEN', 'DEN', 'SF', 'SEA', 'HOU', 'LAR']
        all_projections = engine.generate_projections(all_teams, 'test_enhanced_all_teams.csv')
        print("✓ All teams projections generated successfully")
        
        print(f"\nTotal projections: {len(all_projections)}")
        print(f"Teams covered: {sorted(all_projections['team'].unique())}")
        
    except Exception as e:
        print(f"✗ Error generating all teams projections: {e}")
        return
    
    print("\n✓ All tests passed! Enhanced projection engine is working correctly.")

if __name__ == "__main__":
    main()
