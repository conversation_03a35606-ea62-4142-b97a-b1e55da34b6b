#!/usr/bin/env python3
"""
Enhanced NFL Projection System with Gamebook Intelligence
Combines market signals with gamebook-derived team intelligence
"""

import json
import pandas as pd
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import numpy as np

from gamebook_intelligence import GamebookIntelligence
from integrated_nfl_system import IntegratedNFLSystem


@dataclass
class EnhancedProjection:
    """Enhanced projection with gamebook intelligence."""
    player: str
    position: str
    team: str
    opponent: str
    base_projection: float
    market_adjustment: float
    gamebook_adjustment: float
    final_projection: float
    confidence: float
    reasoning: List[str]


class EnhancedProjectionSystem:
    """NFL projection system with gamebook intelligence layer."""
    
    def __init__(self, odds_api_key: str = None):
        self.odds_api_key = odds_api_key
        self.nfl_system = IntegratedNFLSystem(odds_api_key) if odds_api_key else None
        self.gamebook_intelligence = GamebookIntelligence()
        self.team_intelligence_loaded = False
        
        # DraftKings scoring system
        self.dk_scoring = {
            'pass_yard': 0.04,
            'pass_td': 4.0,
            'pass_int': -1.0,
            'rush_yard': 0.1,
            'rush_td': 6.0,
            'rec_yard': 0.1,
            'rec': 1.0,
            'rec_td': 6.0,
            'fumble': -1.0,
            'dst_sack': 1.0,
            'dst_int': 2.0,
            'dst_fumble_rec': 2.0,
            'dst_td': 6.0,
            'dst_safety': 2.0,
            'dst_block': 2.0,
            'fg_made': 3.0,
            'xp_made': 1.0
        }
        
        # Position-specific adjustment factors
        self.position_factors = {
            'QB': {
                'explosive_offense': 0.15,
                'weak_pass_defense': 0.20,
                'strong_pass_rush': -0.12,
                'weather_impact': -0.08
            },
            'RB': {
                'explosive_offense': 0.12,
                'weak_run_defense': 0.25,
                'strong_run_defense': -0.18,
                'game_script_positive': 0.15
            },
            'WR': {
                'explosive_offense': 0.18,
                'weak_pass_defense': 0.22,
                'target_share_boost': 0.20,
                'weather_impact': -0.05
            },
            'TE': {
                'explosive_offense': 0.10,
                'weak_te_coverage': 0.30,
                'red_zone_target': 0.15,
                'game_script_positive': 0.08
            },
            'K': {
                'dome_boost': 0.10,
                'weather_penalty': -0.15,
                'high_total': 0.08
            },
            'DST': {
                'weak_offense_opponent': 0.25,
                'turnover_prone_qb': 0.20,
                'home_field': 0.08,
                'weather_boost': 0.05
            }
        }
    
    def load_gamebook_intelligence(self) -> None:
        """Load and analyze gamebook data."""
        if not self.team_intelligence_loaded:
            print("🧠 LOADING GAMEBOOK INTELLIGENCE...")
            self.gamebook_intelligence.run_full_analysis()
            self.team_intelligence_loaded = True
            print("✅ Gamebook intelligence loaded!")
    
    def run_enhanced_projections(self, game_info: Dict[str, str], 
                               base_projections: Dict[str, float] = None) -> Dict[str, Any]:
        """Run complete enhanced projection system."""
        
        # Ensure gamebook intelligence is loaded
        self.load_gamebook_intelligence()
        
        away_team = game_info['away_team']
        home_team = game_info['home_team']
        
        print(f"🎯 RUNNING ENHANCED PROJECTIONS: {away_team} @ {home_team}")
        print("=" * 55)
        
        # Step 1: Get market data and signals (if API key provided)
        market_results = {}
        if self.nfl_system and self.odds_api_key:
            print("📊 Fetching market data and signals...")
            try:
                market_results = self.nfl_system.run_complete_analysis(base_projections or {})
            except Exception as e:
                print(f"⚠️ Market data unavailable: {str(e)}")
                market_results = {'recommendations': [], 'market_signals': {}}
        
        # Step 2: Get gamebook matchup analysis
        print("🧠 Analyzing gamebook intelligence...")
        matchup_analysis = self.gamebook_intelligence.get_matchup_analysis(away_team, home_team)
        
        # Step 3: Generate enhanced projections
        print("⚡ Generating enhanced projections...")
        enhanced_projections = self._generate_enhanced_projections(
            game_info, base_projections or {}, market_results, matchup_analysis
        )
        
        # Step 4: Create final output
        results = {
            'game_info': game_info,
            'enhanced_projections': enhanced_projections,
            'market_analysis': market_results.get('market_signals', {}),
            'gamebook_analysis': matchup_analysis,
            'team_rankings': self.gamebook_intelligence.get_team_rankings(),
            'final_recommendations': self._create_final_recommendations(enhanced_projections)
        }
        
        return results
    
    def _generate_enhanced_projections(self, game_info: Dict, base_projections: Dict,
                                     market_results: Dict, matchup_analysis: Dict) -> List[EnhancedProjection]:
        """Generate enhanced projections with all intelligence layers."""
        
        enhanced_projections = []
        away_team = game_info['away_team']
        home_team = game_info['home_team']
        
        # Get team profiles
        away_profile = self.gamebook_intelligence.team_profiles.get(away_team)
        home_profile = self.gamebook_intelligence.team_profiles.get(home_team)
        
        # Sample players for demonstration (in real system, would get from roster data)
        sample_players = self._get_sample_players(away_team, home_team)
        
        for player_info in sample_players:
            player = player_info['name']
            position = player_info['position']
            team = player_info['team']
            opponent = home_team if team == away_team else away_team
            
            # Base projection
            base_proj = base_projections.get(player, self._estimate_base_projection(position))
            
            # Market adjustment
            market_adj = self._calculate_market_adjustment(player, market_results)
            
            # Gamebook adjustment
            gamebook_adj = self._calculate_gamebook_adjustment(
                player_info, matchup_analysis, away_profile, home_profile
            )
            
            # Final projection
            final_proj = base_proj * (1 + market_adj + gamebook_adj)
            
            # Confidence calculation
            confidence = self._calculate_enhanced_confidence(
                player_info, market_results, matchup_analysis
            )
            
            # Reasoning
            reasoning = self._generate_reasoning(
                player_info, market_adj, gamebook_adj, matchup_analysis
            )
            
            enhanced_projection = EnhancedProjection(
                player=player,
                position=position,
                team=team,
                opponent=opponent,
                base_projection=base_proj,
                market_adjustment=market_adj,
                gamebook_adjustment=gamebook_adj,
                final_projection=final_proj,
                confidence=confidence,
                reasoning=reasoning
            )
            
            enhanced_projections.append(enhanced_projection)
        
        # Sort by final projection
        enhanced_projections.sort(key=lambda x: x.final_projection, reverse=True)
        
        return enhanced_projections
    
    def _get_sample_players(self, away_team: str, home_team: str) -> List[Dict]:
        """Get sample players for demonstration."""
        # In real system, would pull from roster/depth chart data
        sample_players = [
            {'name': f'{away_team} QB1', 'position': 'QB', 'team': away_team},
            {'name': f'{home_team} QB1', 'position': 'QB', 'team': home_team},
            {'name': f'{away_team} RB1', 'position': 'RB', 'team': away_team},
            {'name': f'{home_team} RB1', 'position': 'RB', 'team': home_team},
            {'name': f'{away_team} WR1', 'position': 'WR', 'team': away_team},
            {'name': f'{home_team} WR1', 'position': 'WR', 'team': home_team},
            {'name': f'{away_team} TE1', 'position': 'TE', 'team': away_team},
            {'name': f'{home_team} TE1', 'position': 'TE', 'team': home_team},
            {'name': f'{away_team} DST', 'position': 'DST', 'team': away_team},
            {'name': f'{home_team} DST', 'position': 'DST', 'team': home_team},
        ]
        
        return sample_players
    
    def _estimate_base_projection(self, position: str) -> float:
        """Estimate base projection by position."""
        base_projections = {
            'QB': 18.5,
            'RB': 12.8,
            'WR': 11.2,
            'TE': 8.5,
            'K': 7.2,
            'DST': 8.0
        }
        return base_projections.get(position, 8.0)
    
    def _calculate_market_adjustment(self, player: str, market_results: Dict) -> float:
        """Calculate market-based adjustment."""
        if not market_results or 'recommendations' not in market_results:
            return 0.0
        
        # Look for player in market recommendations
        for rec in market_results.get('recommendations', []):
            if player in rec.get('player', ''):
                edge = rec.get('edge', 0)
                confidence = rec.get('confidence', 0.5)
                return edge * confidence * 0.15  # Scale market impact
        
        return 0.0
    
    def _calculate_gamebook_adjustment(self, player_info: Dict, matchup_analysis: Dict,
                                     away_profile: Any, home_profile: Any) -> float:
        """Calculate gamebook intelligence adjustment."""
        if 'error' in matchup_analysis:
            return 0.0
        
        position = player_info['position']
        team = player_info['team']
        
        # Get position-specific factors
        factors = self.position_factors.get(position, {})
        
        # Get projection adjustments from matchup analysis
        proj_adjustments = matchup_analysis.get('projection_adjustments', {})
        
        # Calculate adjustment based on team and matchup
        adjustment = 0.0
        
        # Overall matchup edge
        overall_edge = matchup_analysis.get('overall_edge', 0)
        if team in matchup_analysis.get('matchup', '').split(' @ ')[0]:  # Away team
            adjustment += overall_edge * 0.10
        else:  # Home team
            adjustment -= overall_edge * 0.10
        
        # Position-specific adjustments
        if 'offensive_boost' in proj_adjustments:
            adjustment += proj_adjustments['offensive_boost'] * factors.get('explosive_offense', 0.1)
        
        if 'situational_boost' in proj_adjustments:
            adjustment += proj_adjustments['situational_boost'] * 0.8
        
        # Cap adjustment at reasonable bounds
        return max(-0.25, min(0.25, adjustment))
    
    def _calculate_enhanced_confidence(self, player_info: Dict, market_results: Dict,
                                     matchup_analysis: Dict) -> float:
        """Calculate enhanced confidence score."""
        base_confidence = 0.70
        
        # Market confidence boost
        market_boost = 0.0
        if market_results and 'recommendations' in market_results:
            for rec in market_results.get('recommendations', []):
                if player_info['name'] in rec.get('player', ''):
                    market_boost = rec.get('confidence', 0.5) * 0.15
                    break
        
        # Gamebook confidence factors
        gamebook_factors = matchup_analysis.get('confidence_factors', {})
        gamebook_boost = gamebook_factors.get('overall_confidence', 0.5) * 0.15
        
        final_confidence = base_confidence + market_boost + gamebook_boost
        return max(0.3, min(0.95, final_confidence))
    
    def _generate_reasoning(self, player_info: Dict, market_adj: float,
                          gamebook_adj: float, matchup_analysis: Dict) -> List[str]:
        """Generate reasoning for projection."""
        reasoning = []
        
        # Market reasoning
        if abs(market_adj) > 0.05:
            direction = "boosted" if market_adj > 0 else "reduced"
            reasoning.append(f"Market signals {direction} projection by {abs(market_adj):.1%}")
        
        # Gamebook reasoning
        if abs(gamebook_adj) > 0.05:
            direction = "boosted" if gamebook_adj > 0 else "reduced"
            reasoning.append(f"Matchup analysis {direction} projection by {abs(gamebook_adj):.1%}")
        
        # Key advantages
        key_advantages = matchup_analysis.get('key_advantages', [])
        if key_advantages:
            reasoning.append(f"Key factors: {', '.join(key_advantages[:2])}")
        
        # Position-specific insights
        position = player_info['position']
        if position == 'QB' and gamebook_adj > 0.1:
            reasoning.append("Favorable passing matchup identified")
        elif position == 'RB' and gamebook_adj > 0.1:
            reasoning.append("Weak run defense creates opportunity")
        elif position in ['WR', 'TE'] and gamebook_adj > 0.1:
            reasoning.append("Secondary vulnerability detected")
        
        return reasoning or ["Standard projection based on available data"]
    
    def _create_final_recommendations(self, projections: List[EnhancedProjection]) -> List[Dict]:
        """Create final recommendations from enhanced projections."""
        recommendations = []
        
        for proj in projections:
            if proj.confidence > 0.75 and proj.final_projection > proj.base_projection * 1.08:
                recommendations.append({
                    'player': proj.player,
                    'position': proj.position,
                    'projection': round(proj.final_projection, 1),
                    'confidence': round(proj.confidence, 2),
                    'edge': round((proj.final_projection - proj.base_projection) / proj.base_projection, 3),
                    'reasoning': proj.reasoning[:2],  # Top 2 reasons
                    'recommendation': 'STRONG' if proj.confidence > 0.85 else 'MODERATE'
                })
        
        return recommendations
    
    def print_enhanced_results(self, results: Dict) -> None:
        """Print formatted enhanced results."""
        game_info = results['game_info']
        projections = results['enhanced_projections']
        recommendations = results['final_recommendations']
        
        print(f"\n🎯 ENHANCED PROJECTIONS: {game_info['away_team']} @ {game_info['home_team']}")
        print("=" * 65)
        
        # Top projections
        print("\n📊 TOP PROJECTIONS:")
        print("-" * 50)
        for i, proj in enumerate(projections[:10], 1):
            print(f"{i:2d}. {proj.player:<20} {proj.final_projection:5.1f} pts "
                  f"(Conf: {proj.confidence:.2f})")
        
        # High-confidence recommendations
        print(f"\n🎯 HIGH-CONFIDENCE RECOMMENDATIONS:")
        print("-" * 50)
        for rec in recommendations:
            print(f"• {rec['player']:<20} {rec['projection']:5.1f} pts "
                  f"({rec['edge']:+.1%} edge, {rec['confidence']:.2f} conf)")
            print(f"  Reasoning: {', '.join(rec['reasoning'])}")
        
        # Gamebook insights
        gamebook_analysis = results['gamebook_analysis']
        if 'key_advantages' in gamebook_analysis:
            print(f"\n🧠 GAMEBOOK INSIGHTS:")
            print("-" * 50)
            print(f"Overall Edge: {gamebook_analysis.get('overall_edge', 0):+.3f}")
            for advantage in gamebook_analysis['key_advantages']:
                print(f"• {advantage}")
        
        # Simple copy-paste format
        print(f"\n📋 COPY-PASTE FORMAT:")
        print("-" * 30)
        for proj in projections[:15]:
            print(f"{proj.player}, {proj.final_projection:.1f}")


def main():
    """Example usage of Enhanced Projection System."""
    
    # Example game
    game_info = {
        'away_team': 'Vikings',
        'home_team': 'Bears'
    }
    
    # Example base projections (would come from your models)
    base_projections = {
        'Vikings QB1': 22.5,
        'Bears QB1': 19.8,
        'Vikings RB1': 15.2,
        'Bears RB1': 13.7,
        'Vikings WR1': 14.8,
        'Bears WR1': 12.3
    }
    
    # Run enhanced system
    system = EnhancedProjectionSystem()  # No API key for demo
    results = system.run_enhanced_projections(game_info, base_projections)
    
    # Print results
    system.print_enhanced_results(results)


if __name__ == "__main__":
    main()
