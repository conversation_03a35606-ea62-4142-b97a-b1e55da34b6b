#!/usr/bin/env python3
"""
Team Profile Calculator - Recalculates rolling averages and ratings after each game
"""
import json
import os
from pathlib import Path
from typing import Dict, List, Any
import statistics
from datetime import datetime

class TeamProfileCalculator:
    def __init__(self):
        self.team_history_dir = Path("team_history")
        self.league_rankings_file = "league_rankings.json"
        
    def load_team_history(self, team_name: str) -> List[Dict]:
        """Load all games for a specific team"""
        team_file = self.team_history_dir / f"{team_name}.jsonl"
        if not team_file.exists():
            return []
        
        games = []
        with open(team_file, 'r') as f:
            for line in f:
                games.append(json.loads(line.strip()))
        return games
    
    def calculate_team_profile(self, team_name: str) -> Dict[str, Any]:
        """Calculate comprehensive team profile from game history"""
        games = self.load_team_history(team_name)
        if not games:
            return self._empty_profile(team_name)
        
        # Basic stats
        total_games = len(games)
        wins = sum(1 for g in games if g['result'] == 'W')
        win_pct = wins / total_games if total_games > 0 else 0
        
        # Offensive metrics
        avg_points_for = statistics.mean([g['score_for'] for g in games])
        avg_yards_per_play = statistics.mean([g['yards_per_play'] for g in games])
        avg_yards_per_rush = statistics.mean([g['yards_per_rush'] for g in games])
        avg_yards_per_pass = statistics.mean([g['yards_per_pass'] for g in games])
        avg_third_down_pct = statistics.mean([g['third_down_pct'] for g in games])
        avg_red_zone_pct = statistics.mean([g['red_zone_pct'] for g in games])
        avg_explosive_plays = statistics.mean([g['explosive_plays_15plus'] for g in games])
        
        # Defensive metrics (points allowed)
        avg_points_against = statistics.mean([g['score_against'] for g in games])
        
        # Discipline metrics
        avg_penalties = statistics.mean([g['penalties'] for g in games])
        avg_penalty_yards = statistics.mean([g['penalty_yards'] for g in games])
        avg_turnovers = statistics.mean([g['turnovers'] for g in games])
        avg_sacks_allowed = statistics.mean([g['sacks_allowed'] for g in games])
        
        # Situational metrics
        comeback_wins = sum(1 for g in games if g.get('comeback_win', False))
        blown_leads = sum(1 for g in games if g.get('blown_lead', False))
        
        # Calculate composite ratings (0-100 scale)
        offensive_rating = self._calculate_offensive_rating(
            avg_points_for, avg_yards_per_play, avg_third_down_pct, 
            avg_red_zone_pct, avg_explosive_plays
        )
        
        defensive_rating = self._calculate_defensive_rating(avg_points_against)
        
        discipline_rating = self._calculate_discipline_rating(
            avg_penalties, avg_penalty_yards, avg_turnovers, avg_sacks_allowed
        )
        
        situational_rating = self._calculate_situational_rating(
            comeback_wins, blown_leads, total_games, win_pct
        )
        
        # Overall rating
        overall_rating = (offensive_rating + defensive_rating + discipline_rating + situational_rating) / 4
        
        return {
            "team": team_name,
            "games_played": total_games,
            "record": f"{wins}-{total_games - wins}",
            "win_percentage": round(win_pct, 3),
            "offensive_rating": round(offensive_rating, 1),
            "defensive_rating": round(defensive_rating, 1),
            "discipline_rating": round(discipline_rating, 1),
            "situational_rating": round(situational_rating, 1),
            "overall_rating": round(overall_rating, 1),
            "averages": {
                "points_for": round(avg_points_for, 1),
                "points_against": round(avg_points_against, 1),
                "yards_per_play": round(avg_yards_per_play, 2),
                "yards_per_rush": round(avg_yards_per_rush, 2),
                "yards_per_pass": round(avg_yards_per_pass, 2),
                "third_down_pct": round(avg_third_down_pct, 1),
                "red_zone_pct": round(avg_red_zone_pct, 1),
                "explosive_plays": round(avg_explosive_plays, 1),
                "penalties": round(avg_penalties, 1),
                "penalty_yards": round(avg_penalty_yards, 1),
                "turnovers": round(avg_turnovers, 2),
                "sacks_allowed": round(avg_sacks_allowed, 1)
            },
            "situational": {
                "comeback_wins": comeback_wins,
                "blown_leads": blown_leads,
                "clutch_factor": round((comeback_wins - blown_leads) / total_games, 3)
            }
        }
    
    def _empty_profile(self, team_name: str) -> Dict[str, Any]:
        """Return empty profile for teams with no games"""
        return {
            "team": team_name,
            "games_played": 0,
            "record": "0-0",
            "win_percentage": 0.0,
            "offensive_rating": 50.0,
            "defensive_rating": 50.0,
            "discipline_rating": 50.0,
            "situational_rating": 50.0,
            "overall_rating": 50.0,
            "averages": {},
            "situational": {}
        }
    
    def _calculate_offensive_rating(self, ppg, ypp, third_down, red_zone, explosive):
        """Calculate offensive rating (0-100)"""
        # Normalize each metric to 0-100 scale based on NFL benchmarks
        ppg_score = min(100, max(0, (ppg - 10) * 2.5))  # 10-50 ppg range
        ypp_score = min(100, max(0, (ypp - 3.0) * 25))  # 3.0-7.0 ypp range
        third_score = min(100, max(0, third_down * 2.5))  # 0-40% range
        red_score = min(100, max(0, red_zone * 1.25))  # 0-80% range
        explosive_score = min(100, max(0, explosive * 10))  # 0-10 explosive plays
        
        return (ppg_score * 0.3 + ypp_score * 0.25 + third_score * 0.2 + 
                red_score * 0.15 + explosive_score * 0.1)
    
    def _calculate_defensive_rating(self, ppg_allowed):
        """Calculate defensive rating (0-100) - lower points allowed = higher rating"""
        # Invert scale: 10 ppg allowed = 100 rating, 40 ppg allowed = 0 rating
        return min(100, max(0, 100 - (ppg_allowed - 10) * 3.33))
    
    def _calculate_discipline_rating(self, penalties, penalty_yards, turnovers, sacks_allowed):
        """Calculate discipline rating (0-100) - fewer penalties/turnovers = higher rating"""
        penalty_score = min(100, max(0, 100 - penalties * 8))  # 0-12 penalties
        yards_score = min(100, max(0, 100 - penalty_yards * 0.8))  # 0-125 yards
        turnover_score = min(100, max(0, 100 - turnovers * 25))  # 0-4 turnovers
        sack_score = min(100, max(0, 100 - sacks_allowed * 20))  # 0-5 sacks
        
        return (penalty_score * 0.3 + yards_score * 0.3 + turnover_score * 0.25 + sack_score * 0.15)
    
    def _calculate_situational_rating(self, comeback_wins, blown_leads, total_games, win_pct):
        """Calculate situational/clutch rating (0-100)"""
        if total_games == 0:
            return 50.0
        
        clutch_factor = (comeback_wins - blown_leads) / total_games
        win_bonus = win_pct * 50  # 0-50 points for win percentage
        clutch_bonus = clutch_factor * 50 + 50  # -50 to +50 points for clutch factor
        
        return min(100, max(0, win_bonus + clutch_bonus))

def update_team_profiles():
    """Update profiles for all teams with game history"""
    calculator = TeamProfileCalculator()
    
    # Get all team files
    team_files = list(calculator.team_history_dir.glob("*.jsonl"))
    profiles = []
    
    for team_file in team_files:
        team_name = team_file.stem
        profile = calculator.calculate_team_profile(team_name)
        profiles.append(profile)
    
    # Sort by overall rating
    profiles.sort(key=lambda x: x['overall_rating'], reverse=True)
    
    # Add rankings
    for i, profile in enumerate(profiles):
        profile['rank'] = i + 1
    
    # Create league intelligence summary
    league_summary = {
        "last_updated": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "total_teams": len(profiles),
        "total_games_processed": sum(p['games_played'] for p in profiles) // 2,
        "league_averages": _calculate_league_averages(profiles),
        "top_10": profiles[:10],
        "bottom_10": profiles[-10:] if len(profiles) >= 10 else profiles,
        "power_rankings_insights": _generate_power_insights(profiles),
        "full_rankings": profiles
    }

    # Save league rankings
    with open(calculator.league_rankings_file, 'w') as f:
        json.dump(league_summary, f, indent=2)

    print(f"Updated profiles for {len(profiles)} teams")
    return profiles

def _calculate_league_averages(profiles: List[Dict]) -> Dict:
    """Calculate league-wide averages for context"""
    if not profiles:
        return {}

    total_games = sum(p['games_played'] for p in profiles if p['games_played'] > 0)
    if total_games == 0:
        return {}

    return {
        "points_per_game": round(sum(p['averages'].get('points_for', 0) * p['games_played']
                                   for p in profiles if p['games_played'] > 0) / total_games, 1),
        "yards_per_play": round(sum(p['averages'].get('yards_per_play', 0) * p['games_played']
                                  for p in profiles if p['games_played'] > 0) / total_games, 2),
        "third_down_pct": round(sum(p['averages'].get('third_down_pct', 0) * p['games_played']
                                  for p in profiles if p['games_played'] > 0) / total_games, 1),
        "penalties_per_game": round(sum(p['averages'].get('penalties', 0) * p['games_played']
                                      for p in profiles if p['games_played'] > 0) / total_games, 1)
    }

def _generate_power_insights(profiles: List[Dict]) -> Dict:
    """Generate key insights from power rankings"""
    if len(profiles) < 4:
        return {"note": "Need more teams for meaningful insights"}

    # Find elite teams (top 25%)
    elite_threshold = len(profiles) // 4
    elite_teams = profiles[:elite_threshold] if elite_threshold > 0 else profiles[:1]

    # Find struggling teams (bottom 25%)
    struggling_teams = profiles[-elite_threshold:] if elite_threshold > 0 else profiles[-1:]

    # Identify key differentiators
    elite_avg_rating = sum(t['overall_rating'] for t in elite_teams) / len(elite_teams)
    struggling_avg_rating = sum(t['overall_rating'] for t in struggling_teams) / len(struggling_teams)

    return {
        "elite_teams": [{"team": t['team'], "rating": t['overall_rating'],
                        "record": t['record']} for t in elite_teams],
        "struggling_teams": [{"team": t['team'], "rating": t['overall_rating'],
                             "record": t['record']} for t in struggling_teams],
        "rating_gap": round(elite_avg_rating - struggling_avg_rating, 1),
        "undefeated_teams": [t['team'] for t in profiles if t['win_percentage'] == 1.0 and t['games_played'] > 0],
        "winless_teams": [t['team'] for t in profiles if t['win_percentage'] == 0.0 and t['games_played'] > 0],
        "most_explosive": max(profiles, key=lambda x: x['averages'].get('explosive_plays', 0))['team'] if profiles else None,
        "most_disciplined": min(profiles, key=lambda x: x['averages'].get('penalties', 999))['team'] if profiles else None
    }

if __name__ == "__main__":
    profiles = update_team_profiles()
    for profile in profiles[:5]:  # Show top 5
        print(f"{profile['rank']}. {profile['team']} - {profile['overall_rating']} "
              f"({profile['record']}, {profile['win_percentage']:.1%})")
