#!/usr/bin/env python3
"""
Comprehensive Props Analysis for WAS @ GB
Maximizes API usage to get the most complete picture for projections.
"""

import os
import sys
import pandas as pd
from typing import Dict, Any, List
from dotenv import load_dotenv

sys.path.append('src')
from proj.fetch_odds import (
    get_totals_spreads, get_player_props, get_advanced_game_markets,
    find_game_event_id, process_player_props_response
)

load_dotenv()


class ComprehensivePropsAnalyzer:
    """Complete props and game context analysis for WAS @ GB."""
    
    def __init__(self):
        self.api_key = os.getenv('ODDS_API_KEY')
        self.requests_used = 0
        self.max_requests = 10  # Conservative limit for this analysis
        
    def analyze_current_coverage(self) -> Dict[str, Any]:
        """Analyze what we already have vs. what we need."""
        try:
            props_df = pd.read_csv('csvs/was_gb_player_props_20250911_2151.csv')
        except:
            return {'status': 'no_existing_data'}
        
        analysis = {
            'total_props': len(props_df),
            'unique_players': len(props_df['player_name'].unique()),
            'markets_covered': sorted(props_df['market'].unique()),
            'bookmakers': sorted(props_df['bookmaker'].unique()),
        }
        
        # Check coverage by position
        dk_df = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-2_players.csv', skiprows=1)
        key_players = dk_df[dk_df['Pos'].isin(['QB', 'RB', 'WR', 'TE'])]['Player'].tolist()
        
        covered_players = []
        missing_players = []
        
        for player in key_players:
            if any(player.lower() in name.lower() for name in props_df['player_name'].unique()):
                covered_players.append(player)
            else:
                missing_players.append(player)
        
        analysis.update({
            'covered_players': covered_players,
            'missing_players': missing_players,
            'coverage_rate': len(covered_players) / len(key_players) if key_players else 0
        })
        
        # Identify gaps
        gaps = self.identify_gaps(props_df, dk_df)
        analysis['gaps'] = gaps
        
        return analysis
    
    def identify_gaps(self, props_df: pd.DataFrame, dk_df: pd.DataFrame) -> Dict[str, List]:
        """Identify specific gaps in our current data."""
        gaps = {
            'missing_markets': [],
            'missing_alternates': [],
            'missing_defense': [],
            'missing_kickers': []
        }
        
        # Check for missing key markets
        current_markets = set(props_df['market'].unique())
        desired_markets = {
            'player_anytime_td', 'player_1st_td', 'player_last_td',
            'player_field_goals', 'player_pats',
            'player_pass_longest_completion', 'player_reception_longest',
            'player_rush_longest', 'player_defensive_interceptions'
        }
        
        gaps['missing_markets'] = list(desired_markets - current_markets)
        
        # Check for missing alternate lines
        alt_markets = [m for m in current_markets if 'alternate' in m]
        if not alt_markets:
            gaps['missing_alternates'] = ['No alternate lines found']
        
        # Check defense coverage
        defense_players = dk_df[dk_df['Pos'] == 'DST']['Player'].tolist()
        if not any('DST' in str(name) or 'Defense' in str(name) for name in props_df['player_name'].unique()):
            gaps['missing_defense'] = ['Team defense props missing']
        
        return gaps
    
    def should_fetch_additional_data(self, analysis: Dict[str, Any]) -> Dict[str, bool]:
        """Determine what additional data would be valuable."""
        recommendations = {
            'fetch_alternates': False,
            'fetch_advanced_markets': False,
            'fetch_missing_props': False,
            'estimated_requests': 0
        }
        
        # If coverage is low, recommend fetching missing props
        if analysis.get('coverage_rate', 0) < 0.8:
            recommendations['fetch_missing_props'] = True
            recommendations['estimated_requests'] += 1
        
        # If no alternate lines, might be worth trying
        if not any('alternate' in market for market in analysis.get('markets_covered', [])):
            recommendations['fetch_alternates'] = True
            recommendations['estimated_requests'] += 1
        
        # Advanced markets for game script (low priority)
        if recommendations['estimated_requests'] < 3:
            recommendations['fetch_advanced_markets'] = True
            recommendations['estimated_requests'] += 1
        
        return recommendations
    
    def create_defense_projections(self, props_df: pd.DataFrame) -> Dict[str, float]:
        """Create defense projections from individual defensive player props."""
        defense_props = props_df[props_df['market'].isin(['player_sacks', 'player_tackles_assists', 'player_defensive_interceptions'])]
        
        team_defense = {'WAS': {}, 'GB': {}}
        
        # Map players to teams (simplified)
        was_defenders = ['Bobby Wagner', 'Frankie Luvu', 'Mike Sainristil', 'Xavier McKinney', 'Marshon Lattimore']
        gb_defenders = ['Quay Walker', 'Rashan Gary', 'Keisean Nixon', 'Carrington Valentine', 'Edgerrin Cooper']
        
        for _, prop in defense_props.iterrows():
            player = prop['player_name']
            market = prop['market']
            line = prop['line']
            
            # Determine team
            team = None
            if any(defender in player for defender in was_defenders):
                team = 'WAS'
            elif any(defender in player for defender in gb_defenders):
                team = 'GB'
            
            if team:
                if market not in team_defense[team]:
                    team_defense[team][market] = 0
                team_defense[team][market] += line
        
        # Convert to fantasy points (simplified)
        fantasy_projections = {}
        for team, stats in team_defense.items():
            points = 0
            points += stats.get('player_sacks', 0) * 1.0  # 1 pt per sack
            points += stats.get('player_defensive_interceptions', 0) * 2.0  # 2 pts per INT
            points += stats.get('player_tackles_assists', 0) * 0.5  # 0.5 pts per tackle
            
            # Add base points for allowing points (inverse of team total)
            if team == 'WAS':
                opp_total = 26.25  # GB implied total
            else:
                opp_total = 22.75  # WAS implied total
            
            # Defense scoring based on points allowed
            if opp_total <= 6:
                points += 10
            elif opp_total <= 13:
                points += 7
            elif opp_total <= 20:
                points += 4
            elif opp_total <= 27:
                points += 1
            elif opp_total <= 34:
                points += 0
            else:
                points -= 1
            
            fantasy_projections[f"{team} DST"] = round(points, 2)
        
        return fantasy_projections
    
    def run_comprehensive_analysis(self) -> Dict[str, Any]:
        """Run complete analysis and provide recommendations."""
        print("=== COMPREHENSIVE PROPS ANALYSIS ===")
        
        # Step 1: Analyze current coverage
        print("📊 Analyzing current props coverage...")
        coverage = self.analyze_current_coverage()
        
        if coverage.get('status') == 'no_existing_data':
            print("❌ No existing props data found")
            return {'status': 'no_data'}
        
        print(f"✅ Current Coverage:")
        print(f"   Total Props: {coverage['total_props']}")
        print(f"   Players: {coverage['unique_players']}")
        print(f"   Markets: {len(coverage['markets_covered'])}")
        print(f"   Coverage Rate: {coverage['coverage_rate']:.1%}")
        
        if coverage['missing_players']:
            print(f"   Missing Players: {', '.join(coverage['missing_players'][:5])}")
        
        # Step 2: Get recommendations
        recommendations = self.should_fetch_additional_data(coverage)
        
        print(f"\n🎯 RECOMMENDATIONS:")
        print(f"   Estimated API Requests Needed: {recommendations['estimated_requests']}")
        print(f"   Fetch Missing Props: {recommendations['fetch_missing_props']}")
        print(f"   Fetch Alternates: {recommendations['fetch_alternates']}")
        print(f"   Fetch Advanced Markets: {recommendations['fetch_advanced_markets']}")
        
        # Step 3: Create defense projections from existing data
        try:
            props_df = pd.read_csv('csvs/was_gb_player_props_20250911_2151.csv')
            defense_proj = self.create_defense_projections(props_df)
            print(f"\n🛡️  DEFENSE PROJECTIONS:")
            for team, proj in defense_proj.items():
                print(f"   {team}: {proj} points")
        except Exception as e:
            print(f"❌ Could not create defense projections: {e}")
            defense_proj = {}
        
        # Step 4: Final assessment
        assessment = self.assess_data_quality(coverage)
        
        return {
            'coverage': coverage,
            'recommendations': recommendations,
            'defense_projections': defense_proj,
            'assessment': assessment
        }
    
    def assess_data_quality(self, coverage: Dict[str, Any]) -> Dict[str, Any]:
        """Assess if current data is sufficient for elite projections."""
        score = 0
        max_score = 100
        
        # Coverage rate (40 points)
        score += coverage.get('coverage_rate', 0) * 40
        
        # Market diversity (30 points)
        markets = coverage.get('markets_covered', [])
        key_markets = ['player_pass_yds', 'player_rush_yds', 'player_reception_yds', 
                      'player_receptions', 'player_pass_tds', 'player_rush_tds']
        market_score = sum(1 for market in key_markets if market in markets) / len(key_markets)
        score += market_score * 30
        
        # Multiple bookmakers (20 points)
        books = coverage.get('bookmakers', [])
        book_score = min(len(books) / 3, 1.0)  # Max at 3 books
        score += book_score * 20
        
        # Defense coverage (10 points)
        if 'player_sacks' in markets and 'player_tackles_assists' in markets:
            score += 10
        
        quality = 'excellent' if score >= 85 else 'good' if score >= 70 else 'fair' if score >= 50 else 'poor'
        
        return {
            'score': round(score, 1),
            'quality': quality,
            'sufficient_for_projections': score >= 70,
            'recommendation': self.get_quality_recommendation(score)
        }
    
    def get_quality_recommendation(self, score: float) -> str:
        """Get recommendation based on data quality score."""
        if score >= 85:
            return "Excellent data quality. Proceed with elite projections."
        elif score >= 70:
            return "Good data quality. Projections will be reliable."
        elif score >= 50:
            return "Fair data quality. Consider fetching additional props for key missing players."
        else:
            return "Poor data quality. Recommend manual props gathering or additional API calls."


def main():
    """Main execution."""
    analyzer = ComprehensivePropsAnalyzer()
    results = analyzer.run_comprehensive_analysis()
    
    if results.get('status') == 'no_data':
        print("Run the props fetcher first: python was_gb_props_fetcher.py")
        return
    
    assessment = results.get('assessment', {})
    print(f"\n📈 FINAL ASSESSMENT:")
    print(f"   Data Quality Score: {assessment.get('score', 0)}/100 ({assessment.get('quality', 'unknown')})")
    print(f"   Sufficient for Projections: {assessment.get('sufficient_for_projections', False)}")
    print(f"   Recommendation: {assessment.get('recommendation', 'Unknown')}")
    
    return results


if __name__ == "__main__":
    main()
