#!/usr/bin/env python3
"""
🧠 MASTER NFL PROJECTION SYSTEM
Two-Layer Pipeline: Gamebook Intelligence + Market-Aware Fantasy Projections
"""

import json
import pandas as pd
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
import numpy as np

from gamebook_intelligence import GamebookIntelligence
from enhanced_projection_system import EnhancedProjectionSystem


@dataclass
class MasterProjection:
    """Master projection with full intelligence layers."""
    player: str
    position: str
    team: str
    opponent: str
    base_projection: float
    gamebook_adjustment: float
    market_adjustment: float
    final_projection: float
    confidence: float
    reasoning: List[str]
    team_context: Dict[str, Any]
    matchup_edge: float


class MasterProjectionSystem:
    """🧠 Master NFL Projection System - Two-Layer Intelligence Pipeline"""
    
    def __init__(self, odds_api_key: str = None):
        self.odds_api_key = odds_api_key
        
        # Layer 1: Gamebook Intelligence Engine
        self.gamebook_intelligence = GamebookIntelligence()
        self.gamebook_loaded = False
        
        # Layer 2: Market-Aware Projection Engine
        self.enhanced_system = EnhancedProjectionSystem(odds_api_key)
        
        # Master analysis results
        self.team_rankings = {}
        self.matchup_matrix = {}
        self.league_context = {}
    
    def initialize_intelligence_layers(self) -> None:
        """🏗️ Initialize both intelligence layers."""
        print("🧠 INITIALIZING MASTER PROJECTION SYSTEM")
        print("=" * 50)
        
        # Layer 1: Load Gamebook Intelligence
        if not self.gamebook_loaded:
            print("🏗️ LAYER 1 — GAMEBOOK INTELLIGENCE ENGINE")
            print("-" * 40)
            self.gamebook_intelligence.run_full_analysis()
            self.team_rankings = self.gamebook_intelligence.get_team_rankings()
            self.matchup_matrix = self.gamebook_intelligence.matchup_matrix
            self.gamebook_loaded = True
            print("✅ Gamebook Intelligence Layer Ready")
        
        # Layer 2: Initialize Enhanced System
        print("\n⚡ LAYER 2 — MARKET-AWARE PROJECTION ENGINE")
        print("-" * 40)
        self.enhanced_system.load_gamebook_intelligence()
        print("✅ Market-Aware Projection Layer Ready")
        
        print(f"\n🎯 MASTER SYSTEM READY")
        print(f"   📊 Teams Analyzed: {len(self.gamebook_intelligence.team_profiles)}")
        print(f"   ⚔️ Matchups Created: {len(self.matchup_matrix)}")
        print(f"   🏆 Rankings Generated: {len(self.team_rankings)} categories")
    
    def run_master_analysis(self, away_team: str, home_team: str, 
                           base_projections: Dict[str, float] = None) -> Dict[str, Any]:
        """🚀 Run complete master analysis pipeline."""
        
        # Ensure intelligence layers are loaded
        self.initialize_intelligence_layers()
        
        print(f"\n🎯 MASTER ANALYSIS: {away_team} @ {home_team}")
        print("=" * 55)
        
        # Game info
        game_info = {
            'away_team': away_team,
            'home_team': home_team
        }
        
        # Step 1: Deep Gamebook Analysis
        print("🧠 LAYER 1 — DEEP GAMEBOOK ANALYSIS")
        gamebook_analysis = self._run_deep_gamebook_analysis(away_team, home_team)
        
        # Step 2: Market-Aware Projections
        print("⚡ LAYER 2 — MARKET-AWARE PROJECTIONS")
        enhanced_results = self.enhanced_system.run_enhanced_projections(
            game_info, base_projections or {}
        )
        
        # Step 3: Master Integration
        print("🎯 MASTER INTEGRATION")
        master_projections = self._create_master_projections(
            enhanced_results, gamebook_analysis
        )
        
        # Step 4: Final Analysis Package
        master_results = {
            'game_info': game_info,
            'gamebook_analysis': gamebook_analysis,
            'enhanced_results': enhanced_results,
            'master_projections': master_projections,
            'team_rankings': self.team_rankings,
            'league_context': self._create_league_context(away_team, home_team),
            'final_recommendations': self._create_master_recommendations(master_projections)
        }
        
        return master_results
    
    def _run_deep_gamebook_analysis(self, away_team: str, home_team: str) -> Dict[str, Any]:
        """🧠 Run deep gamebook intelligence analysis."""
        
        # Get team profiles
        away_profile = self.gamebook_intelligence.team_profiles.get(away_team)
        home_profile = self.gamebook_intelligence.team_profiles.get(home_team)
        
        # Get matchup analysis
        matchup_analysis = self.gamebook_intelligence.get_matchup_analysis(away_team, home_team)
        
        # Enhanced team analysis
        away_analysis = self._analyze_team_deeply(away_team, away_profile)
        home_analysis = self._analyze_team_deeply(home_team, home_profile)
        
        # Identify key matchup factors
        key_factors = self._identify_key_matchup_factors(
            away_analysis, home_analysis, matchup_analysis
        )
        
        return {
            'matchup_edge': matchup_analysis.get('overall_edge', 0),
            'away_team_analysis': away_analysis,
            'home_team_analysis': home_analysis,
            'key_matchup_factors': key_factors,
            'projection_adjustments': matchup_analysis.get('projection_adjustments', {}),
            'confidence_factors': matchup_analysis.get('confidence_factors', {})
        }
    
    def _analyze_team_deeply(self, team: str, profile: Any) -> Dict[str, Any]:
        """Analyze team with deep intelligence."""
        if not profile:
            return {'error': f'No profile found for {team}'}
        
        # Get team ranking positions
        team_rankings = {}
        for category, rankings in self.team_rankings.items():
            for i, (ranked_team, score) in enumerate(rankings, 1):
                if ranked_team == team:
                    team_rankings[category] = {'rank': i, 'score': score}
                    break
        
        # Identify strengths and weaknesses
        strengths = []
        weaknesses = []
        
        # Offensive analysis
        if profile.off_explosive_play_rate > 0.15:
            strengths.append("Elite explosive offense")
        elif profile.off_explosive_play_rate < 0.08:
            weaknesses.append("Lacks explosive plays")
        
        if profile.off_success_rate > 0.55:
            strengths.append("Highly consistent offense")
        elif profile.off_success_rate < 0.45:
            weaknesses.append("Inconsistent offense")
        
        if profile.off_third_down_rate > 0.45:
            strengths.append("Excellent third down offense")
        elif profile.off_third_down_rate < 0.35:
            weaknesses.append("Poor third down conversions")
        
        # Defensive analysis
        if profile.def_explosive_allowed < 0.08:
            strengths.append("Elite big-play defense")
        elif profile.def_explosive_allowed > 0.15:
            weaknesses.append("Vulnerable to explosive plays")
        
        if profile.def_success_rate_allowed < 0.40:
            strengths.append("Stingy defense")
        elif profile.def_success_rate_allowed > 0.55:
            weaknesses.append("Allows too much success")
        
        return {
            'team': team,
            'games_played': profile.games_played,
            'rankings': team_rankings,
            'offensive_rating': (profile.off_success_rate + profile.off_explosive_play_rate + 
                                profile.off_epa_per_play) / 3,
            'defensive_rating': 1 - (profile.def_success_rate_allowed + profile.def_explosive_allowed + 
                                   abs(profile.def_epa_allowed)) / 3,
            'strengths': strengths,
            'weaknesses': weaknesses,
            'key_metrics': {
                'explosive_play_rate': profile.off_explosive_play_rate,
                'success_rate': profile.off_success_rate,
                'third_down_rate': profile.off_third_down_rate,
                'explosive_allowed': profile.def_explosive_allowed,
                'success_allowed': profile.def_success_rate_allowed
            }
        }
    
    def _identify_key_matchup_factors(self, away_analysis: Dict, home_analysis: Dict, 
                                    matchup_analysis: Dict) -> List[str]:
        """Identify key factors that will determine the game."""
        factors = []
        
        # Offensive vs Defensive matchups
        away_off_rating = away_analysis.get('offensive_rating', 0.5)
        home_def_rating = home_analysis.get('defensive_rating', 0.5)
        
        if away_off_rating > 0.6 and home_def_rating < 0.4:
            factors.append(f"{away_analysis['team']} elite offense vs weak {home_analysis['team']} defense")
        elif away_off_rating < 0.4 and home_def_rating > 0.6:
            factors.append(f"{home_analysis['team']} elite defense vs weak {away_analysis['team']} offense")
        
        # Specific strength vs weakness matchups
        away_strengths = away_analysis.get('strengths', [])
        home_weaknesses = home_analysis.get('weaknesses', [])
        
        for strength in away_strengths:
            for weakness in home_weaknesses:
                if 'explosive' in strength.lower() and 'explosive' in weakness.lower():
                    factors.append(f"Explosive offense vs vulnerable defense")
                elif 'third down' in strength.lower() and 'third down' in weakness.lower():
                    factors.append(f"Third down efficiency advantage")
        
        # Overall matchup edge
        edge = matchup_analysis.get('overall_edge', 0)
        if abs(edge) > 0.2:
            favored_team = away_analysis['team'] if edge > 0 else home_analysis['team']
            factors.append(f"Significant matchup advantage: {favored_team}")
        
        return factors[:5]  # Top 5 factors
    
    def _create_master_projections(self, enhanced_results: Dict, 
                                 gamebook_analysis: Dict) -> List[MasterProjection]:
        """Create master projections combining all intelligence."""
        master_projections = []
        
        enhanced_projections = enhanced_results.get('enhanced_projections', [])
        matchup_edge = gamebook_analysis.get('matchup_edge', 0)
        
        for proj in enhanced_projections:
            # Enhanced reasoning with gamebook context
            reasoning = list(proj.reasoning)
            
            # Add gamebook insights
            key_factors = gamebook_analysis.get('key_matchup_factors', [])
            if key_factors:
                reasoning.append(f"Key factor: {key_factors[0]}")
            
            # Team context
            team_context = {}
            if proj.team in [gamebook_analysis.get('away_team_analysis', {}).get('team'),
                           gamebook_analysis.get('home_team_analysis', {}).get('team')]:
                team_analysis = (gamebook_analysis.get('away_team_analysis', {}) 
                               if proj.team == gamebook_analysis.get('away_team_analysis', {}).get('team')
                               else gamebook_analysis.get('home_team_analysis', {}))
                team_context = {
                    'offensive_rating': team_analysis.get('offensive_rating', 0.5),
                    'defensive_rating': team_analysis.get('defensive_rating', 0.5),
                    'strengths': team_analysis.get('strengths', []),
                    'weaknesses': team_analysis.get('weaknesses', [])
                }
            
            master_proj = MasterProjection(
                player=proj.player,
                position=proj.position,
                team=proj.team,
                opponent=proj.opponent,
                base_projection=proj.base_projection,
                gamebook_adjustment=proj.gamebook_adjustment,
                market_adjustment=proj.market_adjustment,
                final_projection=proj.final_projection,
                confidence=proj.confidence,
                reasoning=reasoning,
                team_context=team_context,
                matchup_edge=matchup_edge
            )
            
            master_projections.append(master_proj)
        
        return master_projections
    
    def _create_league_context(self, away_team: str, home_team: str) -> Dict[str, Any]:
        """Create league context for the matchup."""
        context = {
            'matchup_teams': [away_team, home_team],
            'team_rankings': {}
        }
        
        # Get rankings for both teams
        for category, rankings in self.team_rankings.items():
            context['team_rankings'][category] = []
            for i, (team, score) in enumerate(rankings, 1):
                if team in [away_team, home_team]:
                    context['team_rankings'][category].append({
                        'team': team,
                        'rank': i,
                        'score': score,
                        'percentile': (32 - i) / 32  # Convert rank to percentile
                    })
        
        return context
    
    def _create_master_recommendations(self, projections: List[MasterProjection]) -> List[Dict]:
        """Create master recommendations."""
        recommendations = []
        
        for proj in projections:
            if proj.confidence > 0.75 and proj.final_projection > proj.base_projection * 1.08:
                edge = (proj.final_projection - proj.base_projection) / proj.base_projection
                
                recommendations.append({
                    'player': proj.player,
                    'position': proj.position,
                    'team': proj.team,
                    'projection': round(proj.final_projection, 1),
                    'confidence': round(proj.confidence, 2),
                    'edge': round(edge, 3),
                    'matchup_edge': round(proj.matchup_edge, 3),
                    'reasoning': proj.reasoning[:2],
                    'team_context': proj.team_context,
                    'recommendation': 'ELITE' if proj.confidence > 0.85 and edge > 0.15 else 'STRONG'
                })
        
        # Sort by confidence * edge
        recommendations.sort(key=lambda x: x['confidence'] * abs(x['edge']), reverse=True)
        
        return recommendations
    
    def print_master_results(self, results: Dict) -> None:
        """Print comprehensive master results."""
        game_info = results['game_info']
        gamebook_analysis = results['gamebook_analysis']
        master_projections = results['master_projections']
        recommendations = results['final_recommendations']
        league_context = results['league_context']
        
        away_team = game_info['away_team']
        home_team = game_info['home_team']
        
        print(f"\n🧠 MASTER ANALYSIS: {away_team} @ {home_team}")
        print("=" * 65)
        
        # Gamebook Intelligence Summary
        print(f"\n🏗️ LAYER 1 — GAMEBOOK INTELLIGENCE")
        print("-" * 40)
        print(f"Overall Matchup Edge: {gamebook_analysis.get('matchup_edge', 0):+.3f}")
        
        key_factors = gamebook_analysis.get('key_matchup_factors', [])
        if key_factors:
            print("Key Matchup Factors:")
            for factor in key_factors:
                print(f"  • {factor}")
        
        # Team Analysis
        away_analysis = gamebook_analysis.get('away_team_analysis', {})
        home_analysis = gamebook_analysis.get('home_team_analysis', {})
        
        for team_analysis in [away_analysis, home_analysis]:
            if 'team' in team_analysis:
                team = team_analysis['team']
                print(f"\n{team} Profile:")
                print(f"  Offensive Rating: {team_analysis.get('offensive_rating', 0):.3f}")
                print(f"  Defensive Rating: {team_analysis.get('defensive_rating', 0):.3f}")
                
                strengths = team_analysis.get('strengths', [])
                if strengths:
                    print(f"  Strengths: {', '.join(strengths[:2])}")
                
                weaknesses = team_analysis.get('weaknesses', [])
                if weaknesses:
                    print(f"  Weaknesses: {', '.join(weaknesses[:2])}")
        
        # Master Projections
        print(f"\n⚡ LAYER 2 — MASTER PROJECTIONS")
        print("-" * 40)
        
        print("TOP PROJECTIONS:")
        for i, proj in enumerate(master_projections[:10], 1):
            print(f"{i:2d}. {proj.player:<20} {proj.final_projection:5.1f} pts "
                  f"(Conf: {proj.confidence:.2f})")
        
        # Elite Recommendations
        if recommendations:
            print(f"\n🎯 MASTER RECOMMENDATIONS:")
            print("-" * 40)
            for rec in recommendations[:5]:
                print(f"• {rec['player']:<20} {rec['projection']:5.1f} pts "
                      f"({rec['edge']:+.1%} edge, {rec['confidence']:.2f} conf)")
                print(f"  {rec['recommendation']}: {', '.join(rec['reasoning'])}")
        
        # League Context
        print(f"\n🏆 LEAGUE CONTEXT:")
        print("-" * 25)
        
        for category, team_ranks in league_context['team_rankings'].items():
            if team_ranks:
                print(f"\n{category.replace('_', ' ').title()}:")
                for team_rank in team_ranks:
                    percentile = int(team_rank['percentile'] * 100)
                    print(f"  {team_rank['team']}: #{team_rank['rank']} ({percentile}th percentile)")
        
        # Copy-Paste Format
        print(f"\n📋 COPY-PASTE FORMAT:")
        print("-" * 30)
        for proj in master_projections[:15]:
            print(f"{proj.player}, {proj.final_projection:.1f}")


def main():
    """Example usage of Master Projection System."""
    
    # Initialize master system
    system = MasterProjectionSystem()  # Add odds_api_key if available
    
    # Example game
    away_team = "Chiefs"
    home_team = "Bills"
    
    # Example base projections
    base_projections = {
        'Patrick Mahomes': 22.5,
        'Josh Allen': 21.8,
        'Travis Kelce': 12.4,
        'Stefon Diggs': 13.7,
        'Isiah Pacheco': 11.2,
        'James Cook': 10.8
    }
    
    # Run master analysis
    results = system.run_master_analysis(away_team, home_team, base_projections)
    
    # Print comprehensive results
    system.print_master_results(results)


if __name__ == "__main__":
    main()
