#!/usr/bin/env python3
"""
Week 2 Focus Report - Packers, Commanders, Vikings, Bears
"""

import pandas as pd
import json
from pathlib import Path

def load_team_findings(team_name):
    """Load team findings JSON"""
    findings_dir = Path('findings')
    
    # Find the latest week directory
    week_dirs = [d for d in findings_dir.glob('asof_week_*') if d.is_dir()]
    if not week_dirs:
        return None
    
    latest_week_dir = max(week_dirs, key=lambda x: int(x.name.split('_')[-1]))
    
    # Try to find team file (handle name variations)
    team_files = list(latest_week_dir.glob(f'team_{team_name}*.json'))
    if not team_files:
        # Try alternative names
        alt_names = {
            'Packers': ['Green Bay Packers', 'GB'],
            'Commanders': ['Washington Commanders', 'WAS'],
            'Vikings': ['Minnesota Vikings', 'MIN'],
            'Bears': ['Chicago Bears', 'CHI']
        }
        
        if team_name in alt_names:
            for alt_name in alt_names[team_name]:
                team_files = list(latest_week_dir.glob(f'team_{alt_name}*.json'))
                if team_files:
                    break
    
    if team_files:
        with open(team_files[0], 'r') as f:
            return json.load(f)
    
    return None

def get_team_data_from_parquet(team_name):
    """Get team data from parquet files"""
    try:
        df_ranks = pd.read_parquet('models/team_ranks.parquet')
        df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
        
        # Find team (handle name variations)
        team_row = df_ranks[df_ranks['team'].str.contains(team_name, case=False, na=False)]
        if team_row.empty:
            return None, None
        
        team_row = team_row.iloc[0]
        
        hl_row = df_holes_levers[df_holes_levers['team'].str.contains(team_name, case=False, na=False)]
        if not hl_row.empty:
            hl_row = hl_row.iloc[0]
        else:
            hl_row = None
            
        return team_row, hl_row
        
    except Exception as e:
        print(f"Error loading data for {team_name}: {e}")
        return None, None

def analyze_team(team_name):
    """Analyze a specific team"""
    print(f"\n🏈 {team_name.upper()} ANALYSIS")
    print("=" * 50)
    
    team_row, hl_row = get_team_data_from_parquet(team_name)
    
    if team_row is None:
        print(f"❌ No data found for {team_name}")
        return
    
    actual_team_name = team_row['team']
    games_played = team_row['games_played']
    
    print(f"📊 Team: {actual_team_name}")
    print(f"🎮 Games Played: {games_played}")
    
    # Offensive Rankings
    print(f"\n🚀 OFFENSIVE RANKINGS:")
    off_metrics = {
        'Points Per Drive': ('OFF_ppd_rating_rank', 'OFF_ppd_rating_z'),
        'Pass Efficiency': ('OFF_nyd_rating_rank', 'OFF_nyd_rating_z'),
        'Rush Efficiency': ('OFF_rush_ypa_rating_rank', 'OFF_rush_ypa_rating_z'),
        'Explosive Plays': ('OFF_explosive_rate_rating_rank', 'OFF_explosive_rate_rating_z'),
        'Red Zone': ('OFF_rz_td_pct_rating_rank', 'OFF_rz_td_pct_rating_z')
    }
    
    for metric_name, (rank_col, z_col) in off_metrics.items():
        rank = team_row.get(rank_col, 99)
        z_score = team_row.get(z_col, 0)
        if rank != 99:
            print(f"   • {metric_name:15s}: Rank {int(rank):2d} (Z: {z_score:+.2f})")
    
    # Defensive Rankings
    print(f"\n🛡️  DEFENSIVE RANKINGS:")
    def_metrics = {
        'Points Per Drive': ('DEF_ppd_allowed_rating_rank', 'DEF_ppd_allowed_rating_z'),
        'Pass Defense': ('DEF_nyd_allowed_rating_rank', 'DEF_nyd_allowed_rating_z'),
        'Rush Defense': ('DEF_rush_ypa_allowed_rating_rank', 'DEF_rush_ypa_allowed_rating_z'),
        'Explosive Defense': ('DEF_explosive_rate_allowed_rating_rank', 'DEF_explosive_rate_allowed_rating_z')
    }
    
    for metric_name, (rank_col, z_col) in def_metrics.items():
        rank = team_row.get(rank_col, 99)
        z_score = team_row.get(z_col, 0)
        if rank != 99:
            # For defense, lower allowed is better, so invert z-score for display
            display_z = -z_score if 'allowed' in z_col else z_score
            print(f"   • {metric_name:15s}: Rank {int(rank):2d} (Z: {display_z:+.2f})")
    
    # Exploitables and Strengths
    if hl_row is not None:
        print(f"\n🎯 EXPLOITABLE AREAS (Holes ≥ +0.7σ):")
        hole_cols = [col for col in hl_row.index if col.startswith('hole_')]
        exploitables = []
        
        for hole_col in hole_cols:
            hole_value = hl_row.get(hole_col, 0)
            if hole_value >= 0.7:
                hole_name = hole_col.replace('hole_', '').replace('_', ' ').title()
                exploitables.append((hole_name, hole_value))
        
        if exploitables:
            for hole_name, hole_value in sorted(exploitables, key=lambda x: x[1], reverse=True):
                print(f"   🔴 {hole_name:20s}: +{hole_value:.2f}σ")
        else:
            print("   ✅ No major exploitable areas")
        
        print(f"\n🚀 OFFENSIVE STRENGTHS (Levers ≥ +0.5σ):")
        lever_cols = [col for col in hl_row.index if col.startswith('lever_')]
        strengths = []
        
        for lever_col in lever_cols:
            lever_value = hl_row.get(lever_col, 0)
            if lever_value >= 0.5:
                lever_name = lever_col.replace('lever_', '').replace('_', ' ').title()
                strengths.append((lever_name, lever_value))
        
        if strengths:
            for lever_name, lever_value in sorted(strengths, key=lambda x: x[1], reverse=True):
                print(f"   🟢 {lever_name:20s}: +{lever_value:.2f}σ")
        else:
            print("   ⚪ No major offensive strengths")

def main():
    """Generate Week 2 focus report"""
    
    print("🏈 WEEK 2 FOCUS REPORT")
    print("=" * 60)
    print("📅 Packers vs Lions, Vikings vs Bears")
    print("🎯 Elite-level prop reading analysis")
    
    week2_teams = ['Packers', 'Lions', 'Vikings', 'Bears']
    
    for team in week2_teams:
        analyze_team(team)
    
    print("\n\n🎯 KEY MATCHUP INSIGHTS:")
    print("=" * 40)
    
    print("\n📊 PACKERS vs LIONS:")
    print("   • Packers have elite pass protection (+1.42σ)")
    print("   • Lions have major pass rush holes (+1.42σ pressure hole)")
    print("   • Lions pass defense exploitable (+1.38σ pass efficiency hole)")
    print("   • Packers explosive pass offense (+1.11σ)")
    
    print("\n📊 VIKINGS vs BEARS:")
    print("   • Bears have elite red zone offense (+1.39σ)")
    print("   • Vikings have penalty discipline issues (+1.53σ penalty hole)")
    print("   • Both teams have red zone defensive holes (+1.39σ)")
    print("   • Vikings explosive plays potential (+0.40σ)")
    
    print("\n\n💡 PROP BETTING ANGLES:")
    print("=" * 30)
    print("🎯 Target Packers passing props vs Lions weak pass defense")
    print("🎯 Target Bears red zone TDs vs Vikings red zone defense")
    print("🎯 Avoid Vikings penalty props (discipline issues)")
    print("🎯 Consider Packers explosive play props vs Lions")

if __name__ == "__main__":
    main()
