#!/usr/bin/env python3
"""
UNM @ UCLA - Props-only player projections using main + alternate lines and anytime TD.
- Reads data/unm_ucla_props_raw.json (from The Odds API)
- Fits distributions per player/market via probit (normal CDF) using multiple points across books/alternates
- Converts implied means to DraftKings fantasy points (strictly props-based; no props => 0 points)
- Saves:
  * data/unm_ucla_prop_based_projections.csv (per-player total FP)
  * data/unm_ucla_prop_based_breakdown.csv (per-player stat means and components)
"""

import json
import math
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional
from scipy.stats import norm

RAW_PATH = Path('data/unm_ucla_props_raw.json')
OUT_SUM_PATH = Path('data/unm_ucla_prop_based_projections.csv')
OUT_BREAK_PATH = Path('data/unm_ucla_prop_based_breakdown.csv')

# Book weights (sharper lines weighted higher)
BOOK_WEIGHTS = {
    'Pinnacle': 1.0,
    'BetOnline.ag': 0.9,
    'Bovada': 0.8,
    'FanDuel': 0.7,
    'DraftKings': 0.7,
    'BetMGM': 0.6,
}

# Scoring weights (DK-style PPR)
SCORING = {
    'pass_yards': 0.04,
    'pass_tds': 4.0,
    'pass_ints': -1.0,
    'rush_yards': 0.10,
    'rush_tds': 6.0,
    'rec_yards': 0.10,
    'receptions': 1.0,
    'rec_tds': 6.0,
    'kicking_points': 1.0,  # This market is already in points
}

# Default sigmas if we only have a single point (rare with alternates, but possible)
SIGMA_PRIORS = {
    'player_pass_yds': 35.0,
    'player_rush_yds': 25.0,
    'player_reception_yds': 20.0,
    'player_receptions': 2.5,
    'player_pass_tds': 0.8,
    'player_rush_tds': 0.6,
    'player_reception_tds': 0.5,
}

BASE_MARKET_MAP = {
    'player_pass_yds': 'pass_yards',
    'player_rush_yds': 'rush_yards',
    'player_reception_yds': 'rec_yards',
    'player_receptions': 'receptions',
    'player_pass_tds': 'pass_tds',
    'player_rush_tds': 'rush_tds',
    'player_reception_tds': 'rec_tds',
    'player_pass_interceptions': 'pass_ints',
    'player_kicking_points': 'kicking_points',
}

# Link alternate market keys to base
ALT_KEYS = {
    'player_pass_yds_alternate': 'player_pass_yds',
    'player_rush_yds_alternate': 'player_rush_yds',
    'player_reception_yds_alternate': 'player_reception_yds',
    'player_receptions_alternate': 'player_receptions',
    'player_pass_tds_alternate': 'player_pass_tds',
    'player_rush_tds_alternate': 'player_rush_tds',
    'player_reception_tds_alternate': 'player_reception_tds',
}

ANYTIME_KEY = 'player_anytime_td'


def american_to_prob(odds: int) -> float:
    if odds >= 0:
        return 100.0 / (odds + 100.0)
    else:
        return abs(odds) / (abs(odds) + 100.0)


def devig_multiplicative(p_over: float, p_under: float) -> Tuple[float, float]:
    total = p_over + p_under
    if total <= 0:
        return 0.5, 0.5
    return p_over / total, p_under / total


def collect_points_from_markets(raw: Dict[str, Any]) -> Dict[str, Dict[str, List[Tuple[float, float, float]]]]:
    """
    Build dict[player][base_market] -> list of (point, fair_over_prob, weight)
    Includes main over/under markets (devigged) and alternates (over-only; treat as-is)
    """
    data: Dict[str, Dict[str, List[Tuple[float, float, float]]]] = {}

    for bm in raw.get('bookmakers', []):
        book = bm.get('title', 'Unknown')
        weight = BOOK_WEIGHTS.get(book, 0.5)

        for market in bm.get('markets', []):
            mkey = market.get('key', '')

            # Map alt markets to their base keys for aggregation
            base_key = ALT_KEYS.get(mkey, mkey)
            if base_key not in BASE_MARKET_MAP and base_key != ANYTIME_KEY:
                continue

            # Group outcomes per player
            by_player: Dict[str, Dict[str, Dict[str, Any]]] = {}
            for out in market.get('outcomes', []):
                player = out.get('description', '')
                name = out.get('name')  # 'Over'/'Under' or 'Yes'
                price = out.get('price')
                point = out.get('point')
                if not player or price is None:
                    continue
                if player not in by_player:
                    by_player[player] = {}
                by_player[player][name] = {'price': price, 'point': point}

            for player, outs in by_player.items():
                if base_key == ANYTIME_KEY:
                    # Store anytime as a special container under 'anytime'
                    p_yes = american_to_prob(int(outs.get('Yes', {}).get('price', 0))) if 'Yes' in outs else None
                    if p_yes is None:
                        continue
                    data.setdefault(player, {}).setdefault(ANYTIME_KEY, [])
                    data[player][ANYTIME_KEY].append((1.0, p_yes, weight))  # point dummy
                    continue

                # Main over/under -> devig
                if 'Over' in outs and 'Under' in outs:
                    over_p = american_to_prob(int(outs['Over']['price']))
                    under_p = american_to_prob(int(outs['Under']['price']))
                    fair_over, _ = devig_multiplicative(over_p, under_p)
                    line = outs['Over']['point'] if outs['Over']['point'] is not None else outs['Under']['point']
                    if line is not None:
                        data.setdefault(player, {}).setdefault(base_key, [])
                        data[player][base_key].append((float(line), float(fair_over), weight))
                # Alternate markets often only have Over
                elif 'Over' in outs and outs['Over'].get('point') is not None:
                    p_over = american_to_prob(int(outs['Over']['price']))
                    data.setdefault(player, {}).setdefault(base_key, [])
                    data[player][base_key].append((float(outs['Over']['point']), float(p_over), weight))

    return data


def fit_mu_sigma(points: List[Tuple[float, float, float]]) -> Optional[Tuple[float, float]]:
    """
    Weighted probit fit: p_over(x) = 1 - Phi((x - mu)/sigma)
    => z = Phi^{-1}(1 - p_over) = (x - mu)/sigma
    Fit x = mu + sigma*z via weighted linear regression.
    """
    xs = []
    zs = []
    ws = []
    for x, p_over, w in points:
        # Clip probabilities to avoid inf values
        p_over = min(max(p_over, 0.01), 0.99)
        z = norm.ppf(1.0 - p_over)
        xs.append(x)
        zs.append(z)
        ws.append(max(w, 1e-3))

    if len(xs) < 2:
        return None

    # Weighted linear regression: x = a + b*z ; a=mu, b=sigma
    W = np.diag(ws)
    Z = np.vstack([np.ones(len(zs)), zs]).T
    X = np.array(xs)
    try:
        params = np.linalg.lstsq(W @ Z, W @ X, rcond=None)[0]
        mu, sigma = float(params[0]), float(params[1])
        sigma = max(sigma, 1e-6)
        return mu, sigma
    except Exception:
        return None


def single_point_mu(points: List[Tuple[float, float, float]], market_key: str) -> Optional[Tuple[float, float]]:
    # Use prior sigma per market and invert mu from a single (x, p_over)
    if not points:
        return None
    x, p_over, _ = points[0]
    p_over = min(max(p_over, 0.01), 0.99)
    z = norm.ppf(1.0 - p_over)
    sigma = SIGMA_PRIORS.get(market_key, 5.0)
    mu = x - z * sigma
    return mu, sigma


def build_stat_means(data: Dict[str, Dict[str, List[Tuple[float, float, float]]]]) -> Dict[str, Dict[str, float]]:
    """Return player -> {stat_key: implied_mean}
    Stat keys: pass_yards, rush_yards, rec_yards, receptions, pass_tds, rush_tds, rec_tds, pass_ints, kicking_points
    """
    player_stats: Dict[str, Dict[str, float]] = {}

    for player, markets in data.items():
        stats: Dict[str, float] = {}
        for market_key, pts in markets.items():
            if market_key == ANYTIME_KEY:
                # handled later in FP conversion as TD bonus if specific TD props missing
                continue
            if market_key not in BASE_MARKET_MAP:
                continue

            # Fit mu,sigma
            fit = fit_mu_sigma(pts)
            if fit is None:
                fit = single_point_mu(pts, market_key)
            if fit is None:
                continue
            mu, _ = fit
            stats[BASE_MARKET_MAP[market_key]] = max(mu, 0.0)

        if stats:
            player_stats[player] = stats

    return player_stats


def attach_anytime_td_bonus(raw_points: Dict[str, Dict[str, List[Tuple[float, float, float]]]],
                            player_stats: Dict[str, Dict[str, float]]) -> Dict[str, float]:
    """Return player -> anytime_td_prob (combined average across books).
    We'll only apply anytime TD to FP if both rec_tds and rush_tds are missing for that player to avoid double counting.
    """
    any_prob: Dict[str, float] = {}
    for player, markets in raw_points.items():
        if ANYTIME_KEY not in markets:
            continue
        probs = []
        weights = []
        for _, p_yes, w in markets[ANYTIME_KEY]:
            probs.append(p_yes)
            weights.append(w)
        if probs:
            p = float(np.average(probs, weights=weights))
            any_prob[player] = min(max(p, 0.0), 1.0)
    return any_prob


def to_fantasy_points(player_stats: Dict[str, Dict[str, float]], anytime_probs: Dict[str, float]) -> Tuple[pd.DataFrame, pd.DataFrame]:
    rows = []
    breakdown_rows = []

    for player, stats in player_stats.items():
        # Base components from explicit props
        fp_components = {}
        for k, v in stats.items():
            if k in SCORING:
                fp_components[k] = v * SCORING[k]

        # Apply anytime TD only if no specific TD props present
        if 'rush_tds' not in stats and 'rec_tds' not in stats and player in anytime_probs:
            fp_components['anytime_td'] = 6.0 * anytime_probs[player]
        else:
            fp_components['anytime_td'] = 0.0

        total_fp = float(sum(fp_components.values()))

        row = {
            'player_name': player,
            'total_fp': round(total_fp, 3),
        }
        rows.append(row)

        # breakdown
        b = {'player_name': player}
        for k in ['pass_yards','pass_tds','pass_ints','rush_yards','rush_tds','rec_yards','receptions','rec_tds','kicking_points']:
            b[f'mean_{k}'] = round(stats.get(k, 0.0), 3)
            b[f'fp_{k}'] = round(stats.get(k, 0.0) * SCORING.get(k, 0.0), 3)
        b['anytime_td_prob'] = round(anytime_probs.get(player, 0.0), 3)
        b['fp_anytime_td'] = round(fp_components['anytime_td'], 3)
        b['total_fp'] = round(total_fp, 3)
        breakdown_rows.append(b)

    df_sum = pd.DataFrame(rows).sort_values('total_fp', ascending=False)
    df_break = pd.DataFrame(breakdown_rows).sort_values('total_fp', ascending=False)
    return df_sum, df_break


def main() -> None:
    raw = json.loads(RAW_PATH.read_text())
    points = collect_points_from_markets(raw)
    player_means = build_stat_means(points)
    anytime_probs = attach_anytime_td_bonus(points, player_means)
    df_sum, df_break = to_fantasy_points(player_means, anytime_probs)

    OUT_SUM_PATH.parent.mkdir(parents=True, exist_ok=True)
    df_sum.to_csv(OUT_SUM_PATH, index=False)
    df_break.to_csv(OUT_BREAK_PATH, index=False)

    print(f"Saved projections: {OUT_SUM_PATH} ({len(df_sum)} players)")
    print(f"Saved breakdown:   {OUT_BREAK_PATH}")
    print("Top 5:")
    print(df_sum.head(5).to_string(index=False))


if __name__ == '__main__':
    main()

