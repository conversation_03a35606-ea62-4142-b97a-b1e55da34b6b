#!/usr/bin/env python3
"""
WAS @ GB Elite Projections System
Creates elite projections using 50% props, 30% models, 20% context methodology.
"""

import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional
from dotenv import load_dotenv

# Add src to path
sys.path.append('src')

from proj.fetch_odds import get_totals_spreads
from was_gb_props_fetcher import WasGBPropsAnalyzer

load_dotenv()


class WasGBEliteProjections:
    """Elite projections for Washington @ Green Bay using props-driven methodology."""
    
    def __init__(self):
        self.game_info = {
            'home_team': 'GB',
            'away_team': 'WAS', 
            'spread': -3.5,  # GB favored
            'total': 49.0,
            'gb_implied_total': 26.25,
            'was_implied_total': 22.75,
            'weather': {
                'conditions': 'clear',
                'temperature': 72,
                'wind_speed': 5,
                'precipitation': 0
            }
        }
        
        # Standard fantasy scoring
        self.scoring = {
            'pass_yards': 0.04,
            'pass_tds': 4.0,
            'pass_ints': -2.0,
            'rush_yards': 0.1,
            'rush_tds': 6.0,
            'rec_yards': 0.1,
            'receptions': 1.0,
            'rec_tds': 6.0,
            'fumbles': -2.0
        }
    
    def load_draftkings_data(self) -> pd.DataFrame:
        """Load and clean DraftKings showdown data."""
        try:
            dk_df = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-2_players.csv', skiprows=1)
            
            # Clean and standardize
            dk_df = dk_df.dropna(subset=['Player'])
            dk_df['Player'] = dk_df['Player'].str.strip()
            
            # Add team mapping
            dk_df['team_abbr'] = dk_df['Team'].map({'WAS': 'WAS', 'GB': 'GB'})
            
            print(f"✅ Loaded {len(dk_df)} DraftKings players")
            return dk_df
            
        except Exception as e:
            print(f"❌ Error loading DraftKings data: {e}")
            return pd.DataFrame()
    
    def load_props_data(self) -> Optional[pd.DataFrame]:
        """Load player props data (API or manual)."""
        # Try to find recent props file
        import glob
        props_files = glob.glob('csvs/was_gb_player_props_*.csv')
        
        if props_files:
            latest_file = max(props_files)
            try:
                props_df = pd.read_csv(latest_file)
                print(f"✅ Loaded props from {latest_file}")
                return props_df
            except Exception as e:
                print(f"❌ Error loading props: {e}")
        
        # Try manual props file
        manual_files = ['csvs/was_gb_manual_props.csv', 'csvs/player_props.csv']
        for file in manual_files:
            if os.path.exists(file):
                try:
                    props_df = pd.read_csv(file)
                    print(f"✅ Loaded manual props from {file}")
                    return props_df
                except Exception as e:
                    continue
        
        print("⚠️  No props data found - will use models only")
        return None
    
    def extract_prop_signals(self, player_name: str, props_df: pd.DataFrame) -> Dict[str, float]:
        """Extract 50% props-based signals for a player."""
        if props_df is None or props_df.empty:
            return {'base_projection': 0, 'confidence': 0}
        
        # Find player props (fuzzy matching)
        player_props = props_df[
            props_df['player_name'].str.contains(player_name, case=False, na=False) |
            props_df['player_name'].str.replace(' ', '').str.contains(
                player_name.replace(' ', ''), case=False, na=False
            )
        ]
        
        if player_props.empty:
            return {'base_projection': 0, 'confidence': 0}
        
        projection = 0
        confidence = 0.8
        
        # Process each prop type
        for _, prop in player_props.iterrows():
            market = prop['market']
            line = prop['line']
            over_odds = prop.get('over_odds', -110)
            under_odds = prop.get('under_odds', -110)
            
            # Convert odds to implied probability
            over_prob = self.american_to_prob(over_odds)
            under_prob = self.american_to_prob(under_odds)
            
            # Remove vig and get fair probability
            total_prob = over_prob + under_prob
            fair_over = over_prob / total_prob
            
            # Calculate implied mean (simplified)
            implied_mean = line * (1 + (fair_over - 0.5) * 0.2)
            
            # Convert to fantasy points based on market
            fantasy_points = self.convert_prop_to_fantasy_points(market, implied_mean)
            projection += fantasy_points
        
        return {
            'base_projection': projection,
            'confidence': confidence,
            'prop_count': len(player_props)
        }
    
    def american_to_prob(self, odds: int) -> float:
        """Convert American odds to probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    
    def convert_prop_to_fantasy_points(self, market: str, implied_mean: float) -> float:
        """Convert prop market to fantasy points."""
        conversions = {
            'player_pass_yds': implied_mean * self.scoring['pass_yards'],
            'player_pass_tds': implied_mean * self.scoring['pass_tds'],
            'player_rush_yds': implied_mean * self.scoring['rush_yards'],
            'player_rush_tds': implied_mean * self.scoring['rush_tds'],
            'player_reception_yds': implied_mean * self.scoring['rec_yards'],
            'player_receptions': implied_mean * self.scoring['receptions'],
            'player_reception_tds': implied_mean * self.scoring['rec_tds'],
            'player_anytime_td': implied_mean * 6.0,  # Average TD value
        }
        
        return conversions.get(market, 0)
    
    def apply_game_context(self, base_proj: float, player: Dict, team: str) -> float:
        """Apply 20% game context adjustments."""
        adjustment = 1.0
        
        # Team total adjustment
        team_total = self.game_info['gb_implied_total'] if team == 'GB' else self.game_info['was_implied_total']
        league_avg = 22.5
        total_factor = team_total / league_avg
        adjustment *= (0.8 + 0.2 * total_factor)  # 20% weight
        
        # Game script (spread impact)
        spread = self.game_info['spread']
        if team == 'GB' and spread < -3:  # GB heavily favored
            if player.get('Pos') in ['RB']:
                adjustment *= 1.1  # More rushing in blowouts
            elif player.get('Pos') == 'QB':
                adjustment *= 0.95  # Less passing volume
        elif team == 'WAS' and spread > 3:  # WAS big underdog
            if player.get('Pos') == 'QB':
                adjustment *= 1.1  # More passing when trailing
            elif player.get('Pos') in ['RB']:
                adjustment *= 0.9  # Less rushing when trailing
        
        # Weather impact (minimal for dome/good conditions)
        weather = self.game_info['weather']
        if weather['wind_speed'] > 15:
            if player.get('Pos') == 'QB':
                adjustment *= 0.95
        
        return base_proj * adjustment
    
    def create_elite_projections(self) -> pd.DataFrame:
        """Create elite projections using 50% props, 30% models, 20% context."""
        print("=== WAS @ GB ELITE PROJECTIONS ===")
        print(f"Game: WAS @ GB | Spread: GB {self.game_info['spread']} | Total: {self.game_info['total']}")
        print(f"Implied Totals: WAS {self.game_info['was_implied_total']}, GB {self.game_info['gb_implied_total']}")
        
        # Load data
        dk_df = self.load_draftkings_data()
        props_df = self.load_props_data()
        
        if dk_df.empty:
            print("❌ Cannot create projections without DraftKings data")
            return pd.DataFrame()
        
        projections = []
        
        for _, player in dk_df.iterrows():
            player_name = player['Player']
            team = player['Team']
            position = player['Pos']
            salary = player['Salary']
            
            # Skip defense for now
            if position == 'DST':
                continue
            
            # 50% Props-based projection
            prop_signals = self.extract_prop_signals(player_name, props_df)
            props_projection = prop_signals['base_projection']
            
            # 30% Model-based (use FC Proj as proxy)
            model_projection = player.get('FC', 0) if pd.notna(player.get('FC')) else 0
            
            # Blend: 50% props + 30% model
            if props_projection > 0:
                base_projection = 0.5 * props_projection + 0.3 * model_projection
                confidence = 0.85
            else:
                # Fallback to model if no props
                base_projection = model_projection
                confidence = 0.6
            
            # 20% Game context
            final_projection = self.apply_game_context(base_projection, player, team)
            
            projections.append({
                'player_name': player_name,
                'team': team,
                'position': position,
                'salary': salary,
                'props_proj': props_projection,
                'model_proj': model_projection,
                'final_proj': round(final_projection, 2),
                'confidence': confidence,
                'value': round(final_projection / salary * 1000, 2) if salary > 0 else 0,
                'prop_count': prop_signals.get('prop_count', 0)
            })
        
        proj_df = pd.DataFrame(projections)
        proj_df = proj_df.sort_values('final_proj', ascending=False)
        
        print(f"\n✅ Created {len(proj_df)} projections")
        print("\nTop 10 Projections:")
        print(proj_df[['player_name', 'team', 'position', 'final_proj', 'value', 'prop_count']].head(10).to_string(index=False))
        
        return proj_df
    
    def save_projections(self, proj_df: pd.DataFrame) -> str:
        """Save projections to CSV."""
        filename = f"csvs/was_gb_elite_projections_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        proj_df.to_csv(filename, index=False)
        print(f"\n💾 Projections saved to: {filename}")
        return filename


def main():
    """Main execution."""
    projector = WasGBEliteProjections()
    
    # Try to fetch live odds first
    try:
        analyzer = WasGBPropsAnalyzer()
        analyzer.fetch_game_odds()
        projector.game_info.update(analyzer.game_info)
    except:
        print("Using default game odds")
    
    # Create projections
    proj_df = projector.create_elite_projections()
    
    if not proj_df.empty:
        projector.save_projections(proj_df)
        
        print("\n🎯 NEXT STEPS:")
        print("1. Review projections for accuracy")
        print("2. Compare with your manual analysis")
        print("3. Adjust for any late news/injuries")
        print("4. Build optimal lineups")
    
    return proj_df


if __name__ == "__main__":
    main()
