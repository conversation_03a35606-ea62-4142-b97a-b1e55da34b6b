#!/usr/bin/env python3
"""
Browns @ Ravens DraftKings Projections
Elite-level projections using team ratings and market data
"""

import pandas as pd
import numpy as np

def get_team_edges():
    """Get matchup edges from team ratings"""
    try:
        df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
        
        # Find Ravens and Browns
        ravens_row = df_holes_levers[df_holes_levers['team'].str.contains('Ravens|BAL', case=False, na=False)]
        browns_row = df_holes_levers[df_holes_levers['team'].str.contains('Browns|CLE', case=False, na=False)]
        
        if not ravens_row.empty and not browns_row.empty:
            ravens = ravens_row.iloc[0]
            browns = browns_row.iloc[0]
            
            # Ravens advantages (from our earlier analysis)
            ravens_pass_edge = ravens.get('lever_explosive_pass', 2.56) - browns.get('hole_pass_eff', 1.75)
            ravens_rush_edge = ravens.get('lever_ppd', 1.69) - browns.get('hole_rush_eff', 0.5)
            ravens_explosive_edge = ravens.get('lever_explosive_pass', 2.56) - browns.get('hole_explosive_pass', 1.13)
            
            return {
                'ravens_pass_edge': ravens_pass_edge,
                'ravens_rush_edge': ravens_rush_edge, 
                'ravens_explosive_edge': ravens_explosive_edge,
                'browns_pass_edge': -0.5,  # Browns weaker
                'browns_rush_edge': -0.3
            }
    except:
        pass
    
    # Default edges based on team analysis
    return {
        'ravens_pass_edge': 1.2,
        'ravens_rush_edge': 0.8,
        'ravens_explosive_edge': 1.5,
        'browns_pass_edge': -0.5,
        'browns_rush_edge': -0.3
    }

def create_projections():
    """Create elite DraftKings projections"""
    
    edges = get_team_edges()
    
    # DraftKings scoring
    scoring = {
        'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
        'rush_yard': 0.1, 'rush_td': 6,
        'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
        'fg_made': 3, 'xp_made': 1,
        'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
        'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4, 'dst_pts_14_20': 1
    }
    
    # Model team totals with edges
    ravens_base = {'pass_yds': 275, 'pass_tds': 2.5, 'rush_yds': 140, 'rush_tds': 1.5, 'points': 28}
    browns_base = {'pass_yds': 225, 'pass_tds': 1.5, 'rush_yds': 95, 'rush_tds': 0.8, 'points': 19}
    
    # Apply edges
    ravens_adj = {
        'pass_yds': ravens_base['pass_yds'] * (1 + 0.06 * edges['ravens_pass_edge']),
        'pass_tds': ravens_base['pass_tds'] * (1 + 0.08 * edges['ravens_rush_edge']),
        'rush_yds': ravens_base['rush_yds'] * (1 + 0.05 * edges['ravens_rush_edge']),
        'rush_tds': ravens_base['rush_tds'] * (1 + 0.07 * edges['ravens_rush_edge']),
        'points': ravens_base['points']
    }
    
    browns_adj = {
        'pass_yds': browns_base['pass_yds'] * (1 + 0.06 * edges['browns_pass_edge']),
        'pass_tds': browns_base['pass_tds'] * (1 + 0.08 * edges['browns_rush_edge']),
        'rush_yds': browns_base['rush_yds'] * (1 + 0.05 * edges['browns_rush_edge']),
        'rush_tds': browns_base['rush_tds'] * (1 + 0.07 * edges['browns_rush_edge']),
        'points': browns_base['points']
    }
    
    # Player projections
    projections = {}
    
    # RAVENS PLAYERS
    
    # Lamar Jackson - Elite dual-threat QB with explosive edge
    lamar_pass_yds = ravens_adj['pass_yds'] * 0.85  # 85% of team passing
    lamar_rush_yds = ravens_adj['rush_yds'] * 0.25  # 25% of team rushing
    projections['Lamar Jackson'] = (
        lamar_pass_yds * scoring['pass_yard'] +
        ravens_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        0.8 * scoring['pass_int'] +  # INTs
        lamar_rush_yds * scoring['rush_yard'] +
        ravens_adj['rush_tds'] * 0.3 * scoring['rush_td']
    )
    
    # Derrick Henry - Workhorse RB
    henry_rush_yds = ravens_adj['rush_yds'] * 0.65
    projections['Derrick Henry'] = (
        henry_rush_yds * scoring['rush_yard'] +
        ravens_adj['rush_tds'] * 0.6 * scoring['rush_td'] +
        2.5 * scoring['reception'] + 18 * scoring['rec_yard']  # Receiving
    )
    
    # Zay Flowers - WR1 with explosive upside
    flowers_rec_yds = lamar_pass_yds * 0.25
    projections['Zay Flowers'] = (
        6 * scoring['reception'] +
        flowers_rec_yds * scoring['rec_yard'] +
        ravens_adj['pass_tds'] * 0.3 * scoring['rec_td']
    )
    
    # Mark Andrews - TE1
    andrews_rec_yds = lamar_pass_yds * 0.18
    projections['Mark Andrews'] = (
        4 * scoring['reception'] +
        andrews_rec_yds * scoring['rec_yard'] +
        ravens_adj['pass_tds'] * 0.25 * scoring['rec_td']
    )
    
    # Rashod Bateman - WR2
    projections['Rashod Bateman'] = (
        3.2 * scoring['reception'] +
        42 * scoring['rec_yard'] +
        0.3 * scoring['rec_td']
    )
    
    # Justice Hill - RB2
    projections['Justice Hill'] = (
        35 * scoring['rush_yard'] +
        0.2 * scoring['rush_td'] +
        1.8 * scoring['reception'] + 15 * scoring['rec_yard']
    )
    
    # DeAndre Hopkins - WR3
    projections['DeAndre Hopkins'] = (
        2.5 * scoring['reception'] +
        32 * scoring['rec_yard'] +
        0.2 * scoring['rec_td']
    )
    
    # BROWNS PLAYERS
    
    # Joe Flacco - Starting QB vs tough Ravens D
    flacco_pass_yds = browns_adj['pass_yds'] * 0.85
    projections['Joe Flacco'] = (
        flacco_pass_yds * scoring['pass_yard'] +
        browns_adj['pass_tds'] * 0.9 * scoring['pass_td'] +
        1.2 * scoring['pass_int'] +  # Higher INTs vs Ravens
        5 * scoring['rush_yard']  # Minimal rushing
    )
    
    # Jerry Jeudy - WR1
    jeudy_rec_yds = flacco_pass_yds * 0.28
    projections['Jerry Jeudy'] = (
        5.5 * scoring['reception'] +
        jeudy_rec_yds * scoring['rec_yard'] +
        browns_adj['pass_tds'] * 0.35 * scoring['rec_td']
    )
    
    # David Njoku - TE1
    njoku_rec_yds = flacco_pass_yds * 0.20
    projections['David Njoku'] = (
        4 * scoring['reception'] +
        njoku_rec_yds * scoring['rec_yard'] +
        browns_adj['pass_tds'] * 0.25 * scoring['rec_td']
    )
    
    # Jerome Ford - RB1
    projections['Jerome Ford'] = (
        48 * scoring['rush_yard'] +
        0.4 * scoring['rush_td'] +
        2.5 * scoring['reception'] + 20 * scoring['rec_yard']
    )
    
    # Cedric Tillman - WR2
    projections['Cedric Tillman'] = (
        2.8 * scoring['reception'] +
        35 * scoring['rec_yard'] +
        0.2 * scoring['rec_td']
    )
    
    # Dylan Sampson - RB (if active)
    projections['Dylan Sampson'] = (
        25 * scoring['rush_yard'] +
        0.2 * scoring['rush_td'] +
        1 * scoring['reception'] + 8 * scoring['rec_yard']
    )
    
    # DEFENSE/SPECIAL TEAMS
    
    # Ravens DST - Elite defense vs struggling Browns offense
    projections['Ravens'] = (
        3.2 * scoring['dst_sack'] +
        1.1 * scoring['dst_int'] +
        0.8 * scoring['dst_fumble_rec'] +
        0.3 * scoring['dst_td'] +
        scoring['dst_pts_14_20']  # Browns likely score 14-20
    )
    
    # Browns DST - Facing elite Ravens offense
    projections['Browns'] = (
        2.1 * scoring['dst_sack'] +
        0.7 * scoring['dst_int'] +
        0.5 * scoring['dst_fumble_rec'] +
        0.15 * scoring['dst_td'] +
        -1  # Ravens likely score 28+ (28-34 range)
    )
    
    # KICKERS
    
    # Justin Tucker - Elite kicker, Ravens should score
    projections['Justin Tucker'] = (
        2.2 * scoring['fg_made'] +
        3.4 * scoring['xp_made']
    )
    
    # Dustin Hopkins - Browns kicker
    projections['Dustin Hopkins'] = (
        1.7 * scoring['fg_made'] +
        2.1 * scoring['xp_made']
    )
    
    return projections

def main():
    """Generate and output projections"""
    projections = create_projections()
    
    # Sort by projection descending
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)
    
    print("Player,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

if __name__ == "__main__":
    main()
