#!/usr/bin/env python3
"""
Integrated NFL Projection System v3.0
Complete system with enhanced prop reading capabilities baked in
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import json
from datetime import datetime
import requests


class IntegratedNFLSystem:
    """Complete NFL projection system with all enhancements integrated."""
    
    def __init__(self, odds_api_key: str = None):
        self.odds_api_key = odds_api_key
        
        # Enhanced market reading parameters
        self.market_config = {
            'sharp_money_threshold': 2.0,      # Points difference for sharp signal
            'uncertainty_std_threshold': 2.0,  # Line STD for uncertainty
            'wide_range_threshold': 4.0,       # Line range for uncertainty
            'min_books_threshold': 4,          # Minimum books for confidence
            'min_edge_threshold': 0.08,        # Minimum edge for recommendations
            'min_confidence_threshold': 0.65   # Minimum confidence for recommendations
        }
        
        # Market signal confidence adjustments
        self.adjustments = {
            'high_uncertainty': 0.75,    # 25% reduction for high uncertainty
            'sharp_alignment': 1.25,     # 25% boost for sharp money alignment
            'sharp_disagreement': 0.70,  # 30% reduction for sharp disagreement
            'low_liquidity': 0.85,       # 15% reduction for thin markets
            'correlation_issue': 0.80    # 20% reduction for correlation problems
        }
        
        # Sportsbook reliability (from post-game analysis)
        self.book_reliability = {
            'circa': 0.30,
            'pinnacle': 0.30,
            'betmgm': 0.20,
            'bookmaker': 0.15,
            'betrivers': 0.05
        }
    
    def fetch_live_props(self, game_id: str = None) -> Dict[str, Any]:
        """Fetch live player props from The Odds API."""
        
        if not self.odds_api_key:
            print("⚠️ No Odds API key provided - using sample data")
            return self.load_sample_props()
        
        try:
            # Fetch NFL player props
            url = "https://api.the-odds-api.com/v4/sports/americanfootball_nfl/odds"
            params = {
                'api_key': self.odds_api_key,
                'regions': 'us',
                'markets': 'player_pass_yds,player_rush_yds,player_reception_yds,player_receptions',
                'oddsFormat': 'american'
            }
            
            response = requests.get(url, params=params)
            
            if response.status_code == 200:
                return self.process_api_response(response.json())
            else:
                print(f"❌ API Error: {response.status_code}")
                return self.load_sample_props()
                
        except Exception as e:
            print(f"❌ Error fetching props: {e}")
            return self.load_sample_props()
    
    def load_sample_props(self) -> Dict[str, Any]:
        """Load sample prop data for testing."""
        try:
            prop_data = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')
            return {'prop_data': prop_data, 'source': 'sample'}
        except:
            return {'error': 'No sample data available'}
    
    def analyze_market_signals(self, prop_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market signals for enhanced prop reading."""
        
        signals = {
            'sharp_money': {},
            'uncertainty': {},
            'liquidity': {},
            'summary': {'total_props': len(prop_data), 'signals_found': 0}
        }
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            # Sharp money analysis
            sharp_line = row.get('sharp_line', row.get('line_consensus', 0))
            public_line = row.get('public_line', row.get('line_consensus', 0))
            sharp_diff = abs(sharp_line - public_line)
            
            if sharp_diff >= self.market_config['sharp_money_threshold']:
                signals['sharp_money'][key] = {
                    'direction': 'OVER' if sharp_line > public_line else 'UNDER',
                    'strength': 'STRONG' if sharp_diff > 3 else 'MEDIUM',
                    'difference': sharp_diff,
                    'sharp_line': sharp_line,
                    'public_line': public_line
                }
                signals['summary']['signals_found'] += 1
            
            # Uncertainty analysis
            line_std = row.get('line_std', 0)
            line_range = row.get('line_range_max', 0) - row.get('line_range_min', 0)
            book_count = row.get('book_count', 1)
            
            uncertainty_score = 0
            if line_std > self.market_config['uncertainty_std_threshold']:
                uncertainty_score += 0.4
            if line_range > self.market_config['wide_range_threshold']:
                uncertainty_score += 0.4
            if book_count < self.market_config['min_books_threshold']:
                uncertainty_score += 0.2
            
            if uncertainty_score > 0.5:
                signals['uncertainty'][key] = {
                    'score': min(1.0, uncertainty_score),
                    'factors': {
                        'high_std': line_std > self.market_config['uncertainty_std_threshold'],
                        'wide_range': line_range > self.market_config['wide_range_threshold'],
                        'low_books': book_count < self.market_config['min_books_threshold']
                    }
                }
            
            # Liquidity scoring
            liquidity_score = min(1.0, book_count / 6.0)
            signals['liquidity'][key] = liquidity_score
        
        return signals
    
    def check_correlations(self, projections: Dict[str, float]) -> List[Dict]:
        """Check for correlation issues in projections."""
        
        issues = []
        
        # QB-WR correlation checks
        qb_wr_pairs = [
            ('Jayden Daniels', ['Terry McLaurin', 'Deebo Samuel']),
            ('Jordan Love', ['Romeo Doubs', 'Jayden Reed', 'Tucker Kraft'])
        ]
        
        for qb, receivers in qb_wr_pairs:
            if qb in projections:
                qb_proj = projections[qb]
                qb_pass_component = qb_proj * 0.65  # Assume 65% from passing
                
                for wr in receivers:
                    if wr in projections:
                        wr_proj = projections[wr]
                        
                        # Flag if QB high but WR low
                        if qb_pass_component > 18 and wr_proj < 10:
                            issues.append({
                                'type': 'QB_WR_MISMATCH',
                                'qb': qb,
                                'receiver': wr,
                                'qb_pass_component': qb_pass_component,
                                'receiver_projection': wr_proj,
                                'severity': 'HIGH' if qb_pass_component > 22 else 'MEDIUM'
                            })
        
        return issues
    
    def generate_market_aware_projections(self, 
                                        base_projections: Dict[str, float],
                                        prop_data: pd.DataFrame,
                                        market_signals: Dict) -> Tuple[Dict[str, float], Dict]:
        """Generate projections adjusted for market signals."""
        
        adjusted_projections = base_projections.copy()
        adjustments_made = {}
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            if player not in adjusted_projections:
                continue
            
            original = adjusted_projections[player]
            line = row.get('line_consensus', original)
            
            # Apply sharp money adjustments
            if key in market_signals['sharp_money']:
                sharp_signal = market_signals['sharp_money'][key]
                sharp_line = sharp_signal['sharp_line']
                
                # Blend toward sharp line (30% weight)
                adjusted_projections[player] = original * 0.7 + sharp_line * 0.3
                
                adjustments_made[player] = {
                    'type': 'sharp_money',
                    'direction': sharp_signal['direction'],
                    'original': original,
                    'adjusted': adjusted_projections[player]
                }
            
            # Apply uncertainty adjustments (move toward consensus)
            elif key in market_signals['uncertainty']:
                uncertainty = market_signals['uncertainty'][key]
                uncertainty_factor = uncertainty['score'] * 0.2  # Max 20% adjustment
                
                adjusted_projections[player] = (original * (1 - uncertainty_factor) + 
                                              line * uncertainty_factor)
                
                adjustments_made[player] = {
                    'type': 'uncertainty',
                    'score': uncertainty['score'],
                    'original': original,
                    'adjusted': adjusted_projections[player]
                }
        
        return adjusted_projections, adjustments_made
    
    def create_final_recommendations(self, 
                                   projections: Dict[str, float],
                                   prop_data: pd.DataFrame,
                                   market_signals: Dict) -> List[Dict]:
        """Create final recommendations with market signal integration."""
        
        recommendations = []
        correlation_issues = self.check_correlations(projections)
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            line = row.get('line_consensus', 0)
            
            if player not in projections or line == 0:
                continue
            
            projection = projections[player]
            edge = abs(projection - line) / line
            
            if edge < self.market_config['min_edge_threshold']:
                continue
            
            # Calculate confidence with market adjustments
            base_confidence = row.get('confidence_score', 0.5)
            final_confidence = base_confidence
            confidence_notes = []
            
            # Sharp money adjustment
            if key in market_signals['sharp_money']:
                sharp_signal = market_signals['sharp_money'][key]
                our_direction = 'OVER' if projection > line else 'UNDER'
                
                if sharp_signal['direction'] == our_direction:
                    final_confidence *= self.adjustments['sharp_alignment']
                    confidence_notes.append(f"Sharp money aligned ({sharp_signal['strength']})")
                else:
                    final_confidence *= self.adjustments['sharp_disagreement']
                    confidence_notes.append("Sharp money disagrees")
            
            # Uncertainty penalty
            if key in market_signals['uncertainty']:
                final_confidence *= self.adjustments['high_uncertainty']
                confidence_notes.append("High market uncertainty")
            
            # Liquidity adjustment
            liquidity = market_signals['liquidity'].get(key, 1.0)
            if liquidity < 0.7:
                final_confidence *= self.adjustments['low_liquidity']
                confidence_notes.append("Low market liquidity")
            
            # Correlation penalty
            player_issues = [issue for issue in correlation_issues 
                           if issue.get('qb') == player or issue.get('receiver') == player]
            if player_issues:
                final_confidence *= self.adjustments['correlation_issue']
                confidence_notes.append("Correlation inconsistency")
            
            final_confidence = max(0.1, min(1.0, final_confidence))
            
            if final_confidence >= self.market_config['min_confidence_threshold']:
                rec = {
                    'player': player,
                    'market': market,
                    'line': line,
                    'projection': projection,
                    'edge': edge,
                    'recommendation': 'OVER' if projection > line else 'UNDER',
                    'confidence': final_confidence,
                    'confidence_notes': confidence_notes
                }
                
                # Add market context
                if key in market_signals['sharp_money']:
                    rec['sharp_signal'] = market_signals['sharp_money'][key]
                
                recommendations.append(rec)
        
        # Sort by confidence * edge
        recommendations.sort(key=lambda x: x['confidence'] * x['edge'], reverse=True)
        
        return recommendations
    
    def run_complete_analysis(self, 
                            base_projections: Dict[str, float],
                            game_id: str = None) -> Dict[str, Any]:
        """Run complete integrated analysis."""
        
        print("🚀 INTEGRATED NFL SYSTEM v3.0")
        print("=" * 50)
        
        # 1. Fetch prop data
        print("\n📡 FETCHING PROP DATA...")
        prop_response = self.fetch_live_props(game_id)
        
        if 'error' in prop_response:
            return {'error': prop_response['error']}
        
        prop_data = prop_response['prop_data']
        print(f"   ✅ Loaded {len(prop_data)} props")
        
        # 2. Analyze market signals
        print("\n🔍 ANALYZING MARKET SIGNALS...")
        market_signals = self.analyze_market_signals(prop_data)
        
        print(f"   • Sharp money signals: {len(market_signals['sharp_money'])}")
        print(f"   • High uncertainty props: {len(market_signals['uncertainty'])}")
        
        # 3. Generate market-aware projections
        print("\n⚖️ ADJUSTING PROJECTIONS FOR MARKET SIGNALS...")
        adjusted_projections, adjustments = self.generate_market_aware_projections(
            base_projections, prop_data, market_signals
        )
        
        print(f"   • Players adjusted: {len(adjustments)}")
        
        # 4. Create final recommendations
        print("\n🎯 GENERATING FINAL RECOMMENDATIONS...")
        recommendations = self.create_final_recommendations(
            adjusted_projections, prop_data, market_signals
        )
        
        print(f"   • Final recommendations: {len(recommendations)}")
        
        # 5. Display top recommendations
        print(f"\n📋 TOP RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"   {i}. {rec['player']} {rec['market']} {rec['recommendation']}")
            print(f"      Line: {rec['line']:.1f}, Projection: {rec['projection']:.1f}")
            print(f"      Edge: {rec['edge']:.1%}, Confidence: {rec['confidence']:.2f}")
            if 'sharp_signal' in rec:
                print(f"      Sharp: {rec['sharp_signal']['direction']} ({rec['sharp_signal']['strength']})")
        
        return {
            'prop_data': prop_data,
            'market_signals': market_signals,
            'adjusted_projections': adjusted_projections,
            'adjustments_made': adjustments,
            'recommendations': recommendations,
            'correlation_issues': self.check_correlations(adjusted_projections)
        }


def main():
    """Example usage of integrated system."""
    
    # Initialize system (add your Odds API key here)
    system = IntegratedNFLSystem(odds_api_key=None)  # Add your API key
    
    # Example base projections (from your models)
    sample_projections = {
        'Jayden Daniels': 28.5,
        'Jordan Love': 24.2,
        'Josh Jacobs': 16.8,
        'Terry McLaurin': 13.5,
        'Romeo Doubs': 11.2,
        'Tucker Kraft': 10.8,
        'Deebo Samuel': 15.1
    }
    
    # Run complete analysis
    results = system.run_complete_analysis(sample_projections)
    
    if 'error' not in results:
        print(f"\n✅ ANALYSIS COMPLETE!")
        print(f"   System ready for live game analysis!")
    else:
        print(f"❌ Error: {results['error']}")


if __name__ == "__main__":
    main()
