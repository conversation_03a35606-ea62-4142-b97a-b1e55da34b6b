#!/usr/bin/env python3
"""
Debug <PERSON>'s exact calculation step by step
"""

import os
from bears_lions_elite_projections import BearsLionsEliteProjections

def debug_goff_calculation():
    """Show exact calculation for <PERSON>."""
    
    print("🔍 JARED GOFF CALCULATION BREAKDOWN")
    print("=" * 60)
    
    # Set API key
    os.environ['ODDS_API_KEY'] = '2196c7a83756b69c7efe1926c47fc009'
    
    system = BearsLionsEliteProjections()
    
    # Try to get real market data
    try:
        market_data = system.get_mock_market_data()  # This will try real API first
        player_props = market_data['player_props']
        
        if '<PERSON>' not in player_props:
            print("❌ <PERSON> Goff not found in props")
            return
            
        goff_props = player_props['<PERSON>']
        print(f"📊 Goff Props: {goff_props}")
        
        # Apply elite prop methodology
        prop_analysis = system.apply_elite_prop_methodology('<PERSON>', goff_props)
        print(f"🧠 Prop Analysis: {prop_analysis}")
        
        # Manual calculation trace
        print(f"\n🔬 STEP-BY-STEP CALCULATION:")
        
        # Get factors
        market_factor = prop_analysis['market_strength_factor']
        sharp_factor = prop_analysis['sharp_money_factor'] 
        psych_factor = prop_analysis['psychological_factor']
        value_factor = prop_analysis['line_value_factor']
        combined_factor = market_factor * sharp_factor * psych_factor * value_factor
        
        print(f"Market Factor: {market_factor:.3f}")
        print(f"Sharp Factor: {sharp_factor:.3f}")
        print(f"Psychological Factor: {psych_factor:.3f}")
        print(f"Value Factor: {value_factor:.3f}")
        print(f"Combined Factor: {combined_factor:.3f}")
        
        projection = 0.0
        
        # Passing yards
        if 'player_pass_yds' in goff_props:
            base_yards = goff_props['player_pass_yds']['line']
            adjusted_yards = base_yards * combined_factor
            pass_points = adjusted_yards * system.dk_scoring['pass_yard']
            projection += pass_points
            print(f"\nPass Yards: {base_yards} × {combined_factor:.3f} = {adjusted_yards:.1f} yards")
            print(f"  → {adjusted_yards:.1f} × {system.dk_scoring['pass_yard']} = {pass_points:.2f} pts")
        
        # Passing TDs
        if 'player_pass_tds' in goff_props:
            base_tds = goff_props['player_pass_tds']['line']
            adjusted_tds = base_tds * combined_factor
            td_points = adjusted_tds * system.dk_scoring['pass_td']
            projection += td_points
            print(f"\nPass TDs: {base_tds} × {combined_factor:.3f} = {adjusted_tds:.2f} TDs")
            print(f"  → {adjusted_tds:.2f} × {system.dk_scoring['pass_td']} = {td_points:.2f} pts")
        
        # Interceptions
        int_points = 1.2 * system.dk_scoring['pass_int']
        projection += int_points
        print(f"\nInterceptions: 1.2 × {system.dk_scoring['pass_int']} = {int_points:.2f} pts")
        
        # Anytime TD (rushing)
        if 'player_anytime_td' in goff_props:
            td_prob = system.calculate_anytime_td_probability(goff_props['player_anytime_td'])
            rush_td_points = td_prob * system.dk_scoring['rush_td']
            projection += rush_td_points
            print(f"\nRush TD Probability: {td_prob:.3f}")
            print(f"  → {td_prob:.3f} × {system.dk_scoring['rush_td']} = {rush_td_points:.2f} pts")
        
        print(f"\n📈 BASE PROJECTION: {projection:.2f} pts")
        
        # Team context multiplier
        context_multiplier = system.apply_team_context('Jared Goff', 'QB', 'DET')
        print(f"🏈 Team Context Multiplier: {context_multiplier:.3f}")
        
        # Show team edges
        print(f"\nTeam Edges:")
        for edge, value in system.team_edges.items():
            print(f"  {edge}: {value:.3f}")
        
        # Final calculation
        final_projection = projection * context_multiplier
        print(f"\n🎯 FINAL: {projection:.2f} × {context_multiplier:.3f} = {final_projection:.2f} pts")
        
        # Compare to system output
        system_projection = system.calculate_prop_projection('Jared Goff', 'QB', goff_props, prop_analysis)
        system_final = system_projection * context_multiplier
        
        print(f"\n✅ VERIFICATION:")
        print(f"Manual calculation: {final_projection:.2f}")
        print(f"System calculation: {system_final:.2f}")
        print(f"Match: {'✓' if abs(final_projection - system_final) < 0.01 else '✗'}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_goff_calculation()
