# Ravens vs Bills Focused Projections Summary

## Overview
Created a focused projection script for the Ravens vs Bills playoff game with enhanced logic that includes:
- Live odds fetching via The Odds API
- Weather context integration
- Enhanced player props adjustments
- Game script analysis

## Key Features Implemented

### 1. Ravens vs Bills Focused Script (`ravens_bills_projections.py`)
- **Full Pipeline**: Runs complete offense and defense projections
- **Live Odds Integration**: Fetches current spreads and totals from The Odds API
- **Weather Context**: Incorporates Buffalo weather conditions (25°F, cold/windy)
- **Player Props**: Uses actual prop lines from the odds file for enhanced projections
- **Depth Chart Integration**: Uses actual Ravens and Bills depth charts

### 2. Enhanced Projection Engine Updates (`src/proj/enhanced_projections.py`)
- **Weather Adjustments**: Added comprehensive weather impact logic
  - Cold weather (< 32°F): QB/WR/TE penalties, RB bonuses
  - Wind (> 15 mph): Passing game penalties
  - Precipitation: Wet ball adjustments
  - Severe conditions: Major passing penalties, rushing bonuses
- **Improved Game Context**: Enhanced team total and spread adjustments
- **Position-Specific Logic**: More nuanced adjustments by position

### 3. Game Context Engine Updates (`src/proj/game_context.py`)
- **Stadium Locations**: Added NFL stadium coordinates and dome status
- **Weather Integration**: Automatic weather context for outdoor stadiums
- **Default Weather Logic**: Season-appropriate weather defaults by location
- **Enhanced Context Extraction**: Always includes weather in game context

### 4. New CLI Command (`src/proj/cli.py`)
- **Focused Game Command**: `python -m src.proj.cli focused-game BAL BUF`
- **Live Odds Fetching**: Automatically pulls current odds if API key available
- **Weather Display**: Shows game context including weather conditions
- **Flexible Data Sources**: Works with existing data files

## Current Projections (Ravens vs Bills)

### Top Projections
1. **Josh Allen (BUF QB)**: 19.86 points
2. **Lamar Jackson (BAL QB)**: 19.47 points  
3. **James Cook (BUF RB)**: 16.07 points
4. **Derrick Henry (BAL RB)**: 15.75 points
5. **Khalil Shakir (BUF WR)**: 13.61 points
6. **Zay Flowers (BAL WR)**: 13.34 points

### Game Context Applied
- **Total**: 47.0 points
- **Spread**: Bills -1.5
- **Bills Implied Total**: 24.25
- **Ravens Implied Total**: 22.75
- **Weather**: 25°F, cold/windy conditions
- **Game Script**: Competitive

### Weather Impact
- **QBs**: 5% penalty for cold conditions
- **RBs**: 5% bonus for increased rushing in cold weather
- **WR/TEs**: 8% penalty for cold/windy catching conditions
- **Defenses**: Adjusted based on opponent totals and weather

## Technical Implementation

### Weather Adjustment Logic
```python
def _apply_weather_adjustments(self, position: str, weather: Dict[str, Any]) -> float:
    # Cold weather (< 32°F)
    if temperature < 32:
        if position == 'QB': adjustment *= 0.95
        elif position == 'RB': adjustment *= 1.05
        elif position in ['WR', 'TE']: adjustment *= 0.92
    
    # Wind (> 15 mph)
    if wind_speed > 15:
        if position == 'QB': adjustment *= 0.9
        elif position in ['WR', 'TE']: adjustment *= 0.88
    
    # Severe conditions
    if conditions in ['cold_windy', 'stormy', 'blizzard']:
        if position in ['QB', 'WR', 'TE']: adjustment *= 0.85
        elif position == 'RB': adjustment *= 1.15
```

### Enhanced Props Integration
- **Volume-Based Adjustments**: Higher prop lines = higher projections
- **Position-Specific Logic**: Different thresholds for QB/RB/WR/TE
- **TD Props Bonus**: 10% boost for players with anytime TD props
- **Market-Driven Scaling**: Uses actual betting lines to gauge usage

## Usage Instructions

### Run Ravens vs Bills Projections
```bash
python ravens_bills_projections.py
```

### Use Enhanced Engine for Any Game
```bash
python -m src.proj.cli focused-game BAL BUF --output projections.csv
```

### With Live Odds (requires API key)
```bash
# Set ODDS_API_KEY in .env file
python -m src.proj.cli focused-game BAL BUF
```

## Future Enhancements
1. **Real-Time Weather API**: Integrate live weather data
2. **Injury Updates**: Incorporate real-time injury reports
3. **Line Movement Tracking**: Monitor odds changes over time
4. **Advanced Game Scripts**: More sophisticated game flow modeling
5. **Player Correlation**: Account for teammate correlations

## Files Modified/Created
- `ravens_bills_projections.py` - New focused script
- `src/proj/enhanced_projections.py` - Enhanced weather logic
- `src/proj/game_context.py` - Weather integration
- `src/proj/cli.py` - New focused-game command
- `ravens_bills_projections.csv` - Output file

The enhanced projection engine now provides more accurate, context-aware projections that account for weather, game script, and real-time betting market information.
