"""Tests for enhanced model functionality."""

import pytest
import numpy as np
import pandas as pd
from src.proj.model import (
    american_to_prob, devig, line_to_implied_mean, bayes_blend,
    ContextAwareModel, DefensivePlayerModel
)


def test_american_to_prob():
    """Test American odds to probability conversion."""
    # Positive odds
    assert abs(american_to_prob(100) - 0.5) < 0.001
    assert abs(american_to_prob(200) - 0.333) < 0.001
    assert abs(american_to_prob(300) - 0.25) < 0.001
    
    # Negative odds
    assert abs(american_to_prob(-100) - 0.5) < 0.001
    assert abs(american_to_prob(-200) - 0.667) < 0.001
    assert abs(american_to_prob(-300) - 0.75) < 0.001


def test_devig():
    """Test vig removal from over/under probabilities."""
    # Test multiplicative method
    p_over_raw = 0.52
    p_under_raw = 0.52
    p_over_fair, p_under_fair = devig(p_over_raw, p_under_raw, "multiplicative")
    
    # Should sum to 1.0
    assert abs((p_over_fair + p_under_fair) - 1.0) < 0.001
    
    # Should be equal for symmetric case
    assert abs(p_over_fair - 0.5) < 0.001
    assert abs(p_under_fair - 0.5) < 0.001
    
    # Test additive method
    p_over_fair, p_under_fair = devig(p_over_raw, p_under_raw, "additive")
    assert abs((p_over_fair + p_under_fair) - 1.0) < 0.001
    
    # Test power method
    p_over_fair, p_under_fair = devig(p_over_raw, p_under_raw, "power")
    assert abs((p_over_fair + p_under_fair) - 1.0) < 0.001


def test_line_to_implied_mean():
    """Test line to implied mean conversion."""
    # Test case: line = 50, p_over = 0.5 should give mean = 50
    line = 50.0
    p_over = 0.5
    sigma = 7.0
    
    implied_mean = line_to_implied_mean(line, p_over, sigma)
    assert abs(implied_mean - 50.0) < 0.1
    
    # Test case: p_over > 0.5 should give mean > line
    p_over = 0.6
    implied_mean = line_to_implied_mean(line, p_over, sigma)
    assert implied_mean > line
    
    # Test case: p_over < 0.5 should give mean < line
    p_over = 0.4
    implied_mean = line_to_implied_mean(line, p_over, sigma)
    assert implied_mean < line


def test_bayes_blend():
    """Test Bayesian blending of prior and market information."""
    # Test equal confidence case
    mu_prior = 20.0
    sigma_prior = 5.0
    mu_mkt = 24.0
    sigma_mkt = 5.0
    
    mu_post, sigma_post = bayes_blend(mu_prior, sigma_prior, mu_mkt, sigma_mkt)
    
    # Posterior mean should be between prior and market
    assert mu_prior < mu_post < mu_mkt
    
    # Posterior sigma should be smaller than both inputs
    assert sigma_post < sigma_prior
    assert sigma_post < sigma_mkt
    
    # Test high confidence market case
    sigma_mkt = 1.0  # Very confident market
    mu_post, sigma_post = bayes_blend(mu_prior, sigma_prior, mu_mkt, sigma_mkt)
    
    # Should be closer to market estimate
    assert abs(mu_post - mu_mkt) < abs(mu_post - mu_prior)


def test_context_aware_model():
    """Test ContextAwareModel functionality."""
    model = ContextAwareModel()
    
    # Test psychology bias
    features = {
        'prime_time': True,
        'star_player': True,
        'recency_bias': 0.5
    }
    
    bias = model.apply_psychology_bias(features)
    assert bias > 0  # Should be positive bias
    
    # Test market line processing
    line = 250.0
    over_odds = -110
    under_odds = -110
    week = 1
    
    mu_mkt, sigma_mkt = model.process_market_line(line, over_odds, under_odds, week, features)
    
    # Should apply week 1 sigma inflation
    assert sigma_mkt > model.config['default_sigma_guess']
    
    # Should apply psychology bias
    assert mu_mkt != line  # Should be adjusted


def test_defensive_player_model():
    """Test DefensivePlayerModel functionality."""
    model = DefensivePlayerModel()
    
    # Test basic projection
    player_info = {
        'position': 'LB',
        'snap_share': 0.7
    }
    
    game_context = {
        'opponent_pace': 70,
        'opponent_pass_rate': 0.6
    }
    
    projections = model.project_defensive_stats(player_info, game_context)
    
    # Should have all expected stats
    expected_stats = ['tackles', 'assists', 'sacks', 'interceptions', 'fumble_recoveries']
    for stat in expected_stats:
        assert stat in projections
        assert projections[stat] >= 0
    
    # LB should have higher tackle projection than sack projection
    assert projections['tackles'] > projections['sacks']
    
    # Test matchup adjustments
    base_projections = projections.copy()
    matchup_info = {
        'opponent_oline_rating': 0.8,  # Good OLine
        'opponent_qb_mobility': 0.3,   # Pocket passer
        'weather_conditions': 'clear'
    }
    
    adjusted = model.apply_matchup_adjustments(base_projections, matchup_info)
    
    # Good OLine should reduce sacks
    assert adjusted['sacks'] < base_projections['sacks']


def test_defensive_position_factors():
    """Test that different defensive positions have appropriate factors."""
    model = DefensivePlayerModel()
    
    # Test DE vs CB projections
    de_info = {'position': 'DE', 'snap_share': 0.6}
    cb_info = {'position': 'CB', 'snap_share': 0.6}
    
    game_context = {'opponent_pace': 65, 'opponent_pass_rate': 0.6}
    
    de_proj = model.project_defensive_stats(de_info, game_context)
    cb_proj = model.project_defensive_stats(cb_info, game_context)
    
    # DE should have more sacks, CB should have fewer
    assert de_proj['sacks'] > cb_proj['sacks']
    
    # CB should have fewer tackles than DE
    assert cb_proj['tackles'] < de_proj['tackles']


def test_weather_impact():
    """Test weather impact on defensive projections."""
    model = DefensivePlayerModel()
    
    player_info = {'position': 'LB', 'snap_share': 0.7}
    game_context = {'opponent_pace': 65, 'opponent_pass_rate': 0.6}
    
    base_projections = model.project_defensive_stats(player_info, game_context)
    
    # Test bad weather
    bad_weather_matchup = {
        'opponent_oline_rating': 0.5,
        'opponent_qb_mobility': 0.5,
        'weather_conditions': 'rain'
    }
    
    weather_adjusted = model.apply_matchup_adjustments(base_projections, bad_weather_matchup)
    
    # Bad weather should increase tackles (more running)
    assert weather_adjusted['tackles'] > base_projections['tackles']
    
    # Bad weather should decrease sacks (less passing)
    assert weather_adjusted['sacks'] < base_projections['sacks']
