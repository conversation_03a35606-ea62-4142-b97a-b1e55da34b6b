"""Tests for Sleeper API integration."""

import json
import pytest
import pandas as pd
from unittest.mock import patch, mock_open, MagicMock
from pathlib import Path
from datetime import datetime, timezone

from src.proj.sleeper import (
    get_players, get_state, get_trending, load_cached_players,
    normalize_sleeper_team_name
)
from src.proj.joiners import (
    normalize_player_name, find_sleeper_match, backfill_from_sleeper,
    get_sleeper_suggestions
)


@pytest.fixture
def mock_sleeper_players_response():
    """Mock Sleeper players API response."""
    return {
        "1": {
            "player_id": "1",
            "first_name": "<PERSON>",
            "last_name": "<PERSON>",
            "full_name": "<PERSON>",
            "team": "BUF",
            "position": "QB",
            "active": True
        },
        "2": {
            "player_id": "2",
            "first_name": "Stefon",
            "last_name": "Diggs",
            "full_name": "Stefon Diggs",
            "team": "HOU",
            "position": "WR",
            "active": True
        },
        "3": {
            "player_id": "3",
            "first_name": "<PERSON>",
            "last_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>",
            "full_name": "<PERSON>",
            "team": "<PERSON>",
            "position": "RB",
            "active": True
        }
    }


@pytest.fixture
def mock_sleeper_state_response():
    """Mock Sleeper state API response."""
    return {
        "season": "2024",
        "week": 1,
        "season_type": "regular",
        "display_week": 1
    }


@pytest.fixture
def mock_sleeper_trending_response():
    """Mock Sleeper trending API response."""
    return [
        {"player_id": "1", "count": 150},
        {"player_id": "2", "count": 120},
        {"player_id": "3", "count": 100}
    ]


@pytest.fixture
def sample_players_df():
    """Sample players DataFrame for testing."""
    return pd.DataFrame([
        {
            'player_name': 'Josh Allen',
            'team': 'BUF',
            'position': 'QB'
        },
        {
            'player_name': 'Stefon Diggs',
            'team': '',  # Missing team
            'position': 'WR'
        },
        {
            'player_name': 'C. McCaffrey',
            'team': 'SF',
            'position': ''  # Missing position
        },
        {
            'player_name': 'Unknown Player',
            'team': '',
            'position': ''
        }
    ])


class TestSleeperAPI:
    """Test Sleeper API functions."""
    
    @patch('src.proj.sleeper.requests.get')
    @patch('src.proj.sleeper.Path.mkdir')
    @patch('builtins.open', new_callable=mock_open)
    def test_get_players(self, mock_file, mock_mkdir, mock_get, mock_sleeper_players_response):
        """Test get_players function."""
        # Mock API response
        mock_response = MagicMock()
        mock_response.json.return_value = mock_sleeper_players_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Call function
        df = get_players()
        
        # Verify API call
        mock_get.assert_called_once_with(
            "https://api.sleeper.app/v1/players/nfl", 
            timeout=30
        )
        
        # Verify DataFrame structure
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == [
            'player_id', 'full_name', 'first_name', 'last_name', 
            'team', 'position', 'active'
        ]
        
        # Verify data content
        josh_allen = df[df['player_id'] == '1'].iloc[0]
        assert josh_allen['full_name'] == 'Josh Allen'
        assert josh_allen['team'] == 'BUF'
        assert josh_allen['position'] == 'QB'
        assert josh_allen['active'] == True
        
        # Verify caching
        mock_mkdir.assert_called_once()
        mock_file.assert_called_once()
    
    @patch('src.proj.sleeper.requests.get')
    def test_get_state(self, mock_get, mock_sleeper_state_response):
        """Test get_state function."""
        # Mock API response
        mock_response = MagicMock()
        mock_response.json.return_value = mock_sleeper_state_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Call function
        state = get_state()
        
        # Verify API call
        mock_get.assert_called_once_with(
            "https://api.sleeper.app/v1/state/nfl",
            timeout=30
        )
        
        # Verify response
        assert state['season'] == '2024'
        assert state['week'] == 1
        assert state['season_type'] == 'regular'
    
    @patch('src.proj.sleeper.requests.get')
    def test_get_trending(self, mock_get, mock_sleeper_trending_response):
        """Test get_trending function."""
        # Mock API response
        mock_response = MagicMock()
        mock_response.json.return_value = mock_sleeper_trending_response
        mock_response.raise_for_status.return_value = None
        mock_get.return_value = mock_response
        
        # Call function
        df = get_trending(kind="add", lookback_hours=24, limit=50)
        
        # Verify API call
        mock_get.assert_called_once_with(
            "https://api.sleeper.app/v1/players/nfl/trending/add",
            params={'lookback_hours': 24, 'limit': 50},
            timeout=30
        )
        
        # Verify DataFrame
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 3
        assert list(df.columns) == ['player_id', 'trend_type', 'count', 'lookback_hours']
        
        # Verify sorting (highest count first)
        assert df.iloc[0]['count'] == 150
        assert df.iloc[1]['count'] == 120
        assert df.iloc[2]['count'] == 100


class TestSleeperIntegration:
    """Test Sleeper integration with joiners."""
    
    def test_normalize_player_name(self):
        """Test player name normalization."""
        assert normalize_player_name("Josh Allen") == "josh allen"
        assert normalize_player_name("Christian McCaffrey Jr.") == "christian mccaffrey"
        assert normalize_player_name("D.K. Metcalf") == "d.k. metcalf"
        assert normalize_player_name("Calvin Ridley (WR)") == "calvin ridley"
        assert normalize_player_name("") == ""
    
    def test_normalize_sleeper_team_name(self):
        """Test team name normalization."""
        assert normalize_sleeper_team_name("BUF") == "BUF"
        assert normalize_sleeper_team_name("JAX") == "JAC"
        assert normalize_sleeper_team_name("WSH") == "WAS"
        assert normalize_sleeper_team_name("") == ""
        assert normalize_sleeper_team_name(None) == ""
    
    def test_find_sleeper_match(self):
        """Test finding Sleeper matches."""
        sleeper_df = pd.DataFrame([
            {'full_name': 'Josh Allen', 'team': 'BUF', 'position': 'QB'},
            {'full_name': 'Stefon Diggs', 'team': 'HOU', 'position': 'WR'},
            {'full_name': 'Christian McCaffrey', 'team': 'SF', 'position': 'RB'}
        ])
        
        # Exact match
        match = find_sleeper_match("Josh Allen", sleeper_df)
        assert match is not None
        assert match['full_name'] == 'Josh Allen'
        
        # Partial match
        match = find_sleeper_match("C. McCaffrey", sleeper_df, threshold=0.6)
        assert match is not None
        assert match['full_name'] == 'Christian McCaffrey'
        
        # No match
        match = find_sleeper_match("Unknown Player", sleeper_df)
        assert match is None
    
    @patch('src.proj.joiners.load_cached_players')
    def test_backfill_from_sleeper(self, mock_load_cached, sample_players_df):
        """Test backfilling missing data from Sleeper."""
        # Mock cached Sleeper data
        sleeper_df = pd.DataFrame([
            {'full_name': 'Stefon Diggs', 'team': 'HOU', 'position': 'WR', 'active': True},
            {'full_name': 'Christian McCaffrey', 'team': 'SF', 'position': 'RB', 'active': True}
        ])
        mock_load_cached.return_value = sleeper_df
        
        # Backfill missing data
        result_df = backfill_from_sleeper(sample_players_df)
        
        # Verify backfilling
        stefon_row = result_df[result_df['player_name'] == 'Stefon Diggs'].iloc[0]
        assert stefon_row['team'] == 'HOU'  # Should be backfilled
        
        mccaffrey_row = result_df[result_df['player_name'] == 'C. McCaffrey'].iloc[0]
        assert mccaffrey_row['position'] == 'RB'  # Should be backfilled
    
    @patch('src.proj.joiners.load_cached_players')
    def test_get_sleeper_suggestions(self, mock_load_cached):
        """Test getting Sleeper suggestions."""
        # Mock cached Sleeper data
        sleeper_df = pd.DataFrame([
            {'full_name': 'Josh Allen', 'team': 'BUF', 'position': 'QB', 'active': True},
            {'full_name': 'Josh Gordon', 'team': '', 'position': 'WR', 'active': False}
        ])
        mock_load_cached.return_value = sleeper_df
        
        # Get suggestions
        suggestions = get_sleeper_suggestions("Josh A", threshold=0.5)
        
        # Verify suggestions
        assert len(suggestions) > 0
        assert suggestions[0]['sleeper_name'] == 'Josh Allen'
        assert suggestions[0]['similarity_score'] > 0.5
        assert 'team' in suggestions[0]
        assert 'position' in suggestions[0]
        assert 'active' in suggestions[0]


class TestSleeperCaching:
    """Test Sleeper data caching."""
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('src.proj.sleeper.Path.exists')
    def test_load_cached_players_missing_file(self, mock_exists, mock_file):
        """Test loading cached players when file doesn't exist."""
        mock_exists.return_value = False
        
        result = load_cached_players()
        assert result is None
    
    @patch('builtins.open', new_callable=mock_open)
    @patch('src.proj.sleeper.Path.exists')
    def test_load_cached_players_old_cache(self, mock_exists, mock_file):
        """Test loading cached players when cache is too old."""
        mock_exists.return_value = True

        # Mock old timestamp (more than 24 hours ago)
        from datetime import timedelta
        old_time = datetime.now(timezone.utc) - timedelta(hours=25)
        old_timestamp = old_time.isoformat()

        cache_data = {
            "timestamp": old_timestamp,
            "data": {"1": {"full_name": "Test Player", "position": "QB"}}
        }

        mock_file.return_value.__enter__.return_value.read.return_value = json.dumps(cache_data)

        result = load_cached_players()
        assert result is None  # Should return None for old cache
