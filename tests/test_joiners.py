"""Tests for joiners and reconciliation functionality."""

import pytest
import pandas as pd
import numpy as np
from src.proj.joiners import (
    map_team_props_to_games, reconcile_player_team_totals,
    apply_defensive_pressure_adjustments, compute_target_share_constraints,
    validate_reconciliation
)


@pytest.fixture
def sample_games_df():
    """Sample games DataFrame."""
    return pd.DataFrame({
        'home_team': ['KC', 'BUF'],
        'away_team': ['BUF', 'MIA'],
        'spread': [-3.0, -7.0],
        'total': [53.0, 47.0]
    })


@pytest.fixture
def sample_team_props_df():
    """Sample team props DataFrame."""
    return pd.DataFrame({
        'team': ['KC', 'KC', 'BUF', 'BUF', 'BUF', 'MIA'],
        'opponent': ['BUF', 'BUF', 'KC', '<PERSON>', 'MI<PERSON>', 'BUF'],
        'market': ['team_total', 'pass_yds', 'team_total', 'pass_yds', 'pass_yds', 'team_total'],
        'line': [28.5, 275.5, 24.5, 250.5, 220.0, 20.5],
        'over_odds': [-110, -110, -110, -110, -110, -110],
        'under_odds': [-110, -110, -110, -110, -110, -110],
        'book': ['DraftKings'] * 6,
        'timestamp_iso': ['2024-01-01T12:00:00Z'] * 6
    })


@pytest.fixture
def sample_player_projections():
    """Sample player projections DataFrame."""
    return pd.DataFrame({
        'player_name': ['Patrick Mahomes', 'Josh Allen', 'Tyreek Hill', 'Stefon Diggs'],
        'team': ['KC', 'BUF', 'KC', 'BUF'],
        'position': ['QB', 'QB', 'WR', 'WR'],
        'game_id': ['KC@BUF', 'KC@BUF', 'KC@BUF', 'KC@BUF'],
        'pass_yds_proj': [280.0, 260.0, 0.0, 0.0],
        'rush_yds_proj': [25.0, 45.0, 5.0, 2.0],
        'rec_yds_proj': [0.0, 0.0, 95.0, 85.0],
        'opponent': ['BUF', 'KC', 'BUF', 'KC']
    })


@pytest.fixture
def sample_player_roles():
    """Sample player roles DataFrame."""
    return pd.DataFrame({
        'player_name': ['Tyreek Hill', 'Stefon Diggs', 'Travis Kelce'],
        'team': ['KC', 'BUF', 'KC'],
        'position': ['WR', 'WR', 'TE'],
        'expected_snap_pct': [0.85, 0.80, 0.75],
        'role_notes': ['WR1, outside', 'WR1, outside', 'TE1, primary'],
        'target_share_prior': [0.25, 0.22, 0.18]
    })


def test_map_team_props_to_games(sample_games_df, sample_team_props_df):
    """Test mapping team props to games."""
    team_stats = map_team_props_to_games(sample_team_props_df, sample_games_df)
    
    # Should have entries for each team in each game
    assert len(team_stats) > 0
    
    # Should have game_id column
    assert 'game_id' in team_stats.columns
    
    # Should have implied stats
    assert 'team_total_implied' in team_stats.columns or 'pass_yds_implied' in team_stats.columns
    
    # Check specific team
    kc_stats = team_stats[team_stats['team'] == 'KC']
    assert len(kc_stats) > 0
    
    # Should have opponent info
    assert 'opponent' in team_stats.columns
    assert 'is_home' in team_stats.columns


def test_reconcile_player_team_totals(sample_player_projections, sample_team_props_df, sample_games_df):
    """Test player-team reconciliation."""
    # First map team props to games
    team_stats = map_team_props_to_games(sample_team_props_df, sample_games_df)
    
    # Reconcile projections
    adjusted_projections = reconcile_player_team_totals(
        sample_player_projections, team_stats
    )
    
    # Should return DataFrame with same shape
    assert adjusted_projections.shape == sample_player_projections.shape
    
    # Should have same columns
    assert list(adjusted_projections.columns) == list(sample_player_projections.columns)
    
    # Projections should be adjusted (not identical to original)
    # At least some values should be different
    differences = (adjusted_projections != sample_player_projections).any().any()
    # Note: This might not always be true if no adjustments are needed
    # So we'll just check that the function runs without error


def test_apply_defensive_pressure_adjustments(sample_player_projections):
    """Test defensive pressure adjustments."""
    # Create team stats with defensive metrics
    team_stats = pd.DataFrame({
        'team': ['KC', 'BUF'],
        'game_id': ['KC@BUF', 'KC@BUF'],
        'team_sacks_implied': [2.5, 3.5],
        'qb_sacks_taken_implied': [2.0, 3.0]
    })
    
    adjusted_projections = apply_defensive_pressure_adjustments(
        sample_player_projections, team_stats
    )
    
    # Should return DataFrame with same shape
    assert adjusted_projections.shape == sample_player_projections.shape
    
    # QB projections should be adjusted based on pressure
    qb_projections = adjusted_projections[adjusted_projections['position'] == 'QB']
    assert len(qb_projections) > 0


def test_compute_target_share_constraints(sample_team_props_df, sample_games_df, sample_player_roles):
    """Test target share constraint computation."""
    team_stats = map_team_props_to_games(sample_team_props_df, sample_games_df)
    
    constraints = compute_target_share_constraints(team_stats, sample_player_roles)
    
    # Should return dictionary
    assert isinstance(constraints, dict)
    
    # Should have entries for teams with passing data
    assert len(constraints) > 0
    
    # Each constraint should have expected structure
    for key, constraint in constraints.items():
        assert 'estimated_targets' in constraint
        assert 'target_distribution' in constraint
        assert isinstance(constraint['target_distribution'], dict)


def test_validate_reconciliation(sample_player_projections):
    """Test reconciliation validation."""
    # Create slightly adjusted projections
    adjusted_projections = sample_player_projections.copy()
    adjusted_projections['pass_yds_proj'] *= 1.05  # 5% increase
    
    # Create team constraints
    team_constraints = pd.DataFrame({
        'team': ['KC', 'BUF'],
        'game_id': ['KC@BUF', 'KC@BUF'],
        'pass_yds_implied': [275.0, 255.0]
    })
    
    report = validate_reconciliation(
        sample_player_projections, adjusted_projections, team_constraints
    )
    
    # Should return validation report
    assert isinstance(report, dict)
    assert 'passed' in report
    assert 'warnings' in report
    assert 'errors' in report
    assert 'adjustments_summary' in report
    
    # Should be boolean
    assert isinstance(report['passed'], bool)
    
    # Should have lists
    assert isinstance(report['warnings'], list)
    assert isinstance(report['errors'], list)


def test_reconciliation_with_large_adjustments():
    """Test validation catches large adjustments."""
    original = pd.DataFrame({
        'player_name': ['Test Player'],
        'team': ['TEST'],
        'position': ['QB'],
        'game_id': ['TEST@TEST'],
        'pass_yds_proj': [250.0],
        'rush_yds_proj': [20.0],
        'rec_yds_proj': [0.0]
    })
    
    # Create adjusted with large change
    adjusted = original.copy()
    adjusted['pass_yds_proj'] = [400.0]  # 60% increase
    
    team_constraints = pd.DataFrame({
        'team': ['TEST'],
        'game_id': ['TEST@TEST'],
        'pass_yds_implied': [250.0]
    })
    
    report = validate_reconciliation(original, adjusted, team_constraints, tolerance=0.15)
    
    # Should flag large adjustment
    assert len(report['warnings']) > 0 or len(report['errors']) > 0
    
    # Might fail validation if adjustment is too large
    if len(report['errors']) > 0:
        assert not report['passed']


def test_target_share_distribution():
    """Test target share distribution logic."""
    # Create team stats with passing data
    team_stats = pd.DataFrame({
        'team': ['KC'],
        'game_id': ['KC@BUF'],
        'pass_yds_implied': [280.0]
    })
    
    # Create player roles
    player_roles = pd.DataFrame({
        'player_name': ['WR1', 'WR2', 'TE1', 'RB1'],
        'team': ['KC', 'KC', 'KC', 'KC'],
        'position': ['WR', 'WR', 'TE', 'RB'],
        'expected_snap_pct': [0.85, 0.70, 0.75, 0.60],
        'role_notes': ['WR1', 'WR2', 'TE1', 'RB1']
    })
    
    constraints = compute_target_share_constraints(team_stats, player_roles)
    
    # Should have constraint for KC
    kc_constraint = None
    for key, constraint in constraints.items():
        if 'KC' in key:
            kc_constraint = constraint
            break
    
    assert kc_constraint is not None
    
    # Target distribution should sum to reasonable total (less than 1.0)
    total_share = sum(kc_constraint['target_distribution'].values())
    assert 0.5 < total_share < 1.0
    
    # WR1 should have highest share
    wr1_share = kc_constraint['target_distribution'].get('WR1', 0)
    other_shares = [share for player, share in kc_constraint['target_distribution'].items() 
                   if player != 'WR1']
    
    if other_shares:
        assert wr1_share >= max(other_shares)


def test_pressure_adjustment_direction():
    """Test that pressure adjustments go in the right direction."""
    # Create QB projection
    qb_projection = pd.DataFrame({
        'player_name': ['Test QB'],
        'team': ['KC'],
        'position': ['QB'],
        'game_id': ['KC@BUF'],
        'opponent': ['BUF'],
        'pass_yds_proj': [250.0],
        'rush_yds_proj': [20.0]
    })
    
    # High pressure defense
    high_pressure_stats = pd.DataFrame({
        'team': ['BUF'],  # Opponent defense
        'game_id': ['KC@BUF'],
        'team_sacks_implied': [4.0]  # High sacks
    })
    
    adjusted = apply_defensive_pressure_adjustments(qb_projection, high_pressure_stats)
    
    # High pressure should reduce passing stats
    assert adjusted.iloc[0]['pass_yds_proj'] <= qb_projection.iloc[0]['pass_yds_proj']
    
    # High pressure might increase rushing (scrambling)
    # This relationship might be implemented differently, so we'll just check it runs
