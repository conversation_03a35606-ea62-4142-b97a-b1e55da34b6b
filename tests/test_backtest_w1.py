"""
Tests for Week 1 backtest functionality.
"""

import pytest
import pandas as pd
import numpy as np
import json
import tempfile
from pathlib import Path
from unittest.mock import patch, mock_open

from src.proj.backtest import (
    detect_columns, load_fc_week1_2024, compute_residuals, 
    summarize_residuals, build_residual_adjusters, apply_week1_nudges
)


class TestDetectColumns:
    """Test column detection functionality."""
    
    def test_detect_standard_columns(self):
        """Test detection of standard column names."""
        df = pd.DataFrame({
            'Player': ['<PERSON>'],
            'Team': ['BUF'],
            'Pos': ['QB'],
            'Projection': [22.5],
            'Score': [28.2]
        })
        
        mapping = detect_columns(df)
        
        assert mapping['player'] == 'Player'
        assert mapping['team'] == 'Team'
        assert mapping['pos'] == 'Pos'
        assert mapping['proj'] == 'Projection'
        assert mapping['actual'] == 'Score'
    
    def test_detect_variant_columns(self):
        """Test detection of column name variants."""
        df = pd.DataFrame({
            'player_name': ['<PERSON>'],
            'tm': ['BUF'],
            'position': ['QB'],
            'proj_mean': [22.5],
            'fpts': [28.2]
        })
        
        mapping = detect_columns(df)
        
        assert mapping['player'] == 'player_name'
        assert mapping['team'] == 'tm'
        assert mapping['pos'] == 'position'
        assert mapping['proj'] == 'proj_mean'
        assert mapping['actual'] == 'fpts'
    
    def test_case_insensitive_detection(self):
        """Test case-insensitive column detection."""
        df = pd.DataFrame({
            'PLAYER': ['Josh Allen'],
            'PROJECTION': [22.5],
            'SCORE': [28.2]
        })
        
        mapping = detect_columns(df)
        
        assert mapping['player'] == 'PLAYER'
        assert mapping['proj'] == 'PROJECTION'
        assert mapping['actual'] == 'SCORE'


class TestComputeResiduals:
    """Test residual computation."""
    
    def test_basic_residuals(self):
        """Test basic residual computation."""
        df = pd.DataFrame({
            'player_name': ['Player A', 'Player B', 'Player C'],
            'pos': ['QB', 'RB', 'WR'],
            'exp_proj': [20.0, 15.0, 12.0],
            'actual_fp': [25.0, 10.0, 18.0]
        })
        
        result = compute_residuals(df)
        
        expected_errors = [5.0, -5.0, 6.0]
        assert result['error'].tolist() == expected_errors
        assert result['abs_error'].tolist() == [5.0, 5.0, 6.0]
        assert 'pos_group' in result.columns
        assert 'archetype' in result.columns
    
    def test_winsorization(self):
        """Test error winsorization."""
        # Create data with extreme outliers
        errors = [-50, -10, -5, 0, 5, 10, 50]  # Extreme values at ends
        df = pd.DataFrame({
            'player_name': [f'Player {i}' for i in range(len(errors))],
            'pos': ['QB'] * len(errors),
            'exp_proj': [20.0] * len(errors),
            'actual_fp': [20.0 + e for e in errors]
        })
        
        result = compute_residuals(df)
        
        # Check that extreme values are clipped
        winsorized_errors = result['error_winsorized'].tolist()
        assert min(winsorized_errors) > -50  # Should be clipped
        assert max(winsorized_errors) < 50   # Should be clipped
    
    def test_archetype_assignment(self):
        """Test archetype assignment for different positions."""
        df = pd.DataFrame({
            'player_name': ['WR1', 'WR2', 'RB1', 'RB2'],
            'pos': ['WR', 'WR', 'RB', 'RB'],
            'exp_proj': [18.0, 8.0, 16.0, 6.0],  # High and low projections
            'actual_fp': [20.0, 10.0, 18.0, 8.0]
        })
        
        result = compute_residuals(df)
        
        # Check that archetypes are assigned
        wr_archetypes = result[result['pos_group'] == 'WR']['archetype'].unique()
        rb_archetypes = result[result['pos_group'] == 'RB']['archetype'].unique()
        
        assert 'WR_alpha' in wr_archetypes or 'WR_depth' in wr_archetypes
        assert 'RB_feature' in rb_archetypes or 'RB_committee' in rb_archetypes


class TestBuildResidualAdjusters:
    """Test adjuster building."""
    
    def test_basic_adjusters(self):
        """Test basic adjuster creation."""
        summary = {
            'global': {'mean_error': 2.0, 'count': 100},
            'by_pos': {
                'QB': {'mean_error': 1.5, 'count': 20},
                'RB': {'mean_error': -1.0, 'count': 30}
            }
        }
        
        adjusters = build_residual_adjusters(summary)
        
        # Check that adjusters are created for each group
        assert 'global' in adjusters
        assert 'QB' in adjusters
        assert 'RB' in adjusters
        
        # Check QB adjusters
        qb_adj = adjusters['QB']
        assert 'mu_delta' in qb_adj
        assert 'sigma_mult' in qb_adj
        assert abs(qb_adj['mu_delta']) <= 0.6  # Should respect MU_CAP
        assert 1.0 <= qb_adj['sigma_mult'] <= 1.10  # Should respect SIGMA_CAP
    
    def test_mu_cap_enforcement(self):
        """Test that mu_delta respects the cap."""
        summary = {
            'by_pos': {
                'QB': {'mean_error': 10.0, 'count': 20}  # Very large error
            }
        }
        
        adjusters = build_residual_adjusters(summary, mu_cap=0.5)
        
        qb_adj = adjusters['QB']
        assert abs(qb_adj['mu_delta']) <= 0.5
    
    def test_sigma_cap_enforcement(self):
        """Test that sigma_mult respects the cap."""
        summary = {
            'by_pos': {
                'QB': {'mean_error': 20.0, 'count': 20}  # Very large error
            }
        }
        
        adjusters = build_residual_adjusters(summary, sigma_cap=1.05)
        
        qb_adj = adjusters['QB']
        assert qb_adj['sigma_mult'] <= 1.05
    
    def test_small_sample_filtering(self):
        """Test that groups with small samples are filtered out."""
        summary = {
            'by_pos': {
                'QB': {'mean_error': 2.0, 'count': 2},  # Too small
                'RB': {'mean_error': 1.0, 'count': 10}  # Large enough
            }
        }
        
        adjusters = build_residual_adjusters(summary)
        
        assert 'QB' not in adjusters  # Should be filtered out
        assert 'RB' in adjusters      # Should be included


class TestApplyWeek1Nudges:
    """Test nudge application."""
    
    def test_basic_nudge_application(self):
        """Test basic nudge application."""
        player_row = pd.Series({
            'pos_group': 'QB',
            'archetype': 'QB'
        })
        
        adjusters = {
            'QB': {'mu_delta': 0.5, 'sigma_mult': 1.05}
        }
        
        mu_adj, sigma_mult = apply_week1_nudges(player_row, adjusters)
        
        assert mu_adj == 0.5
        assert sigma_mult == 1.05
    
    def test_confidence_scaling(self):
        """Test that confidence scales nudges appropriately."""
        player_row = pd.Series({
            'pos_group': 'QB',
            'archetype': 'QB'
        })
        
        adjusters = {
            'QB': {'mu_delta': 1.0, 'sigma_mult': 1.10}
        }
        
        # Low confidence should scale down nudges
        mu_adj, sigma_mult = apply_week1_nudges(player_row, adjusters, confidence=0.5)
        
        assert mu_adj == 0.5  # 1.0 * 0.5
        assert sigma_mult == 1.05  # 1.0 + (1.10 - 1.0) * 0.5
    
    def test_archetype_precedence(self):
        """Test that archetype takes precedence over position."""
        player_row = pd.Series({
            'pos_group': 'WR',
            'archetype': 'WR_alpha'
        })
        
        adjusters = {
            'WR': {'mu_delta': 0.2, 'sigma_mult': 1.02},
            'WR_alpha': {'mu_delta': 0.4, 'sigma_mult': 1.04}
        }
        
        mu_adj, sigma_mult = apply_week1_nudges(player_row, adjusters)
        
        # Should use WR_alpha, not WR
        assert mu_adj == 0.4
        assert sigma_mult == 1.04
    
    def test_fallback_to_global(self):
        """Test fallback to global adjusters."""
        player_row = pd.Series({
            'pos_group': 'UNKNOWN',
            'archetype': 'UNKNOWN'
        })
        
        adjusters = {
            'global': {'mu_delta': 0.1, 'sigma_mult': 1.01}
        }
        
        mu_adj, sigma_mult = apply_week1_nudges(player_row, adjusters)
        
        assert mu_adj == 0.1
        assert sigma_mult == 1.01


class TestEndToEnd:
    """End-to-end integration tests."""
    
    def test_full_backtest_pipeline(self):
        """Test the complete backtest pipeline."""
        # Create sample data
        data = {
            'Player': ['Josh Allen', 'Derrick Henry', 'Tyreek Hill'],
            'Pos': ['QB', 'RB', 'WR'],
            'Team': ['BUF', 'TEN', 'MIA'],
            'Projection': [22.0, 18.0, 16.0],
            'Score': [28.0, 15.0, 20.0]
        }
        df = pd.DataFrame(data)
        
        # Save to temporary CSV
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as f:
            df.to_csv(f.name, index=False)
            temp_path = f.name
        
        try:
            # Load and process
            loaded_df = load_fc_week1_2024(temp_path)
            residuals_df = compute_residuals(loaded_df)
            summary = summarize_residuals(residuals_df)
            adjusters = build_residual_adjusters(summary)
            
            # Verify results
            assert len(loaded_df) == 3
            assert 'error' in residuals_df.columns
            assert 'global' in summary
            assert len(adjusters) > 0
            
            # Test nudge application
            for _, player in residuals_df.iterrows():
                mu_adj, sigma_mult = apply_week1_nudges(player, adjusters)
                assert abs(mu_adj) <= 0.6  # Respect MU_CAP
                assert 1.0 <= sigma_mult <= 1.10  # Respect SIGMA_CAP
        
        finally:
            # Clean up
            Path(temp_path).unlink()
