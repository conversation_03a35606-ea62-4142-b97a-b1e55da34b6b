"""Tests for fetch_odds module."""

import pytest
import pandas as pd
import os
from unittest.mock import patch, MagicMock
from src.proj.fetch_odds import implied_team_totals, fetch_secure_data, fetch_weather_data


def test_implied_team_totals():
    """Test implied team totals calculation."""
    # Mock odds data structure
    mock_odds_data = [
        {
            'id': 'game1',
            'sport_key': 'americanfootball_nfl',
            'sport_title': 'NFL',
            'commence_time': '2024-01-01T18:00:00Z',
            'home_team': 'Kansas City Chiefs',
            'away_team': 'Buffalo Bills',
            'bookmakers': [
                {
                    'key': 'draftkings',
                    'title': 'DraftKings',
                    'markets': [
                        {
                            'key': 'spreads',
                            'outcomes': [
                                {
                                    'name': 'Kansas City Chiefs',
                                    'price': -110,
                                    'point': -3.0
                                },
                                {
                                    'name': 'Buffalo Bills',
                                    'price': -110,
                                    'point': 3.0
                                }
                            ]
                        },
                        {
                            'key': 'totals',
                            'outcomes': [
                                {
                                    'name': 'Over',
                                    'price': -110,
                                    'point': 50.5
                                },
                                {
                                    'name': 'Under',
                                    'price': -110,
                                    'point': 50.5
                                }
                            ]
                        }
                    ]
                }
            ]
        }
    ]
    
    df = implied_team_totals(mock_odds_data)
    
    # Check DataFrame structure
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total', 
                       'home_implied_total', 'away_implied_total']
    assert all(col in df.columns for col in expected_columns)
    
    # Check data
    assert len(df) == 1
    row = df.iloc[0]
    
    assert row['home_team'] == 'Kansas City Chiefs'
    assert row['away_team'] == 'Buffalo Bills'
    assert row['spread'] == -3.0  # Chiefs favored by 3
    assert row['total'] == 50.5
    
    # Check implied totals calculation
    # Total = 50.5, Spread = -3.0 (Chiefs favored)
    # Home implied = 50.5/2 + (-3.0)/2 = 25.25 - 1.5 = 23.75
    # Away implied = 50.5/2 - (-3.0)/2 = 25.25 + 1.5 = 26.75
    assert abs(row['home_implied_total'] - 23.75) < 0.01
    assert abs(row['away_implied_total'] - 26.75) < 0.01


def test_implied_team_totals_empty():
    """Test implied team totals with empty data."""
    df = implied_team_totals([])
    
    expected_columns = ['home_team', 'away_team', 'commence_time', 'spread', 'total', 
                       'home_implied_total', 'away_implied_total']
    assert all(col in df.columns for col in expected_columns)
    assert len(df) == 0


def test_implied_team_totals_missing_odds():
    """Test implied team totals with missing odds data."""
    mock_odds_data = [
        {
            'id': 'game1',
            'sport_key': 'americanfootball_nfl',
            'sport_title': 'NFL',
            'commence_time': '2024-01-01T18:00:00Z',
            'home_team': 'Team A',
            'away_team': 'Team B',
            'bookmakers': []  # No bookmakers
        }
    ]
    
    df = implied_team_totals(mock_odds_data)
    
    assert len(df) == 1
    row = df.iloc[0]
    
    assert row['home_team'] == 'Team A'
    assert row['away_team'] == 'Team B'
    assert pd.isna(row['spread'])
    assert pd.isna(row['total'])
    assert pd.isna(row['home_implied_total'])
    assert pd.isna(row['away_implied_total'])


@patch.dict(os.environ, {'API_USERNAME': 'test_user', 'API_PASSWORD': 'test_pass'})
@patch('src.proj.fetch_odds.requests.get')
def test_fetch_secure_data_success(mock_get):
    """Test successful secure data fetch."""
    # Mock successful response
    mock_response = MagicMock()
    mock_response.json.return_value = {'data': 'test_data'}
    mock_response.raise_for_status.return_value = None
    mock_get.return_value = mock_response

    result = fetch_secure_data('https://api.example.com/data')

    # Verify the request was made with correct auth
    mock_get.assert_called_once()
    call_args = mock_get.call_args
    assert call_args[0][0] == 'https://api.example.com/data'
    assert call_args[1]['timeout'] == 30
    assert call_args[1]['auth'].username == 'test_user'
    assert call_args[1]['auth'].password == 'test_pass'

    assert result == {'data': 'test_data'}


def test_fetch_secure_data_missing_credentials():
    """Test fetch_secure_data with missing credentials."""
    with patch.dict(os.environ, {}, clear=True):
        with pytest.raises(ValueError, match="API_USERNAME and API_PASSWORD must be set"):
            fetch_secure_data('https://api.example.com/data')


@patch.dict(os.environ, {'API_USERNAME': 'test_user', 'API_PASSWORD': 'test_pass'})
@patch('src.proj.fetch_odds.requests.get')
def test_fetch_secure_data_http_error(mock_get):
    """Test fetch_secure_data with HTTP error."""
    # Mock HTTP error
    mock_response = MagicMock()
    mock_response.raise_for_status.side_effect = Exception("HTTP 401 Unauthorized")
    mock_get.return_value = mock_response

    with pytest.raises(Exception, match="Failed to fetch data"):
        fetch_secure_data('https://api.example.com/data')


@patch('src.proj.fetch_odds.fetch_secure_data')
def test_fetch_weather_data(mock_fetch_secure):
    """Test weather data fetch."""
    mock_fetch_secure.return_value = {
        'data': [
            {
                'parameter': 't_2m:C',
                'coordinates': [{'lat': 40.7589, 'lon': -73.9851, 'dates': [{'date': '2024-01-15T12:00:00Z', 'value': 5.2}]}]
            }
        ]
    }

    result = fetch_weather_data(40.7589, -73.9851, '2024-01-15')

    # Verify correct URL was constructed
    expected_url = "https://api.meteomatics.com/2024-01-15T12:00:00Z/t_2m:C,wind_speed_10m:ms,precip_1h:mm/40.7589,-73.9851/json"
    mock_fetch_secure.assert_called_once_with(expected_url)

    assert 'data' in result
