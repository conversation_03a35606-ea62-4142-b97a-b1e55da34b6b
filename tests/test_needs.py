"""Tests for needs detection system."""

import pytest
import pandas as pd
import tempfile
import os
from pathlib import Path
from src.proj.needs import NeedsDetector, MissingDataItem


@pytest.fixture
def sample_players_df():
    """Sample players DataFrame for testing."""
    return pd.DataFrame({
        'player_name': ['<PERSON>', '<PERSON><PERSON><PERSON>', '<PERSON>', '<PERSON>'],
        'position': ['QB', 'WR', 'TE', 'RB'],
        'team': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'salary': [9000, 8000, 7000, 6000]
    })


@pytest.fixture
def sample_team_props_df():
    """Sample team props DataFrame for testing."""
    return pd.DataFrame({
        'team': ['KC', 'KC', 'BUF', 'BUF'],
        'opponent': ['BUF', 'BUF', '<PERSON>', '<PERSON>'],
        'market': ['team_total', 'pass_yds', 'team_total', 'pass_yds'],
        'line': [28.5, 275.5, 24.5, 250.5],
        'over_odds': [-110, -110, -110, -110],
        'under_odds': [-110, -110, -110, -110],
        'book': ['DraftKings', 'DraftKings', 'DraftKings', 'DraftKings'],
        'timestamp_iso': ['2024-01-01T12:00:00Z'] * 4
    })


@pytest.fixture
def sample_roles_df():
    """Sample roles DataFrame for testing."""
    return pd.DataFrame({
        'player_name': ['Tyreek Hill', 'Travis Kelce'],
        'team': ['KC', 'KC'],
        'position': ['WR', 'TE'],
        'expected_snap_pct': [0.85, 0.75],
        'role_notes': ['WR1, outside', 'TE1, primary target'],
        'slot_outside_flag': ['outside', 'slot'],
        'target_share_prior': [0.25, 0.20]
    })


def test_needs_detector_initialization():
    """Test NeedsDetector initialization."""
    detector = NeedsDetector()
    
    # Should have position requirements
    assert 'QB' in detector.position_requirements
    assert 'WR' in detector.position_requirements
    assert 'RB' in detector.position_requirements
    assert 'TE' in detector.position_requirements
    assert 'DST' in detector.position_requirements
    
    # Should have game level requirements
    assert 'critical' in detector.game_level_requirements
    assert 'spread' in detector.game_level_requirements['critical']


def test_detect_missing_basic(sample_players_df):
    """Test basic missing data detection."""
    detector = NeedsDetector()
    
    # With minimal data, should find many missing items
    missing_items = detector.detect_missing(sample_players_df)
    
    assert len(missing_items) > 0
    
    # Should find missing team props
    team_prop_missing = [item for item in missing_items if item.category == 'team_props']
    assert len(team_prop_missing) > 0
    
    # Should find missing player data
    player_missing = [item for item in missing_items if item.category == 'player_data']
    assert len(player_missing) > 0


def test_detect_missing_with_team_props(sample_players_df, sample_team_props_df):
    """Test missing data detection with team props provided."""
    detector = NeedsDetector()
    
    missing_items = detector.detect_missing(
        sample_players_df, 
        team_props_df=sample_team_props_df
    )
    
    # Should have fewer team prop issues
    team_prop_missing = [item for item in missing_items if item.category == 'team_props']
    
    # Should still have some missing (like rush_yds, team_sacks)
    assert len(team_prop_missing) > 0
    
    # But should have team_total and pass_yds covered
    missing_markets = [item.description for item in team_prop_missing]
    team_total_missing = any('team_total' in desc for desc in missing_markets)
    pass_yds_missing = any('pass_yds' in desc for desc in missing_markets)
    
    # These should be covered by our sample data
    assert not team_total_missing
    assert not pass_yds_missing


def test_detect_missing_with_roles(sample_players_df, sample_roles_df):
    """Test missing data detection with roles provided."""
    detector = NeedsDetector()
    
    missing_items = detector.detect_missing(
        sample_players_df,
        roles_df=sample_roles_df
    )
    
    # Should have fewer player data issues for players with roles
    player_missing = [item for item in missing_items if item.category == 'player_data']
    
    # Tyreek Hill and Travis Kelce should have fewer missing items
    hill_missing = [item for item in player_missing if item.player_name == 'Tyreek Hill']
    kelce_missing = [item for item in player_missing if item.player_name == 'Travis Kelce']
    mahomes_missing = [item for item in player_missing if item.player_name == 'Patrick Mahomes']
    
    # Players with roles should have fewer missing items than those without
    assert len(hill_missing) < len(mahomes_missing)
    assert len(kelce_missing) < len(mahomes_missing)


def test_confidence_score(sample_players_df):
    """Test confidence score calculation."""
    detector = NeedsDetector()
    
    # Get missing items
    missing_items = detector.detect_missing(sample_players_df)
    
    # Calculate confidence for each player
    for _, player in sample_players_df.iterrows():
        confidence = detector.confidence_score(player, missing_items)
        
        # Should be between 0 and 1
        assert 0.0 <= confidence <= 1.0
        
        # With minimal data, confidence should be low
        assert confidence < 0.8


def test_confidence_score_with_complete_data(sample_players_df, sample_team_props_df, sample_roles_df):
    """Test confidence score with more complete data."""
    detector = NeedsDetector()
    
    # Create more complete player data
    complete_players = sample_players_df.copy()
    complete_players['team_total'] = [28.5, 28.5, 28.5, 28.5]
    complete_players['spread'] = [-3.0, -3.0, -3.0, -3.0]
    
    missing_items = detector.detect_missing(
        complete_players,
        team_props_df=sample_team_props_df,
        roles_df=sample_roles_df
    )
    
    # Players with roles should have higher confidence
    hill_confidence = detector.confidence_score(
        complete_players[complete_players['player_name'] == 'Tyreek Hill'].iloc[0],
        missing_items
    )
    
    mahomes_confidence = detector.confidence_score(
        complete_players[complete_players['player_name'] == 'Patrick Mahomes'].iloc[0],
        missing_items
    )
    
    # Hill has role data, so should have higher confidence
    assert hill_confidence > mahomes_confidence


def test_generate_templates():
    """Test template generation."""
    detector = NeedsDetector()
    
    with tempfile.TemporaryDirectory() as temp_dir:
        templates = detector.generate_templates(temp_dir)
        
        # Should create expected templates
        expected_templates = [
            'player_props_template.csv',
            'team_props_template.csv', 
            'roles_notes_template.csv',
            'games_template.csv'
        ]
        
        for template in expected_templates:
            assert template in templates
            assert Path(templates[template]).exists()
            
            # Check that files have headers
            df = pd.read_csv(templates[template])
            assert len(df.columns) > 0


def test_generate_needs_report(sample_players_df):
    """Test needs report generation."""
    detector = NeedsDetector()
    
    missing_items = detector.detect_missing(sample_players_df)
    report = detector.generate_needs_report(missing_items, sample_players_df)
    
    # Should be a string
    assert isinstance(report, str)
    
    # Should contain expected sections
    assert "# NFL Projections - Missing Data Report" in report
    assert "## Summary" in report
    assert "## Lowest Confidence Players" in report
    
    # Should contain player names
    assert "Patrick Mahomes" in report
    assert "Tyreek Hill" in report


def test_priority_levels():
    """Test that different priority levels are assigned correctly."""
    detector = NeedsDetector()
    
    # Create player with QB position
    qb_player = pd.DataFrame({
        'player_name': ['Test QB'],
        'position': ['QB'],
        'team': ['TEST']
    })
    
    missing_items = detector.detect_missing(qb_player)
    
    # Should have critical, high, medium, and low priority items
    priorities = set(item.priority for item in missing_items)
    assert 'critical' in priorities
    assert 'high' in priorities
    
    # Critical items should include team_total and spread for QB
    critical_items = [item for item in missing_items if item.priority == 'critical']
    critical_descriptions = [item.description for item in critical_items]
    
    # Should have team-level critical requirements
    assert any('team_total' in desc or 'spread' in desc for desc in critical_descriptions)


def test_template_mapping():
    """Test that requirements map to correct templates."""
    detector = NeedsDetector()
    
    # Test specific requirement mappings
    assert detector._get_template_for_requirement('target_share_prior') == 'roles_notes_template.csv'
    assert detector._get_template_for_requirement('slot_outside_flag') == 'roles_notes_template.csv'
    assert detector._get_template_for_requirement('team_total') == 'team_props_template.csv'
    assert detector._get_template_for_requirement('spread') == 'games_template.csv'
    
    # Unknown requirement should default to player props
    assert detector._get_template_for_requirement('unknown_req') == 'player_props_template.csv'
