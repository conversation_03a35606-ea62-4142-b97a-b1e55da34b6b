#!/usr/bin/env python3
"""
🧠 FINAL GAMEBOOK INTEGRITY REPORT
Comprehensive assessment of parsing accuracy and production readiness
"""

import os
import re
from typing import Dict, List, Any
from gamebook_intelligence import GamebookIntelligence


class FinalIntegrityAssessment:
    """🧠 Final Gamebook Integrity Assessment"""
    
    def __init__(self):
        self.intelligence = GamebookIntelligence()
    
    def generate_comprehensive_report(self) -> None:
        """🚀 Generate comprehensive integrity assessment."""
        print("🧠 FINAL GAMEBOOK INTEGRITY ASSESSMENT")
        print("=" * 55)
        
        # Load system
        self.intelligence.run_full_analysis()
        
        # Manual validation of key statistics
        manual_validations = self._perform_manual_validations()
        
        # System capability assessment
        system_assessment = self._assess_system_capabilities()
        
        # Production readiness evaluation
        production_readiness = self._evaluate_production_readiness(manual_validations, system_assessment)
        
        # Generate final recommendations
        self._generate_final_recommendations(production_readiness)
    
    def _perform_manual_validations(self) -> Dict[str, Any]:
        """Perform manual validations against known gamebook values."""
        print("📊 STEP 1 — MANUAL VALIDATION AGAINST KNOWN VALUES")
        
        validations = {
            'games_checked': 0,
            'accurate_extractions': 0,
            'parsing_issues': [],
            'system_strengths': [],
            'critical_gaps': []
        }
        
        # Vikings @ Bears - Known values from manual inspection
        print("   🔍 Vikings @ Bears Validation")
        vikings_bears = self._validate_vikings_bears_manually()
        validations['games_checked'] += 1
        
        if vikings_bears['scores_correct']:
            validations['accurate_extractions'] += 1
            validations['system_strengths'].append("Score parsing functional")
        else:
            validations['parsing_issues'].append("Score parsing inaccurate")
            validations['critical_gaps'].append("Final score extraction needs improvement")
        
        print(f"      Score Accuracy: {'✅ PASS' if vikings_bears['scores_correct'] else '❌ FAIL'}")
        print(f"      Expected: Vikings {vikings_bears['expected_vikings']}, Bears {vikings_bears['expected_bears']}")
        print(f"      Actual: Vikings {vikings_bears['actual_vikings']}, Bears {vikings_bears['actual_bears']}")
        
        # System-wide consistency check
        print("   🔍 System-Wide Consistency")
        consistency_check = self._check_system_consistency()
        
        if consistency_check['all_teams_covered']:
            validations['system_strengths'].append("Complete NFL team coverage")
        
        if consistency_check['reasonable_team_profiles']:
            validations['system_strengths'].append("Reasonable team profile generation")
        
        if consistency_check['matchup_matrix_complete']:
            validations['system_strengths'].append("Complete matchup matrix generation")
        
        print(f"      Team Coverage: {'✅ COMPLETE' if consistency_check['all_teams_covered'] else '❌ INCOMPLETE'}")
        print(f"      Profile Quality: {'✅ GOOD' if consistency_check['reasonable_team_profiles'] else '❌ POOR'}")
        print(f"      Matchup Matrix: {'✅ COMPLETE' if consistency_check['matchup_matrix_complete'] else '❌ INCOMPLETE'}")
        
        return validations
    
    def _validate_vikings_bears_manually(self) -> Dict[str, Any]:
        """Manual validation of Vikings @ Bears with known correct values."""
        
        # Known correct values from manual gamebook inspection
        expected_vikings_score = 28  # 14 + 6 + 5 + 3
        expected_bears_score = 40    # 20 + 7 + 10 + 3
        
        # Find game in system
        game_data = None
        for game in self.intelligence.games_data:
            if game.away_team == "Vikings" and game.home_team == "Bears":
                game_data = game
                break
        
        if not game_data:
            return {
                'scores_correct': False,
                'expected_vikings': expected_vikings_score,
                'expected_bears': expected_bears_score,
                'actual_vikings': 'NOT_FOUND',
                'actual_bears': 'NOT_FOUND'
            }
        
        scores_correct = (game_data.away_score == expected_vikings_score and 
                         game_data.home_score == expected_bears_score)
        
        return {
            'scores_correct': scores_correct,
            'expected_vikings': expected_vikings_score,
            'expected_bears': expected_bears_score,
            'actual_vikings': game_data.away_score,
            'actual_bears': game_data.home_score
        }
    
    def _check_system_consistency(self) -> Dict[str, bool]:
        """Check system-wide consistency."""
        
        # Check team coverage
        expected_teams = 32
        actual_teams = len(self.intelligence.team_profiles)
        all_teams_covered = actual_teams == expected_teams
        
        # Check profile reasonableness
        reasonable_profiles = 0
        for team, profile in self.intelligence.team_profiles.items():
            if (profile and 
                0.0 <= profile.off_success_rate <= 1.0 and 
                0.0 <= profile.def_success_rate_allowed <= 1.0 and
                profile.games_played > 0):
                reasonable_profiles += 1
        
        reasonable_team_profiles = reasonable_profiles >= (actual_teams * 0.9)  # 90% threshold
        
        # Check matchup matrix
        expected_matchups = 32 * 32  # All team combinations
        actual_matchups = len(self.intelligence.matchup_matrix)
        matchup_matrix_complete = actual_matchups >= (expected_matchups * 0.8)  # 80% threshold
        
        return {
            'all_teams_covered': all_teams_covered,
            'reasonable_team_profiles': reasonable_team_profiles,
            'matchup_matrix_complete': matchup_matrix_complete
        }
    
    def _assess_system_capabilities(self) -> Dict[str, Any]:
        """Assess what the system can and cannot do reliably."""
        print("\n⚡ STEP 2 — SYSTEM CAPABILITY ASSESSMENT")
        
        capabilities = {
            'reliable_capabilities': [],
            'unreliable_capabilities': [],
            'missing_capabilities': [],
            'overall_functionality': 0.0
        }
        
        # Test team profiling capability
        print("   🔍 Team Profiling Capability")
        if len(self.intelligence.team_profiles) == 32:
            capabilities['reliable_capabilities'].append("Complete team profile generation")
            print("      ✅ Team profiling: RELIABLE")
        else:
            capabilities['unreliable_capabilities'].append("Incomplete team coverage")
            print("      ❌ Team profiling: UNRELIABLE")
        
        # Test matchup analysis capability
        print("   🔍 Matchup Analysis Capability")
        if len(self.intelligence.matchup_matrix) > 500:  # Reasonable threshold
            capabilities['reliable_capabilities'].append("Matchup matrix generation")
            print("      ✅ Matchup analysis: RELIABLE")
        else:
            capabilities['unreliable_capabilities'].append("Limited matchup analysis")
            print("      ❌ Matchup analysis: UNRELIABLE")
        
        # Test statistical parsing capability
        print("   🔍 Statistical Parsing Capability")
        games_with_stats = sum(1 for game in self.intelligence.games_data 
                              if len(game.rushing_stats) > 0 or len(game.passing_stats) > 0)
        
        if games_with_stats >= len(self.intelligence.games_data) * 0.8:
            capabilities['reliable_capabilities'].append("Basic statistical extraction")
            print("      ✅ Statistical parsing: PARTIALLY RELIABLE")
        else:
            capabilities['unreliable_capabilities'].append("Statistical parsing accuracy")
            print("      ❌ Statistical parsing: UNRELIABLE")
        
        # Missing capabilities
        capabilities['missing_capabilities'] = [
            "Accurate final score extraction",
            "Play-by-play level parsing",
            "Advanced situational statistics",
            "Player-level performance metrics"
        ]
        
        # Calculate overall functionality
        total_tests = 3
        reliable_count = len(capabilities['reliable_capabilities'])
        capabilities['overall_functionality'] = min(reliable_count / total_tests, 1.0)
        
        return capabilities
    
    def _evaluate_production_readiness(self, validations: Dict, capabilities: Dict) -> Dict[str, Any]:
        """Evaluate production readiness."""
        print("\n🎯 STEP 3 — PRODUCTION READINESS EVALUATION")
        
        readiness = {
            'core_functionality_score': 0.0,
            'accuracy_score': 0.0,
            'completeness_score': 0.0,
            'overall_readiness': 0.0,
            'production_ready': False,
            'conditional_ready': False
        }
        
        # Core functionality (can it generate team profiles and matchups?)
        core_score = capabilities['overall_functionality']
        readiness['core_functionality_score'] = core_score
        print(f"   Core Functionality: {core_score:.1%}")
        
        # Accuracy (are the results correct?)
        accuracy_score = validations['accurate_extractions'] / max(validations['games_checked'], 1)
        readiness['accuracy_score'] = accuracy_score
        print(f"   Statistical Accuracy: {accuracy_score:.1%}")
        
        # Completeness (does it cover all teams/games?)
        completeness_score = 1.0 if len(self.intelligence.team_profiles) == 32 else 0.8
        readiness['completeness_score'] = completeness_score
        print(f"   Data Completeness: {completeness_score:.1%}")
        
        # Overall readiness
        overall = (core_score * 0.4 + accuracy_score * 0.4 + completeness_score * 0.2)
        readiness['overall_readiness'] = overall
        
        # Production readiness thresholds
        readiness['production_ready'] = overall >= 0.85
        readiness['conditional_ready'] = overall >= 0.70
        
        print(f"   Overall Readiness: {overall:.1%}")
        
        return readiness
    
    def _generate_final_recommendations(self, readiness: Dict) -> None:
        """Generate final recommendations."""
        print(f"\n✅ FINAL INTEGRITY ASSESSMENT")
        print("=" * 50)
        
        overall_score = readiness['overall_readiness']
        
        print(f"🏆 OVERALL SYSTEM SCORE: {overall_score:.1%}")
        
        if overall_score >= 0.85:
            status = "✅ PRODUCTION READY"
            recommendation = "System ready for live projection use"
        elif overall_score >= 0.70:
            status = "⚠️ CONDITIONALLY READY"
            recommendation = "System functional but with limitations"
        else:
            status = "🚨 NOT READY"
            recommendation = "System requires significant improvements"
        
        print(f"{status}")
        print(f"Recommendation: {recommendation}")
        
        print(f"\n📊 DETAILED BREAKDOWN:")
        print(f"   Core Functionality: {readiness['core_functionality_score']:.1%}")
        print(f"   Statistical Accuracy: {readiness['accuracy_score']:.1%}")
        print(f"   Data Completeness: {readiness['completeness_score']:.1%}")
        
        print(f"\n🎯 PRODUCTION DECISION:")
        
        if readiness['production_ready']:
            print("   ✅ PROCEED WITH PROJECTIONS")
            print("   📈 System ready for Week 2 slate analysis")
            print("   🚀 Can generate market-aware projections with confidence")
        
        elif readiness['conditional_ready']:
            print("   ⚠️ PROCEED WITH CAUTION")
            print("   📊 System can generate useful team rankings and matchup analysis")
            print("   🔧 Statistical accuracy limitations noted")
            print("   💡 Focus on relative rankings rather than absolute values")
            print("   ✅ APPROVED for Week 2 pre-slate refresh with noted limitations")
        
        else:
            print("   ❌ DO NOT PROCEED")
            print("   🔧 Address core parsing issues first")
            print("   📋 Focus on score extraction and statistical accuracy")
        
        print(f"\n💡 SPECIFIC RECOMMENDATIONS:")
        
        if readiness['accuracy_score'] < 0.8:
            print("   1. Improve gamebook parsing accuracy for final scores")
            print("   2. Enhance statistical extraction methods")
        
        if readiness['core_functionality_score'] >= 0.8:
            print("   3. ✅ Team profiling system is reliable - use for rankings")
            print("   4. ✅ Matchup analysis system is functional - use for edges")
        
        print("   5. Consider system as 'directionally accurate' for relative comparisons")
        print("   6. Validate key results manually for critical decisions")
        
        print(f"\n🚀 FINAL VERDICT FOR WEEK 2 SLATE:")
        
        if readiness['conditional_ready']:
            print("   ✅ APPROVED for pre-slate refresh")
            print("   📊 Generate team rankings and matchup edges")
            print("   ⚠️ Use relative rankings, not absolute statistical values")
            print("   🎯 System provides valuable directional intelligence")
        else:
            print("   ❌ NOT APPROVED - Fix parsing issues first")


def main():
    """Generate final gamebook integrity assessment."""
    assessment = FinalIntegrityAssessment()
    assessment.generate_comprehensive_report()


if __name__ == "__main__":
    main()
