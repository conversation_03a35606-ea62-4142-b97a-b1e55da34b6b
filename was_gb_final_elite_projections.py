#!/usr/bin/env python3
"""
WAS @ GB Final Elite Projections
Uses sophisticated multi-book odds analysis for maximum accuracy.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
from dataclasses import dataclass
import json


@dataclass
class FinalProjection:
    """Final projection with full breakdown."""
    player_name: str
    team: str
    position: str
    salary: int
    props_projection: float
    model_projection: float
    context_adjustment: float
    final_projection: float
    confidence: float
    value_score: float
    prop_details: Dict[str, Any]
    decision_factors: List[str]


class FinalEliteProjections:
    """Create final elite projections using all available data."""
    
    def __init__(self):
        self.game_context = {
            'total': 49.0,
            'spread': -3.5,  # GB favored
            'gb_implied_total': 26.25,
            'was_implied_total': 22.75,
            'weather_impact': 0.0,  # Good conditions
            'pace_factor': 1.05,  # Slightly above average
            'game_script': 'competitive'
        }
        
        # Weighting system: 50% props, 30% models, 20% context
        self.weights = {
            'props': 0.50,
            'models': 0.30,
            'context': 0.20
        }
    
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """Load all required data sources."""
        data = {}
        
        try:
            # DraftKings player pool
            data['dk'] = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-2_players.csv', skiprows=1)
            print(f"✅ Loaded {len(data['dk'])} DraftKings players")
            
            # Sophisticated props analysis
            data['props_detailed'] = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')
            data['props_summary'] = pd.read_csv('csvs/was_gb_player_projections_summary_20250911_2159.csv')
            print(f"✅ Loaded detailed props analysis for {len(data['props_summary'])} players")
            
            return data
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return {}
    
    def get_props_projection(self, player_name: str, props_summary: pd.DataFrame) -> Dict[str, Any]:
        """Get sophisticated props-based projection."""
        # Find player in props data (fuzzy matching)
        player_props = props_summary[
            props_summary['player_name'].str.contains(player_name, case=False, na=False) |
            props_summary['player_name'].str.replace(' ', '').str.contains(
                player_name.replace(' ', ''), case=False, na=False
            )
        ]
        
        if player_props.empty:
            return {
                'projection': 0,
                'confidence': 0,
                'prop_count': 0,
                'sharp_signals': 0,
                'line_disagreements': 0,
                'source': 'no_props'
            }
        
        player_data = player_props.iloc[0]
        
        return {
            'projection': player_data['total_projection'],
            'confidence': player_data['avg_confidence'],
            'prop_count': player_data['prop_count'],
            'sharp_signals': player_data['sharp_signals_count'],
            'line_disagreements': player_data['line_disagreements_count'],
            'source': 'multi_book_analysis'
        }
    
    def get_model_projection(self, player: pd.Series) -> Dict[str, Any]:
        """Get model-based projection (using FC as proxy)."""
        fc_proj = player.get('FC', 0)
        my_proj = player.get('My', 0)
        vegas_pts = player.get('VegasPts', 0)
        
        # Use best available model projection
        if pd.notna(fc_proj) and fc_proj > 0:
            projection = fc_proj
            source = 'fc_model'
            confidence = 0.7
        elif pd.notna(my_proj) and my_proj > 0:
            projection = my_proj
            source = 'my_model'
            confidence = 0.6
        elif pd.notna(vegas_pts) and vegas_pts > 0:
            projection = vegas_pts
            source = 'vegas_points'
            confidence = 0.5
        else:
            projection = self.get_position_baseline(player.get('Pos', 'FLEX'))
            source = 'position_baseline'
            confidence = 0.3
        
        return {
            'projection': projection,
            'confidence': confidence,
            'source': source
        }
    
    def get_position_baseline(self, position: str) -> float:
        """Get baseline projection by position."""
        baselines = {
            'QB': 18.0,
            'RB': 12.0,
            'WR': 10.0,
            'TE': 8.0,
            'K': 8.0,
            'DST': 6.0
        }
        return baselines.get(position, 8.0)
    
    def apply_context_adjustments(self, base_projection: float, player: pd.Series, 
                                 props_data: Dict[str, Any]) -> Dict[str, Any]:
        """Apply sophisticated context adjustments."""
        team = player.get('Team', '')
        position = player.get('Pos', '')
        
        adjustments = []
        total_adjustment = 1.0
        
        # Team total adjustment
        if team == 'GB':
            team_total = self.game_context['gb_implied_total']
            opponent_strength = 'average'  # WAS defense
        else:
            team_total = self.game_context['was_implied_total']
            opponent_strength = 'above_average'  # GB defense
        
        team_factor = team_total / 22.5  # League average
        total_adjustment *= (0.9 + 0.1 * team_factor)
        adjustments.append(f"Team total factor: {team_factor:.3f}")
        
        # Game script adjustments
        spread = self.game_context['spread']
        if position == 'QB':
            if (team == 'WAS' and spread > 3) or (team == 'GB' and spread < -7):
                # Trailing team or big favorite - more passing
                script_adj = 1.05
                adjustments.append("Game script: +5% (passing volume)")
            else:
                script_adj = 1.0
                adjustments.append("Game script: neutral")
            total_adjustment *= script_adj
            
        elif position == 'RB':
            if team == 'GB' and spread < -7:
                # Big favorite - more rushing
                script_adj = 1.1
                adjustments.append("Game script: +10% (rushing in blowout)")
            elif team == 'WAS' and spread > 7:
                # Big underdog - less rushing
                script_adj = 0.9
                adjustments.append("Game script: -10% (less rushing when trailing)")
            else:
                script_adj = 1.0
                adjustments.append("Game script: neutral")
            total_adjustment *= script_adj
        
        # Sharp money adjustments
        if props_data.get('sharp_signals', 0) > 0:
            sharp_adj = 1.03  # Small boost for sharp money agreement
            total_adjustment *= sharp_adj
            adjustments.append(f"Sharp money signals: +3% ({props_data['sharp_signals']} signals)")
        
        # Line disagreement penalty
        if props_data.get('line_disagreements', 0) > 2:
            disagreement_adj = 0.97  # Small penalty for high uncertainty
            total_adjustment *= disagreement_adj
            adjustments.append(f"Line disagreements: -3% ({props_data['line_disagreements']} disagreements)")
        
        # Weather (minimal impact for good conditions)
        weather_adj = 1.0 - self.game_context['weather_impact']
        total_adjustment *= weather_adj
        if weather_adj != 1.0:
            adjustments.append(f"Weather: {(weather_adj-1)*100:+.1f}%")
        
        final_projection = base_projection * total_adjustment
        
        return {
            'adjusted_projection': final_projection,
            'adjustment_factor': total_adjustment,
            'adjustments': adjustments
        }
    
    def create_final_projections(self) -> List[FinalProjection]:
        """Create final elite projections - ONLY for players with props data."""
        print("=== FINAL ELITE PROJECTIONS ===")
        print("🎯 Using PROPS-DRIVEN methodology: Only projecting players with betting market data")

        # Load all data
        data = self.load_all_data()
        if not data:
            return []

        projections = []
        skipped_no_props = 0

        for _, player in data['dk'].iterrows():
            player_name = player['Player']
            team = player['Team']
            position = player['Pos']
            salary = player.get('Salary', 0)

            # Skip defense for now (handle separately)
            if position == 'DST':
                continue

            # Get props projection - REQUIRED for our methodology
            props_data = self.get_props_projection(player_name, data['props_summary'])
            props_proj = props_data['projection']

            # ONLY project players with props data
            if props_proj <= 0 or props_data['prop_count'] == 0:
                skipped_no_props += 1
                continue

            # Get model projection (30% weight)
            model_data = self.get_model_projection(player)
            model_proj = model_data['projection']

            # Calculate base projection with full weighting (we have props!)
            base_projection = (
                props_proj * self.weights['props'] +
                model_proj * self.weights['models']
            )
            confidence = (
                props_data['confidence'] * self.weights['props'] +
                model_data['confidence'] * self.weights['models']
            )

            # Apply context adjustments (20% weight)
            context_data = self.apply_context_adjustments(base_projection, player, props_data)
            final_proj = context_data['adjusted_projection']

            # Calculate value score
            value_score = (final_proj / salary * 1000) if salary > 0 else 0

            # Decision factors
            decision_factors = []
            decision_factors.append(f"Props: {props_proj:.1f} pts ({props_data['prop_count']} markets)")
            decision_factors.append(f"Model: {model_proj:.1f} pts ({model_data['source']})")
            decision_factors.extend(context_data['adjustments'])

            projections.append(FinalProjection(
                player_name=player_name,
                team=team,
                position=position,
                salary=salary,
                props_projection=props_proj,
                model_projection=model_proj,
                context_adjustment=context_data['adjustment_factor'],
                final_projection=round(final_proj, 2),
                confidence=round(confidence, 3),
                value_score=round(value_score, 2),
                prop_details=props_data,
                decision_factors=decision_factors
            ))

        # Sort by projection
        projections.sort(key=lambda x: x.final_projection, reverse=True)

        print(f"✅ Created {len(projections)} elite projections (players with props data)")
        print(f"⚠️  Skipped {skipped_no_props} players without props data")
        print("   ^ These players cannot be accurately projected without betting market signals")

        return projections
    
    def save_final_projections(self, projections: List[FinalProjection]) -> str:
        """Save final projections with full breakdown."""
        # Main projections file
        proj_data = []
        for proj in projections:
            proj_data.append({
                'player_name': proj.player_name,
                'team': proj.team,
                'position': proj.position,
                'salary': proj.salary,
                'final_projection': proj.final_projection,
                'props_component': proj.props_projection,
                'model_component': proj.model_projection,
                'context_factor': proj.context_adjustment,
                'confidence': proj.confidence,
                'value_score': proj.value_score,
                'prop_count': proj.prop_details.get('prop_count', 0),
                'sharp_signals': proj.prop_details.get('sharp_signals', 0)
            })

        proj_df = pd.DataFrame(proj_data)
        filename = f"csvs/was_gb_FINAL_elite_projections_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        proj_df.to_csv(filename, index=False)

        # Decision breakdown file
        decision_data = []
        for proj in projections:
            decision_data.append({
                'player_name': proj.player_name,
                'final_projection': proj.final_projection,
                'decision_factors': ' | '.join(proj.decision_factors),
                'confidence_level': 'HIGH' if proj.confidence > 0.8 else 'MEDIUM' if proj.confidence > 0.6 else 'LOW'
            })

        decision_df = pd.DataFrame(decision_data)
        decision_filename = f"csvs/was_gb_projection_decisions_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        decision_df.to_csv(decision_filename, index=False)

        # Create detailed prop-by-prop review file
        self.create_prop_review_file(projections)

        print(f"💾 Final projections saved to: {filename}")
        print(f"💾 Decision breakdown saved to: {decision_filename}")

        return filename, decision_filename

    def create_prop_review_file(self, projections: List[FinalProjection]):
        """Create detailed prop-by-prop review file for manual inspection."""
        try:
            # Load detailed props analysis
            detailed_props = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')

            review_data = []

            for proj in projections:
                player_name = proj.player_name

                # Find all props for this player
                player_props = detailed_props[
                    detailed_props['player_name'].str.contains(player_name, case=False, na=False)
                ]

                for _, prop in player_props.iterrows():
                    market = prop['market']
                    line = prop['line_consensus']
                    projection = prop['final_projection']
                    confidence = prop['confidence_score']

                    # Determine our final number vs the line
                    if 'yds' in market or 'yards' in market:
                        category = 'YARDS'
                        our_number = line * (1 + (projection / max(line * 0.1, 1)))  # Rough conversion back
                    elif 'td' in market or 'touchdown' in market:
                        category = 'TOUCHDOWNS'
                        our_number = projection / 6.0  # Convert fantasy points back to TDs
                    elif 'receptions' in market:
                        category = 'RECEPTIONS'
                        our_number = projection  # Already in receptions
                    elif 'completions' in market:
                        category = 'COMPLETIONS'
                        our_number = projection / 0.5  # Convert back from fantasy points
                    elif 'attempts' in market:
                        category = 'ATTEMPTS'
                        our_number = line  # Use line as baseline
                    elif 'kicking' in market:
                        category = 'KICKING_POINTS'
                        our_number = projection
                    elif 'sacks' in market:
                        category = 'SACKS'
                        our_number = projection
                    elif 'tackles' in market:
                        category = 'TACKLES'
                        our_number = projection / 0.5  # Convert back from fantasy points
                    else:
                        category = 'OTHER'
                        our_number = projection

                    # Determine over/under recommendation
                    if our_number > line * 1.05:  # 5% threshold
                        recommendation = 'OVER'
                    elif our_number < line * 0.95:
                        recommendation = 'UNDER'
                    else:
                        recommendation = 'NEUTRAL'

                    review_data.append({
                        'player_name': player_name,
                        'market': market,
                        'category': category,
                        'line': line,
                        'our_final_number': round(our_number, 2),
                        'difference': round(our_number - line, 2),
                        'percentage_diff': round((our_number - line) / line * 100, 1),
                        'recommendation': recommendation,
                        'confidence': confidence,
                        'fantasy_points': projection,
                        'sharp_vs_public': prop.get('sharp_public_diff', 0),
                        'line_disagreement': prop.get('line_std', 0)
                    })

            review_df = pd.DataFrame(review_data)
            review_filename = f"csvs/was_gb_PROP_REVIEW_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
            review_df.to_csv(review_filename, index=False)

            print(f"📋 Detailed prop review saved to: {review_filename}")
            print("   ^ This file shows our final number vs each betting line")

        except Exception as e:
            print(f"⚠️  Could not create prop review file: {e}")


def main():
    """Main execution."""
    projector = FinalEliteProjections()
    
    # Create projections
    projections = projector.create_final_projections()
    
    if not projections:
        print("❌ Could not create projections")
        return
    
    # Save projections
    proj_file, decision_file = projector.save_final_projections(projections)
    
    # Display top projections
    print(f"\n🏆 TOP 15 FINAL PROJECTIONS:")
    print(f"{'Rank':<4} {'Player':<18} {'Pos':<3} {'Proj':<6} {'Value':<6} {'Conf':<5} {'Props':<5}")
    print("-" * 65)
    
    for i, proj in enumerate(projections[:15]):
        print(f"{i+1:<4} {proj.player_name:<18} {proj.position:<3} "
              f"{proj.final_projection:<6.1f} {proj.value_score:<6.2f} "
              f"{proj.confidence:<5.3f} {proj.prop_details.get('prop_count', 0):<5}")
    
    print(f"\n📊 SUMMARY:")
    print(f"   Total Players: {len(projections)}")
    print(f"   With Props Data: {sum(1 for p in projections if p.prop_details.get('prop_count', 0) > 0)}")
    print(f"   High Confidence (>0.8): {sum(1 for p in projections if p.confidence > 0.8)}")
    print(f"   Sharp Money Signals: {sum(p.prop_details.get('sharp_signals', 0) for p in projections)}")
    
    return projections


if __name__ == "__main__":
    main()
