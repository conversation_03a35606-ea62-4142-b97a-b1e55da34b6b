#!/usr/bin/env python3
"""
Automated Gamebook Processor - Processes all gamebooks and updates team profiles
"""
import json
import os
import re
from pathlib import Path
from typing import Dict, List, Any, Tuple, Optional
from datetime import datetime

class GamebookProcessor:
    def __init__(self):
        self.gamebook_dir = Path("csvs/Gamebook Results")
        self.game_results_file = "game_results.jsonl"
        self.team_history_dir = Path("team_history")
        self.processed_games = set()
        
        # Load already processed games
        self._load_processed_games()
        
        # Ensure directories exist
        self.team_history_dir.mkdir(exist_ok=True)
    
    def _load_processed_games(self):
        """Load list of already processed games"""
        if os.path.exists(self.game_results_file):
            with open(self.game_results_file, 'r') as f:
                for line in f:
                    game = json.loads(line.strip())
                    self.processed_games.add(game['game_id'])
    
    def get_unprocessed_gamebooks(self) -> List[Path]:
        """Get list of unprocessed gamebook files"""
        all_gamebooks = list(self.gamebook_dir.glob("*.md"))
        unprocessed = []
        
        for gamebook in all_gamebooks:
            # Skip the generic gamebook.md file
            if gamebook.name == "gamebook.md":
                continue
                
            # Create game_id from filename
            game_id = self._create_game_id_from_filename(gamebook.name)
            if game_id not in self.processed_games:
                unprocessed.append(gamebook)
        
        return unprocessed
    
    def _create_game_id_from_filename(self, filename: str) -> str:
        """Create consistent game_id from filename"""
        # Remove .md extension and normalize
        base = filename.replace('.md', '').lower()
        # Replace spaces and special chars with underscores
        base = re.sub(r'[^a-z0-9]', '_', base)
        # Remove multiple underscores
        base = re.sub(r'_+', '_', base).strip('_')
        return f"{base}_20250908"  # Assuming all games are from same date
    
    def extract_teams_from_filename(self, filename: str) -> Tuple[str, str]:
        """Extract team names from filename"""
        base = filename.replace('.md', '')
        
        # Handle different filename formats
        if ' vs ' in base:
            teams = base.split(' vs ')
        elif ' at ' in base:
            teams = base.split(' at ')
        else:
            # Try to split on common patterns
            teams = re.split(r'[_\s]+', base)
            if len(teams) >= 2:
                teams = [teams[0], teams[1]]
            else:
                return "Unknown", "Unknown"
        
        # Normalize team names
        away_team = self._normalize_team_name(teams[0].strip())
        home_team = self._normalize_team_name(teams[1].strip())
        
        return away_team, home_team
    
    def _normalize_team_name(self, name: str) -> str:
        """Normalize team names to consistent format"""
        name = name.strip().lower()
        
        # Team name mappings
        team_map = {
            'vikings': 'Vikings',
            'bears': 'Bears',
            '49ers': '49ers',
            'seahawks': 'Seahawks',
            'bengals': 'Bengals',
            'browns': 'Browns',
            'raiders': 'Raiders',
            'patriots': 'Patriots',
            'bucs': 'Buccaneers',
            'buccaneers': 'Buccaneers',
            'falcons': 'Falcons',
            'cardinals': 'Cardinals',
            'saints': 'Saints',
            'chiefs': 'Chiefs',
            'chargers': 'Chargers',
            'cowboys': 'Cowboys',
            'eagles': 'Eagles',
            'dolphins': 'Dolphins',
            'colts': 'Colts',
            'giants': 'Giants',
            'commanders': 'Commanders',
            'lions': 'Lions',
            'packers': 'Packers',
            'panthers': 'Panthers',
            'jaguars': 'Jaguars',
            'ravens': 'Ravens',
            'bills': 'Bills',
            'steelers': 'Steelers',
            'jets': 'Jets',
            'texans': 'Texans',
            'rams': 'Rams',
            'titans': 'Titans',
            'broncos': 'Broncos'
        }
        
        return team_map.get(name, name.title())
    
    def process_next_gamebook(self) -> Optional[Dict]:
        """Process the next unprocessed gamebook"""
        unprocessed = self.get_unprocessed_gamebooks()
        
        if not unprocessed:
            print("No unprocessed gamebooks found!")
            return None
        
        # Process the first unprocessed gamebook
        gamebook_file = unprocessed[0]
        print(f"\n🏈 Processing: {gamebook_file.name}")
        
        try:
            # Extract team names from filename
            away_team, home_team = self.extract_teams_from_filename(gamebook_file.name)
            print(f"   Teams: {away_team} @ {home_team}")
            
            # For now, return a placeholder - in real implementation, 
            # this would call the full extraction logic
            game_data = {
                "game_id": self._create_game_id_from_filename(gamebook_file.name),
                "filename": gamebook_file.name,
                "away_team": away_team,
                "home_team": home_team,
                "status": "ready_for_extraction"
            }
            
            return game_data
            
        except Exception as e:
            print(f"❌ Error processing {gamebook_file.name}: {e}")
            return None
    
    def list_processing_queue(self):
        """Show the processing queue"""
        unprocessed = self.get_unprocessed_gamebooks()
        
        print(f"\n📋 PROCESSING QUEUE ({len(unprocessed)} games remaining):")
        print("=" * 60)
        
        for i, gamebook in enumerate(unprocessed, 1):
            away_team, home_team = self.extract_teams_from_filename(gamebook.name)
            print(f"{i:2d}. {away_team} @ {home_team} ({gamebook.name})")
        
        print(f"\n✅ Already processed: {len(self.processed_games)} games")
        return unprocessed

def main():
    processor = GamebookProcessor()
    
    # Show current status
    processor.list_processing_queue()
    
    # Process next game
    next_game = processor.process_next_gamebook()
    if next_game:
        print(f"\n🎯 Next game ready: {next_game['away_team']} @ {next_game['home_team']}")
        print(f"   File: {next_game['filename']}")
        print(f"   Game ID: {next_game['game_id']}")

if __name__ == "__main__":
    main()
