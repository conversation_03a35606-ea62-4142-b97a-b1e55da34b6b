#!/usr/bin/env python3
"""
Elite Odds Analyzer for WAS @ GB
Uses multi-sportsbook data to derive the most accurate projections possible.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
from dataclasses import dataclass
import json


@dataclass
class PropAnalysis:
    """Analysis of a single prop across multiple books."""
    player_name: str
    market: str
    line_consensus: float
    sharp_line: float  # Best line indicating sharp money
    public_line: float  # Most common line
    implied_probability: float
    devigged_probability: float
    final_projection: float
    confidence_score: float
    book_count: int
    line_range: Tuple[float, float]
    odds_analysis: Dict[str, Any]


class EliteOddsAnalyzer:
    """Sophisticated analysis of multi-book odds for elite projections."""
    
    def __init__(self):
        self.props_df = None
        self.book_rankings = {
            # Sharp books (lower vig, sharper lines)
            'Pinnacle': 1.0,
            'BetOnline.ag': 0.9,
            'Bovada': 0.8,
            # Public books
            'DraftKings': 0.7,
            'BetMGM': 0.6,
            'FanDuel': 0.6
        }
        
        # Fantasy scoring system
        self.scoring = {
            'pass_yards': 0.04,
            'pass_tds': 4.0,
            'pass_completions': 0.5,
            'pass_attempts': 0.0,
            'rush_yards': 0.1,
            'rush_tds': 6.0,
            'reception_yards': 0.1,
            'receptions': 1.0,
            'reception_tds': 6.0,
            'kicking_points': 1.0,
            'sacks': 1.0,
            'tackles_assists': 0.5,
            'defensive_interceptions': 2.0
        }
    
    def load_props_data(self) -> bool:
        """Load the props data."""
        try:
            self.props_df = pd.read_csv('csvs/was_gb_player_props_20250911_2151.csv')
            print(f"✅ Loaded {len(self.props_df)} props from {len(self.props_df['bookmaker'].unique())} books")
            return True
        except Exception as e:
            print(f"❌ Error loading props: {e}")
            return False
    
    def american_to_prob(self, odds: int) -> float:
        """Convert American odds to probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    
    def devig_multiplicative(self, p_over: float, p_under: float) -> Tuple[float, float]:
        """Remove vig using multiplicative method."""
        total = p_over + p_under
        if total <= 0:
            return 0.5, 0.5
        return p_over / total, p_under / total
    
    def calculate_implied_mean(self, line: float, fair_over_prob: float, 
                              market: str, position: str = 'QB') -> float:
        """Calculate implied mean from line and probability."""
        # Position-specific sigma estimates
        sigma_map = {
            'player_pass_yds': {'QB': 35.0},
            'player_rush_yds': {'QB': 15.0, 'RB': 25.0, 'WR': 8.0},
            'player_reception_yds': {'WR': 20.0, 'TE': 15.0, 'RB': 12.0},
            'player_receptions': {'WR': 2.5, 'TE': 2.0, 'RB': 1.8},
            'player_pass_tds': {'QB': 0.8},
            'player_rush_tds': {'RB': 0.6, 'QB': 0.4},
            'player_reception_tds': {'WR': 0.5, 'TE': 0.4}
        }
        
        sigma = sigma_map.get(market, {}).get(position, 5.0)
        
        # Use normal distribution to back out implied mean
        from scipy.stats import norm
        z_score = norm.ppf(fair_over_prob)
        implied_mean = line - (z_score * sigma)
        
        return max(implied_mean, 0)  # Can't be negative
    
    def analyze_single_prop(self, player: str, market: str) -> PropAnalysis:
        """Analyze a single prop across all available books."""
        player_props = self.props_df[
            (self.props_df['player_name'].str.contains(player, case=False, na=False)) &
            (self.props_df['market'] == market)
        ]
        
        if player_props.empty:
            return None
        
        # Collect all lines and odds
        lines = []
        over_probs = []
        under_probs = []
        book_weights = []
        
        for _, prop in player_props.iterrows():
            line = prop['line']
            over_odds = prop['over_odds']
            under_odds = prop['under_odds']
            book = prop['bookmaker']
            
            # Convert to probabilities
            p_over = self.american_to_prob(over_odds)
            p_under = self.american_to_prob(under_odds)
            
            # Remove vig
            fair_over, fair_under = self.devig_multiplicative(p_over, p_under)
            
            lines.append(line)
            over_probs.append(fair_over)
            under_probs.append(fair_under)
            book_weights.append(self.book_rankings.get(book, 0.5))
        
        # Calculate consensus metrics
        weighted_line = np.average(lines, weights=book_weights)
        weighted_prob = np.average(over_probs, weights=book_weights)
        
        # Identify sharp vs public lines
        sharp_books = [i for i, book in enumerate(player_props['bookmaker']) 
                      if self.book_rankings.get(book, 0) >= 0.8]
        public_books = [i for i, book in enumerate(player_props['bookmaker']) 
                       if self.book_rankings.get(book, 0) < 0.7]
        
        sharp_line = np.mean([lines[i] for i in sharp_books]) if sharp_books else weighted_line
        public_line = np.mean([lines[i] for i in public_books]) if public_books else weighted_line
        
        # Calculate implied mean
        position = self.infer_position(market)
        implied_mean = self.calculate_implied_mean(weighted_line, weighted_prob, market, position)
        
        # Convert to fantasy points
        fantasy_points = self.convert_to_fantasy_points(market, implied_mean)
        
        # Calculate confidence based on line agreement
        line_std = np.std(lines) if len(lines) > 1 else 0
        confidence = max(0.5, 1.0 - (line_std / max(weighted_line, 1)) * 2)
        
        return PropAnalysis(
            player_name=player,
            market=market,
            line_consensus=round(weighted_line, 1),
            sharp_line=round(sharp_line, 1),
            public_line=round(public_line, 1),
            implied_probability=round(weighted_prob, 3),
            devigged_probability=round(weighted_prob, 3),
            final_projection=round(fantasy_points, 2),
            confidence_score=round(confidence, 3),
            book_count=len(player_props),
            line_range=(min(lines), max(lines)),
            odds_analysis={
                'line_std': round(line_std, 2),
                'sharp_public_diff': round(sharp_line - public_line, 2),
                'vig_average': round(np.mean([p_over + p_under - 1 for p_over, p_under in zip([self.american_to_prob(prop['over_odds']) for _, prop in player_props.iterrows()], [self.american_to_prob(prop['under_odds']) for _, prop in player_props.iterrows()])]), 3)
            }
        )
    
    def infer_position(self, market: str) -> str:
        """Infer position from market type."""
        if 'pass' in market:
            return 'QB'
        elif 'rush' in market and 'reception' not in market:
            return 'RB'
        elif 'reception' in market:
            return 'WR'
        elif 'kicking' in market:
            return 'K'
        elif 'sack' in market or 'tackle' in market:
            return 'DEF'
        return 'FLEX'
    
    def convert_to_fantasy_points(self, market: str, implied_mean: float) -> float:
        """Convert market implied mean to fantasy points."""
        market_map = {
            'player_pass_yds': 'pass_yards',
            'player_pass_tds': 'pass_tds',
            'player_pass_completions': 'pass_completions',
            'player_rush_yds': 'rush_yards',
            'player_rush_tds': 'rush_tds',
            'player_reception_yds': 'reception_yards',
            'player_receptions': 'receptions',
            'player_reception_tds': 'reception_tds',
            'player_kicking_points': 'kicking_points',
            'player_sacks': 'sacks',
            'player_tackles_assists': 'tackles_assists',
            'player_defensive_interceptions': 'defensive_interceptions'
        }
        
        scoring_key = market_map.get(market)
        if scoring_key and scoring_key in self.scoring:
            return implied_mean * self.scoring[scoring_key]
        
        return 0
    
    def analyze_all_props(self) -> List[PropAnalysis]:
        """Analyze all available props."""
        if not self.load_props_data():
            return []
        
        # Get unique player-market combinations
        unique_combos = self.props_df.groupby(['player_name', 'market']).size().reset_index()
        
        analyses = []
        print(f"🔍 Analyzing {len(unique_combos)} unique prop combinations...")
        
        for _, combo in unique_combos.iterrows():
            player = combo['player_name']
            market = combo['market']
            
            analysis = self.analyze_single_prop(player, market)
            if analysis:
                analyses.append(analysis)
        
        print(f"✅ Completed analysis of {len(analyses)} props")
        return analyses
    
    def create_player_projections(self, analyses: List[PropAnalysis]) -> Dict[str, Dict]:
        """Aggregate prop analyses into player projections."""
        player_projections = {}
        
        for analysis in analyses:
            player = analysis.player_name
            
            if player not in player_projections:
                player_projections[player] = {
                    'total_projection': 0,
                    'prop_count': 0,
                    'confidence_weighted_avg': 0,
                    'props_breakdown': {},
                    'sharp_signals': [],
                    'line_disagreements': []
                }
            
            proj = player_projections[player]
            proj['total_projection'] += analysis.final_projection
            proj['prop_count'] += 1
            proj['confidence_weighted_avg'] += analysis.confidence_score
            proj['props_breakdown'][analysis.market] = {
                'line': analysis.line_consensus,
                'projection': analysis.final_projection,
                'confidence': analysis.confidence_score
            }
            
            # Track sharp money signals
            if abs(analysis.odds_analysis['sharp_public_diff']) > 0.5:
                proj['sharp_signals'].append({
                    'market': analysis.market,
                    'sharp_line': analysis.sharp_line,
                    'public_line': analysis.public_line,
                    'difference': analysis.odds_analysis['sharp_public_diff']
                })
            
            # Track line disagreements
            if analysis.line_range[1] - analysis.line_range[0] > 1.0:
                proj['line_disagreements'].append({
                    'market': analysis.market,
                    'range': analysis.line_range,
                    'consensus': analysis.line_consensus
                })
        
        # Calculate final confidence scores
        for player, proj in player_projections.items():
            if proj['prop_count'] > 0:
                proj['confidence_weighted_avg'] /= proj['prop_count']
        
        return player_projections
    
    def save_detailed_analysis(self, analyses: List[PropAnalysis], 
                              projections: Dict[str, Dict]) -> str:
        """Save detailed analysis for review."""
        # Create detailed breakdown
        detailed_data = []
        
        for analysis in analyses:
            detailed_data.append({
                'player_name': analysis.player_name,
                'market': analysis.market,
                'line_consensus': analysis.line_consensus,
                'sharp_line': analysis.sharp_line,
                'public_line': analysis.public_line,
                'final_projection': analysis.final_projection,
                'confidence_score': analysis.confidence_score,
                'book_count': analysis.book_count,
                'line_range_min': analysis.line_range[0],
                'line_range_max': analysis.line_range[1],
                'sharp_public_diff': analysis.odds_analysis['sharp_public_diff'],
                'line_std': analysis.odds_analysis['line_std']
            })
        
        # Save to CSV
        detailed_df = pd.DataFrame(detailed_data)
        filename = f"csvs/was_gb_detailed_prop_analysis_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        detailed_df.to_csv(filename, index=False)
        
        # Save projections summary
        proj_data = []
        for player, proj in projections.items():
            proj_data.append({
                'player_name': player,
                'total_projection': round(proj['total_projection'], 2),
                'prop_count': proj['prop_count'],
                'avg_confidence': round(proj['confidence_weighted_avg'], 3),
                'sharp_signals_count': len(proj['sharp_signals']),
                'line_disagreements_count': len(proj['line_disagreements'])
            })
        
        proj_df = pd.DataFrame(proj_data).sort_values('total_projection', ascending=False)
        proj_filename = f"csvs/was_gb_player_projections_summary_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        proj_df.to_csv(proj_filename, index=False)
        
        print(f"💾 Detailed analysis saved to: {filename}")
        print(f"💾 Projections summary saved to: {proj_filename}")
        
        return filename, proj_filename


def main():
    """Main execution."""
    analyzer = EliteOddsAnalyzer()
    
    print("=== ELITE ODDS ANALYSIS ===")
    
    # Analyze all props
    analyses = analyzer.analyze_all_props()
    
    if not analyses:
        print("❌ No props data to analyze")
        return
    
    # Create player projections
    projections = analyzer.create_player_projections(analyses)
    
    # Save detailed analysis
    detail_file, proj_file = analyzer.save_detailed_analysis(analyses, projections)
    
    # Show top projections
    print(f"\n🏆 TOP PROJECTIONS:")
    sorted_players = sorted(projections.items(), 
                           key=lambda x: x[1]['total_projection'], 
                           reverse=True)
    
    for i, (player, proj) in enumerate(sorted_players[:10]):
        print(f"{i+1:2d}. {player:<20} {proj['total_projection']:6.2f} pts "
              f"({proj['prop_count']} props, {proj['avg_confidence']:.3f} conf)")
    
    print(f"\n✅ Analysis complete! Review files:")
    print(f"   📊 Detailed: {detail_file}")
    print(f"   📈 Summary: {proj_file}")
    
    return analyses, projections


if __name__ == "__main__":
    main()
