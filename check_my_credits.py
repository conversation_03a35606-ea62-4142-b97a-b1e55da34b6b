#!/usr/bin/env python3
"""
Quick Credit Check for The Odds API
Simple script to check your remaining API credits
"""

from api_credit_checker import Odds<PERSON><PERSON><PERSON><PERSON>


def main():
    """Quick credit check with your API key."""
    
    print("🔍 THE ODDS API CREDIT CHECKER")
    print("=" * 35)
    
    # Put your API key here
    api_key = "YOUR_API_KEY_HERE"  # Replace with your actual key
    
    if api_key == "YOUR_API_KEY_HERE":
        print("❌ Please edit this file and add your actual API key!")
        print("   Get your key from: https://the-odds-api.com/")
        print("   Replace 'YOUR_API_KEY_HERE' with your actual key")
        return
    
    # Check credits
    checker = OddsAPIChecker(api_key)
    checker.print_credit_status()
    
    # Show projection cost estimates
    print(f"\n💡 PROJECTION COST ESTIMATES:")
    print("-" * 30)
    
    estimate = checker.estimate_projection_costs(1)
    if 'error' not in estimate:
        print(f"   1 game: {estimate['total_requests_needed']} requests, ${estimate['estimated_cost']:.2f}")
        print(f"   5 games: {estimate['total_requests_needed']*5} requests, ${estimate['estimated_cost']*5:.2f}")
        print(f"   10 games: {estimate['total_requests_needed']*10} requests, ${estimate['estimated_cost']*10:.2f}")
        
        max_games = estimate['games_possible']
        print(f"\n🎯 With current credits: ~{max_games} games possible")
    else:
        print("   Could not calculate estimates")


if __name__ == "__main__":
    main()
