#!/usr/bin/env python3
"""
Enhanced NFL Projection Engine v3.0
Integrating improved prop reading and market signal analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import json
from datetime import datetime


class EnhancedNFLEngine:
    """Complete NFL projection engine with advanced prop reading capabilities."""
    
    def __init__(self):
        # Market signal thresholds (from improved_prop_reader.py)
        self.market_thresholds = {
            'high_std': 2.0,           # High line disagreement
            'wide_range': 4.0,         # Wide line range
            'strong_sharp_signal': 2.0, # Strong sharp vs public difference
            'min_books': 4,            # Minimum books for confidence
            'correlation_threshold': 0.7 # Minimum correlation for consistency
        }
        
        # Confidence adjustments based on market signals
        self.confidence_adjustments = {
            'high_std': 0.80,          # 20% reduction for high disagreement
            'wide_range': 0.75,        # 25% reduction for wide range
            'low_book_count': 0.85,    # 15% reduction for thin markets
            'strong_sharp_signal': 1.2, # 20% increase for strong sharp signal
            'correlation_penalty': 0.8  # 20% reduction for correlation issues
        }
        
        # Sharp money weighting tiers
        self.sharp_money_weights = {
            'strong': {'sharp': 0.70, 'public': 0.30},    # >3 point diff
            'medium': {'sharp': 0.60, 'public': 0.40},    # 2-3 point diff
            'weak': {'sharp': 0.55, 'public': 0.45},      # 1-2 point diff
            'none': {'sharp': 0.50, 'public': 0.50}       # <1 point diff
        }
        
        # Book reliability weighting (based on WAS@GB analysis)
        self.book_weights = {
            'circa': 0.25,
            'pinnacle': 0.25,
            'betmgm': 0.20,
            'bookmaker': 0.15,
            'betrivers': 0.10,
            'fanduel': 0.05
        }
    
    def analyze_market_signals(self, prop_data: pd.DataFrame) -> Dict[str, Any]:
        """Comprehensive market signal analysis."""
        
        market_analysis = {
            'uncertainty_signals': {},
            'sharp_money_signals': {},
            'liquidity_scores': {},
            'correlation_flags': []
        }
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            # 1. Uncertainty Analysis
            line_std = row.get('line_std', 0)
            line_range = row.get('line_range_max', 0) - row.get('line_range_min', 0)
            book_count = row.get('book_count', 1)
            
            uncertainty_score = 0
            if line_std > self.market_thresholds['high_std']:
                uncertainty_score += 0.3
            if line_range > self.market_thresholds['wide_range']:
                uncertainty_score += 0.3
            if book_count < self.market_thresholds['min_books']:
                uncertainty_score += 0.2
            
            market_analysis['uncertainty_signals'][key] = {
                'score': min(1.0, uncertainty_score),
                'line_std': line_std,
                'line_range': line_range,
                'book_count': book_count
            }
            
            # 2. Sharp Money Analysis
            sharp_line = row.get('sharp_line', row.get('line_consensus', 0))
            public_line = row.get('public_line', row.get('line_consensus', 0))
            sharp_diff = abs(sharp_line - public_line)
            
            if sharp_diff >= self.market_thresholds['strong_sharp_signal']:
                signal_strength = 'strong' if sharp_diff > 3 else 'medium'
                market_analysis['sharp_money_signals'][key] = {
                    'strength': signal_strength,
                    'direction': 'OVER' if sharp_line > public_line else 'UNDER',
                    'sharp_line': sharp_line,
                    'public_line': public_line,
                    'difference': sharp_diff
                }
            
            # 3. Liquidity Scoring
            liquidity_score = min(1.0, book_count / 6.0)  # Scale to 6 books max
            market_analysis['liquidity_scores'][key] = liquidity_score
        
        return market_analysis
    
    def check_projection_correlations(self, projections: Dict[str, float]) -> List[Dict]:
        """Check for correlation inconsistencies in projections."""
        
        correlation_issues = []
        
        # QB vs WR correlation check
        qb_players = ['Jayden Daniels', 'Jordan Love']
        wr_mappings = {
            'Jayden Daniels': ['Terry McLaurin', 'Deebo Samuel'],
            'Jordan Love': ['Romeo Doubs', 'Jayden Reed', 'Tucker Kraft']
        }
        
        for qb in qb_players:
            if qb in projections:
                qb_projection = projections[qb]
                
                # Estimate QB passing component (assume 70% of fantasy points from passing)
                qb_pass_component = qb_projection * 0.7
                
                # Check associated receivers
                for wr in wr_mappings.get(qb, []):
                    if wr in projections:
                        wr_projection = projections[wr]
                        
                        # If QB has high passing projection, WRs should too
                        if qb_pass_component > 20 and wr_projection < 8:
                            correlation_issues.append({
                                'type': 'QB_WR_MISMATCH',
                                'qb': qb,
                                'wr': wr,
                                'qb_pass_component': qb_pass_component,
                                'wr_projection': wr_projection,
                                'severity': 'HIGH' if qb_pass_component > 25 else 'MEDIUM'
                            })
        
        return correlation_issues
    
    def calculate_market_adjusted_projections(self, 
                                            raw_projections: Dict[str, float],
                                            prop_data: pd.DataFrame,
                                            market_analysis: Dict) -> Dict[str, float]:
        """Calculate projections adjusted for market signals."""
        
        adjusted_projections = raw_projections.copy()
        adjustment_log = {}
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            if player not in adjusted_projections:
                continue
            
            original_projection = adjusted_projections[player]
            line_consensus = row.get('line_consensus', original_projection)
            
            # Start with original projection
            final_projection = original_projection
            adjustments = []
            
            # 1. Sharp Money Adjustment
            if key in market_analysis['sharp_money_signals']:
                sharp_signal = market_analysis['sharp_money_signals'][key]
                sharp_line = sharp_signal['sharp_line']
                public_line = sharp_signal['public_line']
                
                # Weight toward sharp line based on signal strength
                weights = self.sharp_money_weights[sharp_signal['strength']]
                market_implied = sharp_line * weights['sharp'] + public_line * weights['public']
                
                # Blend our projection with market-implied projection
                final_projection = final_projection * 0.6 + market_implied * 0.4
                adjustments.append(f"Sharp money {sharp_signal['direction']}")
            
            # 2. Uncertainty Penalty
            if key in market_analysis['uncertainty_signals']:
                uncertainty = market_analysis['uncertainty_signals'][key]
                if uncertainty['score'] > 0.5:
                    # Move projection closer to consensus line when uncertain
                    uncertainty_factor = uncertainty['score']
                    final_projection = (final_projection * (1 - uncertainty_factor * 0.3) + 
                                      line_consensus * uncertainty_factor * 0.3)
                    adjustments.append(f"Uncertainty penalty ({uncertainty['score']:.2f})")
            
            # 3. Liquidity Adjustment
            liquidity_score = market_analysis['liquidity_scores'].get(key, 1.0)
            if liquidity_score < 0.7:
                # Move toward consensus on thin markets
                final_projection = final_projection * 0.8 + line_consensus * 0.2
                adjustments.append(f"Low liquidity ({liquidity_score:.2f})")
            
            adjusted_projections[player] = final_projection
            
            if adjustments:
                adjustment_log[player] = {
                    'original': original_projection,
                    'final': final_projection,
                    'change': final_projection - original_projection,
                    'adjustments': adjustments
                }
        
        return adjusted_projections, adjustment_log
    
    def generate_enhanced_recommendations(self, 
                                        projections: Dict[str, float],
                                        prop_data: pd.DataFrame,
                                        market_analysis: Dict,
                                        min_edge: float = 0.08,
                                        min_confidence: float = 0.65) -> List[Dict]:
        """Generate recommendations with enhanced market signal analysis."""
        
        recommendations = []
        correlation_issues = self.check_projection_correlations(projections)
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            line = row.get('line_consensus', 0)
            
            if player not in projections or line == 0:
                continue
            
            projection = projections[player]
            edge = abs(projection - line) / line
            
            if edge < min_edge:
                continue
            
            # Calculate base confidence
            base_confidence = row.get('confidence_score', 0.5)
            
            # Apply market signal adjustments
            final_confidence = base_confidence
            confidence_factors = []
            
            # Uncertainty adjustment
            if key in market_analysis['uncertainty_signals']:
                uncertainty = market_analysis['uncertainty_signals'][key]
                if uncertainty['score'] > 0.3:
                    uncertainty_penalty = self.confidence_adjustments['high_std']
                    final_confidence *= uncertainty_penalty
                    confidence_factors.append(f"Uncertainty: {uncertainty_penalty:.2f}")
            
            # Sharp money boost
            if key in market_analysis['sharp_money_signals']:
                sharp_signal = market_analysis['sharp_money_signals'][key]
                our_direction = 'OVER' if projection > line else 'UNDER'
                
                if sharp_signal['direction'] == our_direction:
                    sharp_boost = self.confidence_adjustments['strong_sharp_signal']
                    final_confidence *= sharp_boost
                    confidence_factors.append(f"Sharp alignment: {sharp_boost:.2f}")
                else:
                    # Sharp money disagrees with us - reduce confidence
                    final_confidence *= 0.7
                    confidence_factors.append("Sharp disagreement: 0.70")
            
            # Liquidity adjustment
            liquidity_score = market_analysis['liquidity_scores'].get(key, 1.0)
            if liquidity_score < 0.7:
                liquidity_penalty = self.confidence_adjustments['low_book_count']
                final_confidence *= liquidity_penalty
                confidence_factors.append(f"Low liquidity: {liquidity_penalty:.2f}")
            
            # Correlation penalty
            player_correlation_issues = [issue for issue in correlation_issues 
                                       if issue.get('qb') == player or issue.get('wr') == player]
            if player_correlation_issues:
                correlation_penalty = self.confidence_adjustments['correlation_penalty']
                final_confidence *= correlation_penalty
                confidence_factors.append(f"Correlation issue: {correlation_penalty:.2f}")
            
            # Cap confidence
            final_confidence = max(0.1, min(1.0, final_confidence))
            
            if final_confidence >= min_confidence:
                recommendation = {
                    'player': player,
                    'market': market,
                    'line': line,
                    'projection': projection,
                    'edge': edge,
                    'recommendation': 'OVER' if projection > line else 'UNDER',
                    'base_confidence': base_confidence,
                    'final_confidence': final_confidence,
                    'confidence_factors': confidence_factors
                }
                
                # Add market context
                if key in market_analysis['sharp_money_signals']:
                    recommendation['sharp_signal'] = market_analysis['sharp_money_signals'][key]
                
                if key in market_analysis['uncertainty_signals']:
                    recommendation['uncertainty'] = market_analysis['uncertainty_signals'][key]
                
                recommendations.append(recommendation)
        
        # Sort by confidence * edge
        recommendations.sort(key=lambda x: x['final_confidence'] * x['edge'], reverse=True)
        
        return recommendations
    
    def run_complete_analysis(self, 
                            prop_data_file: str,
                            raw_projections: Dict[str, float]) -> Dict[str, Any]:
        """Run complete enhanced analysis."""
        
        try:
            prop_data = pd.read_csv(prop_data_file)
        except Exception as e:
            return {'error': f"Could not load prop data: {e}"}
        
        print("🚀 ENHANCED NFL ENGINE v3.0 - COMPLETE ANALYSIS")
        print("=" * 60)
        
        # 1. Market Signal Analysis
        print("\n📊 ANALYZING MARKET SIGNALS...")
        market_analysis = self.analyze_market_signals(prop_data)
        
        print(f"   • Uncertainty signals: {len(market_analysis['uncertainty_signals'])}")
        print(f"   • Sharp money signals: {len(market_analysis['sharp_money_signals'])}")
        print(f"   • Liquidity scores calculated: {len(market_analysis['liquidity_scores'])}")
        
        # 2. Correlation Check
        print("\n🔗 CHECKING PROJECTION CORRELATIONS...")
        correlation_issues = self.check_projection_correlations(raw_projections)
        print(f"   • Correlation issues found: {len(correlation_issues)}")
        
        for issue in correlation_issues:
            print(f"     - {issue['type']}: {issue['qb']} vs {issue['wr']} ({issue['severity']})")
        
        # 3. Market-Adjusted Projections
        print("\n⚖️ CALCULATING MARKET-ADJUSTED PROJECTIONS...")
        adjusted_projections, adjustment_log = self.calculate_market_adjusted_projections(
            raw_projections, prop_data, market_analysis
        )
        
        print(f"   • Players adjusted: {len(adjustment_log)}")
        
        # 4. Enhanced Recommendations
        print("\n🎯 GENERATING ENHANCED RECOMMENDATIONS...")
        recommendations = self.generate_enhanced_recommendations(
            adjusted_projections, prop_data, market_analysis
        )
        
        print(f"   • Recommendations generated: {len(recommendations)}")
        
        # 5. Summary
        print(f"\n📋 TOP RECOMMENDATIONS:")
        for i, rec in enumerate(recommendations[:5], 1):
            print(f"   {i}. {rec['player']} {rec['market']} {rec['recommendation']}")
            print(f"      Edge: {rec['edge']:.1%}, Confidence: {rec['final_confidence']:.2f}")
            if 'sharp_signal' in rec:
                print(f"      Sharp: {rec['sharp_signal']['direction']} ({rec['sharp_signal']['strength']})")
        
        return {
            'market_analysis': market_analysis,
            'correlation_issues': correlation_issues,
            'adjusted_projections': adjusted_projections,
            'adjustment_log': adjustment_log,
            'recommendations': recommendations,
            'summary': {
                'total_props_analyzed': len(prop_data),
                'sharp_signals_found': len(market_analysis['sharp_money_signals']),
                'high_uncertainty_props': len([k for k, v in market_analysis['uncertainty_signals'].items() if v['score'] > 0.5]),
                'final_recommendations': len(recommendations)
            }
        }


def main():
    """Example usage of enhanced engine."""
    
    engine = EnhancedNFLEngine()
    
    # Example projections (would come from your models)
    sample_projections = {
        'Jayden Daniels': 25.0,
        'Jordan Love': 22.0,
        'Josh Jacobs': 18.0,
        'Terry McLaurin': 14.0,
        'Romeo Doubs': 12.0
    }
    
    # Run analysis (using WAS@GB data as example)
    try:
        results = engine.run_complete_analysis(
            'csvs/was_gb_detailed_prop_analysis_20250911_2159.csv',
            sample_projections
        )
        
        if 'error' not in results:
            print(f"\n✅ ANALYSIS COMPLETE!")
            print(f"   Ready for next game with enhanced prop reading!")
        else:
            print(f"❌ Error: {results['error']}")
            
    except Exception as e:
        print(f"❌ Could not run analysis: {e}")
        print("   Engine is ready for use with proper data files")


if __name__ == "__main__":
    main()
