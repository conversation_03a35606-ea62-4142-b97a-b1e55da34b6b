#!/usr/bin/env python3
"""
🏈 ELITE NFL PROJECTION SYSTEM: Jacksonville Jaguars @ Cincinnati Bengals
Complete methodology with real player props, team edges, and elite prop reading
"""

import pandas as pd
import numpy as np
import requests
import os
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import json
from datetime import datetime

@dataclass
class GameContext:
    """Game context and market data."""
    home_team: str = "Bengals"
    away_team: str = "Jaguars"
    total: float = 49.5
    spread: float = -3.5  # Bengals favored
    home_implied: float = 26.5
    away_implied: float = 23.0
    weather: Dict[str, Any] = None

@dataclass
class PlayerProp:
    """Individual player prop with market analysis."""
    player: str
    market: str
    line: float
    over_odds: int
    under_odds: int
    implied_prob_over: float
    implied_prob_under: float
    market_strength: float
    sharp_money_indicator: float

class EliteProjectionSystem:
    """Elite NFL projection system with comprehensive prop analysis."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game_context = GameContext()
        self.team_edges = self.get_team_edges()
        self.dk_scoring = self.get_dk_scoring()

    def get_dk_scoring(self) -> Dict[str, float]:
        """DraftKings scoring system."""
        return {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'fg_made': 3, 'xp_made': 1,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }

    def get_team_edges(self) -> Dict[str, float]:
        """Get matchup edges from team ratings with enhanced analysis."""
        try:
            df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')

            # Find Bengals and Jaguars
            bengals_row = df_holes_levers[df_holes_levers['team'].str.contains('Bengals|CIN', case=False, na=False)]
            jaguars_row = df_holes_levers[df_holes_levers['team'].str.contains('Jaguars|JAC', case=False, na=False)]

            if not bengals_row.empty and not jaguars_row.empty:
                bengals = bengals_row.iloc[0]
                jaguars = jaguars_row.iloc[0]

                print(f"📊 TEAM EDGES ANALYSIS:")
                print(f"Bengals Levers: explosive_pass={bengals.get('lever_explosive_pass', 0):.3f}, rz={bengals.get('lever_rz', 0):.3f}")
                print(f"Jaguars Holes: pass_eff={jaguars.get('hole_pass_eff', 0):.3f}, rz={jaguars.get('hole_rz', 0):.3f}")

                # Enhanced edge calculations
                bengals_pass_edge = bengals.get('lever_explosive_pass', -1.85) - jaguars.get('hole_pass_eff', 0.25)
                bengals_rz_edge = bengals.get('lever_rz', 0.27) - jaguars.get('hole_rz', 1.39)
                jaguars_rush_edge = jaguars.get('lever_ppd', -1.15) - bengals.get('hole_rush_eff', -1.39)
                jaguars_protection_edge = jaguars.get('lever_protection', 0) - bengals.get('hole_pressure', 0)

                return {
                    'bengals_pass_edge': bengals_pass_edge,
                    'bengals_rz_edge': bengals_rz_edge,
                    'jaguars_rush_edge': jaguars_rush_edge,
                    'jaguars_protection_edge': jaguars_protection_edge,
                    'bengals_home_advantage': 0.15,
                    'market_confidence': 0.8
                }
        except Exception as e:
            print(f"⚠️  Using default edges: {e}")

        # Elite default edges based on deep analysis
        return {
            'bengals_pass_edge': 1.2,  # Burrow/Chase vs Jaguars secondary weakness
            'bengals_rz_edge': 1.8,    # Major Bengals RZ advantage
            'jaguars_rush_edge': 0.8,  # Jaguars vs Bengals run D
            'jaguars_protection_edge': 0.3,
            'bengals_home_advantage': 0.15,
            'market_confidence': 0.75
        }

    def fetch_comprehensive_props(self) -> Dict[str, Any]:
        """Fetch comprehensive player props with sharp book prioritization."""
        if not self.api_key:
            print("⚠️  No API key found. Using mock data for demonstration.")
            return self.get_mock_market_data()

        try:
            # Priority hierarchy: Pinnacle > Circa > FanDuel > DraftKings > Others
            sharp_books = ['pinnacle', 'circa', 'fanduel', 'draftkings', 'betmgm', 'caesars']

            # Comprehensive prop markets (20+ markets as requested)
            prop_markets = [
                'player_pass_yds', 'player_pass_tds', 'player_pass_completions', 'player_pass_attempts',
                'player_pass_interceptions', 'player_rush_yds', 'player_rush_tds', 'player_rush_attempts',
                'player_receptions', 'player_reception_yds', 'player_reception_tds',
                'player_rush_reception_yds', 'player_anytime_td', 'player_1st_td',
                'player_tackles_assists', 'player_sacks', 'player_defensive_interceptions',
                'player_kicking_points', 'player_pass_yds_alternate', 'player_rush_yds_alternate',
                'player_reception_yds_alternate', 'player_receptions_alternate', 'player_longest_reception',
                'player_longest_rush', 'player_longest_pass', 'player_fumbles_lost'
            ]

            # Get JAX @ CIN game
            games_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/odds"
            games_params = {
                'api_key': self.api_key,
                'regions': 'us',
                'markets': 'spreads,totals',
                'oddsFormat': 'american'
            }

            games_response = requests.get(games_url, params=games_params)
            games_response.raise_for_status()
            games_data = games_response.json()

            # Find JAX @ CIN game
            target_game = None
            for game in games_data:
                teams = [game['home_team'], game['away_team']]
                if any('Jaguars' in team or 'Jacksonville' in team for team in teams) and \
                   any('Bengals' in team or 'Cincinnati' in team for team in teams):
                    target_game = game
                    break

            if not target_game:
                print("⚠️  JAX @ CIN game not found. Using mock data.")
                return self.get_mock_market_data()

            # Fetch player props for the game
            props_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/events/{target_game['id']}/odds"
            props_params = {
                'api_key': self.api_key,
                'regions': 'us',
                'markets': ','.join(prop_markets),
                'oddsFormat': 'american'
            }

            props_response = requests.get(props_url, params=props_params)
            props_response.raise_for_status()
            props_data = props_response.json()

            return self.process_sharp_props(props_data, sharp_books)

        except Exception as e:
            print(f"⚠️  API fetch failed: {e}. Using mock data.")
            return self.get_mock_market_data()

    def process_sharp_props(self, props_data: Dict[str, Any], sharp_books: List[str]) -> Dict[str, Any]:
        """Process props with sharp book prioritization and market analysis."""
        processed_props = {}

        # Extract bookmaker data with sharp prioritization
        bookmaker_hierarchy = {}
        for bookmaker in props_data.get('bookmakers', []):
            book_key = bookmaker['key'].lower()
            if book_key in sharp_books:
                bookmaker_hierarchy[book_key] = {
                    'priority': sharp_books.index(book_key),
                    'data': bookmaker
                }

        # Process each market with elite prop reading methodology
        for market in props_data.get('bookmakers', [{}])[0].get('markets', []):
            market_key = market['key']

            for outcome in market.get('outcomes', []):
                player_name = outcome.get('description', '').replace(' (', '').replace(')', '')

                if player_name not in processed_props:
                    processed_props[player_name] = {}

                # Get best sharp line (prioritize Pinnacle/Circa)
                sharp_line = self.get_sharp_line(market_key, player_name, bookmaker_hierarchy)
                if sharp_line:
                    processed_props[player_name][market_key] = sharp_line

        return {
            'game_context': self.game_context,
            'player_props': processed_props,
            'market_analysis': self.analyze_market_strength(bookmaker_hierarchy)
        }

    def get_sharp_line(self, market: str, player: str, bookmaker_hierarchy: Dict) -> Optional[Dict]:
        """Get the sharpest available line for a player prop."""
        # Priority: Pinnacle > Circa > Others
        for book_key in ['pinnacle', 'circa', 'fanduel', 'draftkings']:
            if book_key in bookmaker_hierarchy:
                book_data = bookmaker_hierarchy[book_key]['data']
                for market_data in book_data.get('markets', []):
                    if market_data['key'] == market:
                        for outcome in market_data.get('outcomes', []):
                            if player in outcome.get('description', ''):
                                return {
                                    'line': outcome.get('point', 0),
                                    'over_odds': outcome.get('price', 0) if outcome.get('name') == 'Over' else None,
                                    'under_odds': outcome.get('price', 0) if outcome.get('name') == 'Under' else None,
                                    'book': book_key,
                                    'market_strength': self.calculate_market_strength(outcome)
                                }
        return None

    def calculate_market_strength(self, outcome: Dict) -> float:
        """Calculate market strength based on odds and book reputation."""
        price = abs(outcome.get('price', -110))

        # Stronger markets have tighter lines (closer to -110)
        if price <= 105:
            return 0.95  # Very strong
        elif price <= 115:
            return 0.85  # Strong
        elif price <= 125:
            return 0.70  # Moderate
        else:
            return 0.50  # Weak

    def analyze_market_strength(self, bookmaker_hierarchy: Dict) -> Dict[str, float]:
        """Analyze overall market strength and sharp money indicators."""
        return {
            'overall_strength': 0.85 if 'pinnacle' in bookmaker_hierarchy else 0.70,
            'sharp_book_count': len([b for b in bookmaker_hierarchy.keys() if b in ['pinnacle', 'circa']]),
            'market_confidence': 0.80,
            'line_movement_factor': 1.0  # Would track line movement in full implementation
        }

    def get_mock_market_data(self) -> Dict[str, Any]:
        """Enhanced mock data with sharp book simulation for demonstration."""
        return {
            'game_context': self.game_context,
            'market_analysis': {
                'overall_strength': 0.85,
                'sharp_book_count': 2,
                'market_confidence': 0.80,
                'line_movement_factor': 1.0
            },
            'player_props': {
                'Joe Burrow': {
                    'player_pass_yds': {'line': 285.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_pass_tds': {'line': 2.5, 'over_odds': -105, 'under_odds': -115, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_pass_completions': {'line': 24.5, 'over_odds': -110, 'under_odds': -110, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +450, 'under_odds': -650, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                'JaMarr Chase': {
                    'player_reception_yds': {'line': 85.5, 'over_odds': -108, 'under_odds': -112, 'book': 'pinnacle', 'market_strength': 0.95},
                    'player_receptions': {'line': 6.5, 'over_odds': -115, 'under_odds': -105, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +140, 'under_odds': -180, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_longest_reception': {'line': 22.5, 'over_odds': -110, 'under_odds': -110, 'book': 'circa', 'market_strength': 0.75}
                },
                'Tee Higgins': {
                    'player_reception_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_receptions': {'line': 5.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +180, 'under_odds': -240, 'book': 'pinnacle', 'market_strength': 0.80}
                },
                'Chase Brown': {
                    'player_rush_yds': {'line': 75.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_rush_attempts': {'line': 16.5, 'over_odds': -115, 'under_odds': -105, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +160, 'under_odds': -200, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Trevor Lawrence': {
                    'player_pass_yds': {'line': 245.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_pass_tds': {'line': 1.5, 'over_odds': -120, 'under_odds': +100, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_rush_yds': {'line': 25.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +500, 'under_odds': -750, 'book': 'pinnacle', 'market_strength': 0.75}
                },
                'Travis Etienne': {
                    'player_rush_yds': {'line': 65.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.90},
                    'player_rush_attempts': {'line': 14.5, 'over_odds': -105, 'under_odds': -115, 'book': 'circa', 'market_strength': 0.85},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +170, 'under_odds': -220, 'book': 'pinnacle', 'market_strength': 0.85}
                },
                'Brian Thomas Jr': {
                    'player_reception_yds': {'line': 55.5, 'over_odds': -110, 'under_odds': -110, 'book': 'pinnacle', 'market_strength': 0.85},
                    'player_receptions': {'line': 4.5, 'over_odds': -115, 'under_odds': -105, 'book': 'circa', 'market_strength': 0.80},
                    'player_anytime_td': {'line': 0.5, 'over_odds': +200, 'under_odds': -260, 'book': 'pinnacle', 'market_strength': 0.80}
                }
            }
        }

    def apply_elite_prop_methodology(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        """Apply elite prop reading methodology - top 1% Vegas bettor approach."""

        # 🧠 ELITE PROP READING: Read between the lines like sharp bettors
        analysis = {
            'base_projection': 0.0,
            'market_strength_factor': 1.0,
            'sharp_money_factor': 1.0,
            'psychological_factor': 1.0,
            'line_value_factor': 1.0,
            'confidence': 0.5
        }

        if not props:
            return analysis

        # 1. MARKET STRENGTH ANALYSIS
        # Sharp books (Pinnacle/Circa) carry more weight - BUT REALISTIC
        pinnacle_props = {k: v for k, v in props.items() if v.get('book') == 'pinnacle'}
        circa_props = {k: v for k, v in props.items() if v.get('book') == 'circa'}

        if pinnacle_props:
            analysis['market_strength_factor'] = 1.15  # Premium weight for Pinnacle
            analysis['confidence'] += 0.2
        elif circa_props:
            analysis['market_strength_factor'] = 1.10  # Strong weight for Circa
            analysis['confidence'] += 0.15

        # 2. IMPLIED PROBABILITY ANALYSIS
        # Look for market inefficiencies and sharp money indicators
        for prop_type, prop_data in props.items():
            over_odds = prop_data.get('over_odds', -110)
            under_odds = prop_data.get('under_odds', -110)

            # Convert American odds to implied probability
            over_prob = self.american_to_implied_prob(over_odds)
            under_prob = self.american_to_implied_prob(under_odds)
            total_prob = over_prob + under_prob

            # Market efficiency check (should sum to ~1.05-1.08 for sharp books)
            if total_prob < 1.06:  # Very efficient market
                analysis['sharp_money_factor'] *= 1.12
                analysis['confidence'] += 0.1
            elif total_prob > 1.12:  # Inefficient market - opportunity
                analysis['line_value_factor'] *= 1.08

        # 3. PSYCHOLOGICAL FACTORS & MARKET BIAS
        # Elite bettors identify when public/media narrative affects lines
        psychological_adjustments = self.analyze_psychological_factors(player, props)
        analysis['psychological_factor'] = psychological_adjustments['factor']
        analysis['confidence'] += psychological_adjustments['confidence_boost']

        # 4. SHARP VS PUBLIC MONEY INDICATORS
        # Look for line movement patterns and betting percentages
        sharp_indicators = self.detect_sharp_money_patterns(player, props)
        analysis['sharp_money_factor'] *= sharp_indicators['multiplier']

        return analysis

    def american_to_implied_prob(self, odds: int) -> float:
        """Convert American odds to implied probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)

    def analyze_psychological_factors(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        """Analyze psychological factors affecting prop lines."""
        factor = 1.0
        confidence_boost = 0.0

        # Star player bias - public overvalues big names
        star_players = ['Joe Burrow', 'JaMarr Chase', 'Trevor Lawrence']
        if player in star_players:
            # Lines might be inflated due to public money
            factor = 0.95  # Slight fade of inflated lines
            confidence_boost = 0.05

        # Primetime game bias
        if self.game_context.home_team == "Bengals":  # Assuming primetime
            factor *= 0.98  # Public loves overs in primetime
            confidence_boost += 0.03

        # Revenge game narrative (if applicable)
        # Weather concerns (dome game, so minimal impact)

        # Recent performance recency bias
        # (In full implementation, would check last 3 games)

        return {'factor': factor, 'confidence_boost': confidence_boost}

    def detect_sharp_money_patterns(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        """Detect sharp money betting patterns."""
        multiplier = 1.0

        # Look for reverse line movement (line moves against public betting %)
        # Look for early sharp action vs late public money
        # Check for steam moves and synchronized book movements

        # For demonstration - would implement full sharp detection in production
        for prop_type, prop_data in props.items():
            market_strength = prop_data.get('market_strength', 0.5)
            if market_strength > 0.90:  # Very strong market suggests sharp action
                multiplier *= 1.05

        return {'multiplier': multiplier}

    def create_projections(self):
        """Create elite DraftKings projections with comprehensive methodology."""

        print("🏈 ELITE NFL PROJECTION SYSTEM: JAX @ CIN")
        print("=" * 60)

        # Fetch comprehensive props with sharp book prioritization
        market_data = self.fetch_comprehensive_props()
        player_props = market_data['player_props']
        market_analysis = market_data['market_analysis']

        print(f"📊 Market Analysis:")
        print(f"   Overall Strength: {market_analysis['overall_strength']:.2f}")
        print(f"   Sharp Books: {market_analysis['sharp_book_count']}")
        print(f"   Confidence: {market_analysis['market_confidence']:.2f}")
        print()

        projections = {}
        detailed_analysis = {}

        # Get DraftKings player data for accurate roster
        dk_players = self.get_dk_player_data()

        # BENGALS PROJECTIONS
        print("🔥 BENGALS PROJECTIONS:")
        bengals_players = [p for p in dk_players if p['team'] == 'CIN']

        for player_data in bengals_players:
            player_name = player_data['name']
            position = player_data['position']

            if player_name in player_props:
                # Apply elite prop methodology
                prop_analysis = self.apply_elite_prop_methodology(player_name, player_props[player_name])

                # Calculate base projection from props
                base_projection = self.calculate_prop_based_projection(
                    player_name, position, player_props[player_name], prop_analysis
                )

                # Apply team context and edges
                context_multiplier = self.apply_team_context_multipliers(
                    player_name, position, 'CIN', self.team_edges
                )

                final_projection = base_projection * context_multiplier

                projections[player_name] = final_projection
                detailed_analysis[player_name] = {
                    'base_projection': base_projection,
                    'context_multiplier': context_multiplier,
                    'prop_analysis': prop_analysis,
                    'position': position,
                    'team': 'CIN'
                }

                print(f"   {player_name} ({position}): {final_projection:.2f} pts")

        # JAGUARS PROJECTIONS
        print("\n🐆 JAGUARS PROJECTIONS:")
        jaguars_players = [p for p in dk_players if p['team'] == 'JAX']

        for player_data in jaguars_players:
            player_name = player_data['name']
            position = player_data['position']

            if player_name in player_props:
                # Apply elite prop methodology
                prop_analysis = self.apply_elite_prop_methodology(player_name, player_props[player_name])

                # Calculate base projection from props
                base_projection = self.calculate_prop_based_projection(
                    player_name, position, player_props[player_name], prop_analysis
                )

                # Apply team context and edges
                context_multiplier = self.apply_team_context_multipliers(
                    player_name, position, 'JAX', self.team_edges
                )

                final_projection = base_projection * context_multiplier

                projections[player_name] = final_projection
                detailed_analysis[player_name] = {
                    'base_projection': base_projection,
                    'context_multiplier': context_multiplier,
                    'prop_analysis': prop_analysis,
                    'position': position,
                    'team': 'JAX'
                }

                print(f"   {player_name} ({position}): {final_projection:.2f} pts")

        # Add players without props using baseline projections
        self.add_baseline_projections(projections, detailed_analysis, dk_players, player_props)

        # Add team defenses
        self.add_team_defense_projections(projections, detailed_analysis)

        return projections, detailed_analysis

    def get_dk_player_data(self) -> List[Dict[str, str]]:
        """Get DraftKings player roster data for JAX @ CIN."""
        # In production, would fetch from DraftKings API or CSV
        # Using comprehensive roster based on current NFL rosters
        return [
            # BENGALS
            {'name': 'Joe Burrow', 'position': 'QB', 'team': 'CIN'},
            {'name': 'JaMarr Chase', 'position': 'WR', 'team': 'CIN'},
            {'name': 'Tee Higgins', 'position': 'WR', 'team': 'CIN'},
            {'name': 'Tyler Boyd', 'position': 'WR', 'team': 'CIN'},
            {'name': 'Andrei Iosivas', 'position': 'WR', 'team': 'CIN'},
            {'name': 'Chase Brown', 'position': 'RB', 'team': 'CIN'},
            {'name': 'Zack Moss', 'position': 'RB', 'team': 'CIN'},
            {'name': 'Samaje Perine', 'position': 'RB', 'team': 'CIN'},
            {'name': 'Mike Gesicki', 'position': 'TE', 'team': 'CIN'},
            {'name': 'Drew Sample', 'position': 'TE', 'team': 'CIN'},
            {'name': 'Erick All Jr.', 'position': 'TE', 'team': 'CIN'},
            {'name': 'Evan McPherson', 'position': 'K', 'team': 'CIN'},

            # JAGUARS
            {'name': 'Trevor Lawrence', 'position': 'QB', 'team': 'JAX'},
            {'name': 'Mac Jones', 'position': 'QB', 'team': 'JAX'},
            {'name': 'Brian Thomas Jr', 'position': 'WR', 'team': 'JAX'},
            {'name': 'Christian Kirk', 'position': 'WR', 'team': 'JAX'},
            {'name': 'Gabe Davis', 'position': 'WR', 'team': 'JAX'},
            {'name': 'Parker Washington', 'position': 'WR', 'team': 'JAX'},
            {'name': 'Travis Etienne', 'position': 'RB', 'team': 'JAX'},
            {'name': 'Tank Bigsby', 'position': 'RB', 'team': 'JAX'},
            {'name': 'D\'Ernest Johnson', 'position': 'RB', 'team': 'JAX'},
            {'name': 'Evan Engram', 'position': 'TE', 'team': 'JAX'},
            {'name': 'Brenton Strange', 'position': 'TE', 'team': 'JAX'},
            {'name': 'Luke Farrell', 'position': 'TE', 'team': 'JAX'},
            {'name': 'Cam Little', 'position': 'K', 'team': 'JAX'}
        ]

    def apply_team_context_multipliers(self, player: str, position: str, team: str, edges: Dict[str, float]) -> float:
        """Apply team context multipliers based on exploitable edges."""
        multiplier = 1.0

        if team == 'CIN':  # Bengals
            if position == 'QB':
                # Burrow benefits from pass edge and home advantage
                multiplier *= (1 + 0.08 * edges.get('bengals_pass_edge', 0))
                multiplier *= (1 + edges.get('bengals_home_advantage', 0))
            elif position == 'WR':
                # WRs benefit from pass edge and RZ efficiency
                multiplier *= (1 + 0.12 * edges.get('bengals_pass_edge', 0))
                if player in ['JaMarr Chase', 'Tee Higgins']:  # Primary targets
                    multiplier *= (1 + 0.06 * edges.get('bengals_rz_edge', 0))
            elif position == 'RB':
                # RBs benefit from RZ edge
                multiplier *= (1 + 0.10 * edges.get('bengals_rz_edge', 0))
            elif position == 'TE':
                # TEs get moderate benefit from pass edge
                multiplier *= (1 + 0.06 * edges.get('bengals_pass_edge', 0))

        elif team == 'JAX':  # Jaguars
            if position == 'QB':
                # Lawrence benefits from protection edge
                multiplier *= (1 + 0.06 * edges.get('jaguars_protection_edge', 0))
            elif position == 'RB':
                # RBs benefit significantly from rush edge vs Bengals
                multiplier *= (1 + 0.15 * edges.get('jaguars_rush_edge', 0))
            elif position == 'WR':
                # WRs get moderate benefit from protection
                multiplier *= (1 + 0.04 * edges.get('jaguars_protection_edge', 0))

        return multiplier

    def calculate_prop_based_projection(self, player: str, position: str, props: Dict[str, Any],
                                      prop_analysis: Dict[str, float]) -> float:
        """Calculate DraftKings fantasy projection from player props."""
        projection = 0.0

        # Apply all prop analysis factors
        market_factor = prop_analysis['market_strength_factor']
        sharp_factor = prop_analysis['sharp_money_factor']
        psych_factor = prop_analysis['psychological_factor']
        value_factor = prop_analysis['line_value_factor']

        combined_factor = market_factor * sharp_factor * psych_factor * value_factor

        if position == 'QB':
            # Passing yards (0.04 pts per yard)
            if 'player_pass_yds' in props:
                pass_yards = props['player_pass_yds']['line'] * combined_factor
                projection += pass_yards * self.dk_scoring['pass_yard']

            # Passing TDs (4 pts each)
            if 'player_pass_tds' in props:
                pass_tds = props['player_pass_tds']['line'] * combined_factor
                projection += pass_tds * self.dk_scoring['pass_td']

            # Interceptions (-1 pt each)
            projection += 1.2 * self.dk_scoring['pass_int']  # Estimated INTs

            # Rushing yards (0.1 pts per yard)
            if 'player_rush_yds' in props:
                rush_yards = props['player_rush_yds']['line'] * combined_factor
                projection += rush_yards * self.dk_scoring['rush_yard']

            # Anytime TD bonus
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rush_td']

        elif position in ['WR', 'TE']:
            # Receiving yards (0.1 pts per yard)
            if 'player_reception_yds' in props:
                rec_yards = props['player_reception_yds']['line'] * combined_factor
                projection += rec_yards * self.dk_scoring['rec_yard']

            # Receptions (1 pt each)
            if 'player_receptions' in props:
                receptions = props['player_receptions']['line'] * combined_factor
                projection += receptions * self.dk_scoring['reception']

            # Receiving TDs (6 pts each) - estimate from anytime TD odds
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rec_td']

        elif position == 'RB':
            # Rushing yards (0.1 pts per yard)
            if 'player_rush_yds' in props:
                rush_yards = props['player_rush_yds']['line'] * combined_factor
                projection += rush_yards * self.dk_scoring['rush_yard']

            # Rushing TDs (6 pts each)
            if 'player_anytime_td' in props:
                td_prob = self.calculate_anytime_td_probability(props['player_anytime_td'])
                projection += td_prob * self.dk_scoring['rush_td']

            # Receiving (RBs often have receiving props)
            if 'player_reception_yds' in props:
                rec_yards = props['player_reception_yds']['line'] * combined_factor * 0.8  # Conservative
                projection += rec_yards * self.dk_scoring['rec_yard']

            # Estimated receptions for RBs
            if position == 'RB':
                est_receptions = 3.0 if player in ['Chase Brown', 'Travis Etienne'] else 1.5
                projection += est_receptions * self.dk_scoring['reception']

        elif position == 'K':
            # Kicker projections based on team implied totals
            team_total = self.game_context.home_implied if player == 'Evan McPherson' else self.game_context.away_implied

            # Estimate FGs and XPs from team total
            estimated_tds = team_total / 7.0
            estimated_fgs = (team_total - estimated_tds * 7) / 3.0

            projection += estimated_fgs * self.dk_scoring['fg_made']
            projection += estimated_tds * self.dk_scoring['xp_made']

        return max(projection, 0.0)  # No negative projections

    def calculate_anytime_td_probability(self, td_prop: Dict[str, Any]) -> float:
        """Calculate anytime TD probability from market odds."""
        over_odds = td_prop.get('over_odds', +200)

        # Convert American odds to implied probability
        if over_odds > 0:
            implied_prob = 100 / (over_odds + 100)
        else:
            implied_prob = abs(over_odds) / (abs(over_odds) + 100)

        # Adjust for market efficiency and sharp book premium
        if td_prop.get('book') == 'pinnacle':
            return implied_prob * 1.05  # Pinnacle lines are sharper
        elif td_prop.get('book') == 'circa':
            return implied_prob * 1.03
        else:
            return implied_prob

    def add_baseline_projections(self, projections: Dict[str, float],
                               detailed_analysis: Dict[str, Any],
                               dk_players: List[Dict[str, str]],
                               player_props: Dict[str, Any]) -> None:
        """Add baseline projections for players without props."""

        position_baselines = {
            'QB': {'backup': 8.5},
            'WR': {'wr1': 12.0, 'wr2': 9.5, 'wr3': 6.5, 'wr4': 4.0},
            'RB': {'rb1': 11.0, 'rb2': 7.5, 'rb3': 4.0},
            'TE': {'te1': 8.0, 'te2': 5.0, 'te3': 3.0},
            'K': {'starter': 8.0}
        }

        for player_data in dk_players:
            player_name = player_data['name']
            position = player_data['position']
            team = player_data['team']

            if player_name not in projections:
                # Assign baseline based on depth chart position
                baseline = self.get_position_baseline(player_name, position, position_baselines)

                # Apply team context
                context_multiplier = self.apply_team_context_multipliers(
                    player_name, position, team, self.team_edges
                )

                final_projection = baseline * context_multiplier
                projections[player_name] = final_projection

                detailed_analysis[player_name] = {
                    'base_projection': baseline,
                    'context_multiplier': context_multiplier,
                    'prop_analysis': {'confidence': 0.3},  # Low confidence for baseline
                    'position': position,
                    'team': team,
                    'source': 'baseline'
                }

    def get_position_baseline(self, player: str, position: str, baselines: Dict) -> float:
        """Get position-specific baseline projection."""
        if position == 'QB':
            if player in ['Joe Burrow', 'Trevor Lawrence']:
                return 18.0  # Starting QBs
            else:
                return baselines['QB']['backup']
        elif position == 'WR':
            if player in ['JaMarr Chase', 'Brian Thomas Jr']:
                return baselines['WR']['wr1']
            elif player in ['Tee Higgins', 'Christian Kirk']:
                return baselines['WR']['wr2']
            elif player in ['Tyler Boyd', 'Gabe Davis']:
                return baselines['WR']['wr3']
            else:
                return baselines['WR']['wr4']
        elif position == 'RB':
            if player in ['Chase Brown', 'Travis Etienne']:
                return baselines['RB']['rb1']
            elif player in ['Zack Moss', 'Tank Bigsby']:
                return baselines['RB']['rb2']
            else:
                return baselines['RB']['rb3']
        elif position == 'TE':
            if player in ['Mike Gesicki', 'Evan Engram']:
                return baselines['TE']['te1']
            elif player in ['Drew Sample', 'Brenton Strange']:
                return baselines['TE']['te2']
            else:
                return baselines['TE']['te3']
        elif position == 'K':
            return baselines['K']['starter']

        return 5.0  # Default

    def add_team_defense_projections(self, projections: Dict[str, float],
                                   detailed_analysis: Dict[str, Any]) -> None:
        """Add team D/ST projections based on defensive analysis."""

        # BENGALS DEFENSE vs Jaguars
        bengals_dst = self.calculate_defense_projection('Bengals', 'JAX')
        projections['Bengals'] = bengals_dst['projection']
        detailed_analysis['Bengals'] = {
            'base_projection': bengals_dst['projection'],
            'context_multiplier': 1.0,
            'position': 'DST',
            'team': 'CIN',
            'source': 'defense_analysis',
            'breakdown': bengals_dst['breakdown']
        }

        # JAGUARS DEFENSE vs Bengals
        jaguars_dst = self.calculate_defense_projection('Jaguars', 'CIN')
        projections['Jaguars'] = jaguars_dst['projection']
        detailed_analysis['Jaguars'] = {
            'base_projection': jaguars_dst['projection'],
            'context_multiplier': 1.0,
            'position': 'DST',
            'team': 'JAX',
            'source': 'defense_analysis',
            'breakdown': jaguars_dst['breakdown']
        }

    def calculate_defense_projection(self, defense_team: str, opponent: str) -> Dict[str, Any]:
        """Calculate team defense projection with comprehensive analysis."""

        # Base defensive stats and game context
        if defense_team == 'Bengals':
            # Bengals D vs Jaguars offense
            base_sacks = 2.8  # Home defense advantage
            base_ints = 0.9
            base_fumbles = 0.6
            base_tds = 0.2

            # Points allowed projection (Jaguars implied total)
            opponent_points = self.game_context.away_implied  # 23.0

            # Apply team edges - Jaguars have protection edge, reduces sacks
            sacks_adj = base_sacks * (1 - 0.1 * self.team_edges.get('jaguars_protection_edge', 0))

        else:  # Jaguars D
            # Jaguars D vs Bengals offense
            base_sacks = 2.2  # Road defense
            base_ints = 0.7
            base_fumbles = 0.5
            base_tds = 0.15

            # Points allowed projection (Bengals implied total)
            opponent_points = self.game_context.home_implied  # 26.5

            # Apply team edges - Bengals have pass edge, may reduce some defensive stats
            sacks_adj = base_sacks * (1 - 0.05 * self.team_edges.get('bengals_pass_edge', 0))

        # Calculate points allowed scoring
        if opponent_points <= 6:
            pts_allowed_score = self.dk_scoring['dst_pts_0']
        elif opponent_points <= 13:
            pts_allowed_score = self.dk_scoring['dst_pts_1_6']
        elif opponent_points <= 20:
            pts_allowed_score = self.dk_scoring['dst_pts_7_13']
        elif opponent_points <= 27:
            pts_allowed_score = self.dk_scoring['dst_pts_14_20']
        elif opponent_points <= 34:
            pts_allowed_score = self.dk_scoring['dst_pts_21_27']
        else:
            pts_allowed_score = self.dk_scoring['dst_pts_28_34']

        # Calculate total projection
        projection = (
            sacks_adj * self.dk_scoring['dst_sack'] +
            base_ints * self.dk_scoring['dst_int'] +
            base_fumbles * self.dk_scoring['dst_fumble_rec'] +
            base_tds * self.dk_scoring['dst_td'] +
            pts_allowed_score
        )

        breakdown = {
            'sacks': sacks_adj,
            'interceptions': base_ints,
            'fumbles': base_fumbles,
            'tds': base_tds,
            'points_allowed': opponent_points,
            'pts_allowed_score': pts_allowed_score
        }

        return {'projection': projection, 'breakdown': breakdown}

def main():
    """Generate and output elite projections."""

    # Initialize the elite projection system
    system = EliteProjectionSystem()

    # Generate comprehensive projections
    projections, detailed_analysis = system.create_projections()

    print("\n" + "="*60)
    print("🏆 FINAL ELITE PROJECTIONS - JAX @ CIN")
    print("="*60)

    # Sort by projection descending
    sorted_projections = sorted(projections.items(), key=lambda x: x[1], reverse=True)

    # Output in requested CSV format
    print("\nPlayer,Projection")
    for player, points in sorted_projections:
        print(f"{player},{points:.2f}")

    # Additional analysis output
    print(f"\n📊 PROJECTION SUMMARY:")
    print(f"Total Players: {len(projections)}")
    print(f"Highest Projection: {sorted_projections[0][0]} ({sorted_projections[0][1]:.2f})")
    print(f"Team Defenses Included: Bengals ({projections.get('Bengals', 0):.2f}), Jaguars ({projections.get('Jaguars', 0):.2f})")

    # Save detailed analysis
    timestamp = datetime.now().strftime("%Y%m%d_%H%M")
    filename = f"jax_cin_elite_projections_{timestamp}.csv"

    with open(filename, 'w') as f:
        f.write("Player,Projection\n")
        for player, points in sorted_projections:
            f.write(f"{player},{points:.2f}\n")

    print(f"\n💾 Projections saved to: {filename}")

    return projections, detailed_analysis

if __name__ == "__main__":
    main()
