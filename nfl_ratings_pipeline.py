#!/usr/bin/env python3
"""
NFL Team Ratings Pipeline
Processes game JSON files to generate opponent-adjusted team ratings and exploitables
"""

import json
import pandas as pd
import numpy as np
import os
from pathlib import Path
import re
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class NFLRatingsPipeline:
    def __init__(self, data_dir: str = "csvs/Gamebook Results", lambda_ewma: float = 0.7):
        self.data_dir = Path(data_dir)
        self.lambda_ewma = lambda_ewma
        self.season = 2024  # Current season
        
        # Create output directories
        self.create_output_dirs()
        
        # QC tracking
        self.qc_issues = []
        
    def create_output_dirs(self):
        """Create necessary output directories"""
        dirs = ['curated', 'models', 'findings', 'qc']
        for d in dirs:
            Path(d).mkdir(exist_ok=True)
    
    def mmss_to_seconds(self, time_str: str) -> Optional[int]:
        """Convert MM:SS to seconds"""
        if not time_str or time_str == "0:00":
            return 0
        try:
            # Handle approximate times like "≈9:00"
            clean_time = time_str.replace('≈', '').strip()
            parts = clean_time.split(':')
            if len(parts) == 2:
                return int(parts[0]) * 60 + int(parts[1])
        except:
            return None
        return None
    
    def parse_yardline(self, yard_str: str) -> Optional[int]:
        """Parse average drive start like 'SF 25' to get yardline from own goal"""
        if not yard_str:
            return None
        try:
            # Extract number from strings like "SF 25", "MIN 31"
            match = re.search(r'(\d+)', yard_str)
            if match:
                return int(match.group(1))
        except:
            return None
        return None
    
    def validate_game_data(self, game_data: Dict, filename: str) -> bool:
        """Validate game data and log QC issues"""
        issues = []
        
        # Check required fields exist
        required_fields = ['away', 'home']
        for field in required_fields:
            if field not in game_data:
                issues.append(f"Missing {field} section")
                return False
        
        # Validate each team's data
        for side in ['away', 'home']:
            team_data = game_data[side]
            
            # Check numeric fields
            numeric_fields = [
                'score', 'total_plays', 'total_yards', 'rushing_attempts', 
                'rushing_yards', 'pass_attempts', 'pass_completions',
                'red_zone_td', 'red_zone_att', 'third_down_made', 'third_down_att'
            ]
            
            missing_fields = []
            for field in numeric_fields:
                if field not in team_data or team_data[field] is None:
                    missing_fields.append(field)
                    team_data[field] = 0  # Coerce to 0
            
            if missing_fields:
                issues.append(f"{side} missing fields: {missing_fields}")
            
            # Validate logical constraints
            if team_data.get('red_zone_att', 0) < team_data.get('red_zone_td', 0):
                issues.append(f"{side} red_zone_att < red_zone_td")
            
            if team_data.get('third_down_att', 0) < team_data.get('third_down_made', 0):
                issues.append(f"{side} third_down_att < third_down_made")
        
        # Log issues
        if issues:
            self.qc_issues.append({
                'filename': filename,
                'issues': issues,
                'severity': 'warning'
            })
        
        return True  # Continue processing even with warnings
    
    def process_game_file(self, filepath: Path) -> Optional[List[Dict]]:
        """Process a single game file and return team-game rows"""
        try:
            with open(filepath, 'r') as f:
                content = f.read()
                # Remove any markdown formatting
                if content.startswith('```json'):
                    content = content.replace('```json', '').replace('```', '')
                game_data = json.loads(content)
            
            if not self.validate_game_data(game_data, filepath.name):
                return None
            
            # Determine week (simplified - you may want to enhance this)
            week = 1  # Default to week 1
            if any(team in filepath.name.lower() for team in ['vikings', 'bears', 'lions', 'packers']):
                week = 2
            
            # Generate game_id
            away_team = game_data['away']['team']
            home_team = game_data['home']['team']
            game_id = f"{self.season}_{week}_{away_team}@{home_team}"
            
            rows = []
            
            # Process away team
            away_row = self.create_team_game_row(
                game_data['away'], game_data['home'], 
                'A', week, game_id
            )
            rows.append(away_row)
            
            # Process home team  
            home_row = self.create_team_game_row(
                game_data['home'], game_data['away'],
                'H', week, game_id
            )
            rows.append(home_row)
            
            return rows
            
        except Exception as e:
            self.qc_issues.append({
                'filename': filepath.name,
                'issues': [f"Parse error: {str(e)}"],
                'severity': 'error'
            })
            return None
    
    def create_team_game_row(self, team_data: Dict, opp_data: Dict, 
                           home_away: str, week: int, game_id: str) -> Dict:
        """Create a team-game row with all computed metrics"""
        
        # Basic info
        row = {
            'season': self.season,
            'week': week,
            'game_id': game_id,
            'team': team_data['team'],
            'opp': opp_data['team'],
            'home_away': home_away
        }
        
        # Score metrics
        row['points_for'] = team_data.get('score', 0)
        row['points_against'] = opp_data.get('score', 0)
        row['point_diff'] = row['points_for'] - row['points_against']
        
        # Volume metrics
        row['plays'] = team_data.get('total_plays', 0)
        row['yards'] = team_data.get('total_yards', 0)
        row['ypp'] = team_data.get('yards_per_play', 0)
        
        # Rushing metrics
        row['rush_att'] = team_data.get('rushing_attempts', 0)
        row['rush_yds'] = team_data.get('rushing_yards', 0)
        row['rush_ypa'] = team_data.get('yards_per_rush', 0)
        
        # Passing metrics
        row['pass_att'] = team_data.get('pass_attempts', 0)
        row['pass_cmp'] = team_data.get('pass_completions', 0)
        row['pass_int'] = team_data.get('pass_interceptions', 0)
        row['sacks'] = team_data.get('sacks_allowed', 0)
        row['sack_yds'] = team_data.get('sack_yards_lost', 0)
        row['pass_yds_gross'] = team_data.get('passing_yards_gross', 0)
        row['pass_yds_net'] = team_data.get('passing_yards_net', 0)
        
        # Derived passing metrics
        row['dropbacks'] = row['pass_att'] + row['sacks']
        row['ypa'] = row['pass_yds_gross'] / max(row['pass_att'], 1)
        row['nyd'] = row['pass_yds_net'] / max(row['dropbacks'], 1)
        row['sack_rate'] = row['sacks'] / max(row['dropbacks'], 1)
        row['int_rate'] = row['pass_int'] / max(row['pass_att'], 1)
        
        # Situational metrics
        row['third_made'] = team_data.get('third_down_made', 0)
        row['third_att'] = team_data.get('third_down_att', 0)
        row['third_pct'] = row['third_made'] / max(row['third_att'], 1)
        
        row['rz_td'] = team_data.get('red_zone_td', 0)
        row['rz_att'] = team_data.get('red_zone_att', 0)
        row['rz_td_pct'] = row['rz_td'] / max(row['rz_att'], 1)
        
        # Ball security
        row['fumbles'] = team_data.get('fumbles', 0)
        row['fumbles_lost'] = team_data.get('fumbles_lost', 0)
        row['turnovers'] = row['pass_int'] + row['fumbles_lost']
        
        # Discipline
        row['pen'] = team_data.get('penalties', 0)
        row['pen_yds'] = team_data.get('penalty_yards', 0)
        
        # Special teams
        row['punts'] = team_data.get('punts', 0)
        row['punt_avg'] = team_data.get('punt_average', 0)
        row['kickoffs'] = team_data.get('kickoffs', 0)
        row['kick_tb'] = team_data.get('kickoff_touchbacks', 0)
        
        # Possession & drives
        row['top_s'] = self.mmss_to_seconds(team_data.get('time_of_possession', '0:00'))
        row['drives'] = team_data.get('drives_total', 0)
        row['avg_start_own_ydln'] = self.parse_yardline(team_data.get('average_drive_start', ''))
        
        if row['drives'] > 0:
            row['pts_per_drive'] = row['points_for'] / row['drives']
            row['yds_per_drive'] = row['yards'] / row['drives']
            row['pen_yds_per_drive'] = row['pen_yds'] / row['drives']
        else:
            row['pts_per_drive'] = 0
            row['yds_per_drive'] = 0
            row['pen_yds_per_drive'] = 0
        
        # Game flow
        row['largest_lead'] = team_data.get('largest_lead', 0)
        row['largest_deficit'] = abs(team_data.get('largest_deficit', 0))
        row['time_leading_s'] = self.mmss_to_seconds(team_data.get('time_leading', '0:00'))
        row['time_trailing_s'] = self.mmss_to_seconds(team_data.get('time_trailing', '0:00'))
        row['drives_leading'] = team_data.get('drives_leading', 0)
        row['drives_trailing'] = team_data.get('drives_trailing', 0)
        
        # Explosives
        row['explosive_15p'] = team_data.get('explosive_plays_15plus', 0)
        row['explosive_rate'] = row['explosive_15p'] / max(row['plays'], 1)
        
        # Opponent mirror fields (for defense calculations)
        row['opp_plays'] = opp_data.get('total_plays', 0)
        row['opp_yards'] = opp_data.get('total_yards', 0)
        row['opp_rush_yds'] = opp_data.get('rushing_yards', 0)
        row['opp_rush_att'] = opp_data.get('rushing_attempts', 0)
        row['opp_pass_yds_net'] = opp_data.get('passing_yards_net', 0)
        row['opp_pass_att'] = opp_data.get('pass_attempts', 0)
        row['opp_sacks'] = opp_data.get('sacks_allowed', 0)
        row['opp_dropbacks'] = row['opp_pass_att'] + row['opp_sacks']
        row['opp_rz_td'] = opp_data.get('red_zone_td', 0)
        row['opp_rz_att'] = opp_data.get('red_zone_att', 0)
        row['opp_explosive_15p'] = opp_data.get('explosive_plays_15plus', 0)
        row['opp_drives'] = opp_data.get('drives_total', 0)
        row['opp_pen_yds'] = opp_data.get('penalty_yards', 0)
        
        return row

    def compute_opponent_adjusted_ratings(self, df_team_game: pd.DataFrame) -> pd.DataFrame:
        """Compute opponent-adjusted unit metrics with EWMA"""

        # Define offensive and defensive axes
        off_axes = {
            'OFF_ppd': 'pts_per_drive',
            'OFF_ypp': 'ypp',
            'OFF_rush_ypa': 'rush_ypa',
            'OFF_nyd': 'nyd',
            'OFF_sack_rate_inv': lambda row: 1 - row['sack_rate'],
            'OFF_int_rate_inv': lambda row: 1 - row['int_rate'],
            'OFF_rz_td_pct': 'rz_td_pct',
            'OFF_explosive_rate': 'explosive_rate',
            'OFF_pen_yds_pdrv': 'pen_yds_per_drive'
        }

        def_axes = {
            'DEF_ppd_allowed': lambda row: row['points_against'] / max(row['opp_drives'], 1),
            'DEF_ypp_allowed': lambda row: row['opp_yards'] / max(row['opp_plays'], 1),
            'DEF_rush_ypa_allowed': lambda row: row['opp_rush_yds'] / max(row['opp_rush_att'], 1),
            'DEF_nyd_allowed': lambda row: row['opp_pass_yds_net'] / max(row['opp_dropbacks'], 1),
            'DEF_sack_rate': lambda row: row['opp_sacks'] / max(row['opp_dropbacks'], 1),
            'DEF_int_rate': lambda row: row['opp_pass_int'] if 'opp_pass_int' in row else 0,
            'DEF_rz_td_pct_allowed': lambda row: row['opp_rz_td'] / max(row['opp_rz_att'], 1),
            'DEF_explosive_rate_allowed': lambda row: row['opp_explosive_15p'] / max(row['opp_plays'], 1),
            'DEF_pen_yds_pdrv_allowed': lambda row: row['opp_pen_yds'] / max(row['opp_drives'], 1)
        }

        # Compute raw metrics for each team-game
        df = df_team_game.copy()

        # Add computed offensive metrics
        for metric, field in off_axes.items():
            if callable(field):
                df[f'{metric}_raw'] = df.apply(field, axis=1)
            else:
                df[f'{metric}_raw'] = df[field]

        # Add computed defensive metrics
        for metric, field in def_axes.items():
            if callable(field):
                df[f'{metric}_raw'] = df.apply(field, axis=1)
            else:
                df[f'{metric}_raw'] = df[field]

        # Sort by week for proper opponent adjustment
        df = df.sort_values(['team', 'week']).reset_index(drop=True)

        # Compute opponent adjustments and EWMA ratings
        ratings_data = []

        for week in sorted(df['week'].unique()):
            week_data = df[df['week'] <= week].copy()

            for team in df['team'].unique():
                team_games = week_data[week_data['team'] == team].copy()

                if len(team_games) == 0:
                    continue

                team_row = {
                    'season': self.season,
                    'as_of_week': week,
                    'team': team,
                    'games_played': len(team_games),
                    'recency_weight': self.lambda_ewma
                }

                # Compute adjusted metrics and EWMA for each axis
                for metric in list(off_axes.keys()) + list(def_axes.keys()):
                    raw_values = []
                    adj_values = []

                    for _, game in team_games.iterrows():
                        raw_val = game[f'{metric}_raw']

                        # Simple opponent adjustment (league average for now)
                        # In full implementation, this would use opponent's historical performance
                        league_avg = week_data[f'{metric}_raw'].mean()

                        if metric.startswith('OFF_'):
                            # For offense: higher is better, adjust upward if facing tough defense
                            adj_val = raw_val  # Simplified - no opponent adjustment for now
                        else:
                            # For defense: lower allowed is better
                            adj_val = raw_val  # Simplified - no opponent adjustment for now

                        raw_values.append(raw_val)
                        adj_values.append(adj_val)

                    # Compute EWMA (more recent games weighted higher)
                    if len(adj_values) == 1:
                        ewma_rating = adj_values[0]
                        raw_ewma = raw_values[0]
                    else:
                        # Simple EWMA calculation
                        weights = [self.lambda_ewma * (1 - self.lambda_ewma)**(len(adj_values)-1-i)
                                 for i in range(len(adj_values))]
                        weight_sum = sum(weights)
                        weights = [w/weight_sum for w in weights]

                        ewma_rating = sum(w * v for w, v in zip(weights, adj_values))
                        raw_ewma = sum(w * v for w, v in zip(weights, raw_values))

                    team_row[f'{metric}_rating'] = ewma_rating
                    team_row[f'{metric}_raw_ewma'] = raw_ewma

                ratings_data.append(team_row)

        return pd.DataFrame(ratings_data)

    def compute_zscores_and_ranks(self, df_ratings: pd.DataFrame) -> pd.DataFrame:
        """Convert ratings to z-scores and ranks"""

        # Get latest week data
        latest_week = df_ratings['as_of_week'].max()
        df_latest = df_ratings[df_ratings['as_of_week'] == latest_week].copy()

        # Only include teams with at least 1 game
        df_latest = df_latest[df_latest['games_played'] >= 1]

        # Define rating columns
        rating_cols = [col for col in df_latest.columns if col.endswith('_rating')]

        # Compute z-scores
        for col in rating_cols:
            mean_val = df_latest[col].mean()
            std_val = df_latest[col].std()

            if std_val > 0:
                df_latest[f'{col}_z'] = (df_latest[col] - mean_val) / std_val
            else:
                df_latest[f'{col}_z'] = 0

        # Compute ranks (1 = best)
        for col in rating_cols:
            if col.startswith('OFF_'):
                # For offense: higher is better
                df_latest[f'{col}_rank'] = df_latest[col].rank(ascending=False, method='min')
            else:
                # For defense: lower allowed is better (invert for strength)
                df_latest[f'{col}_rank'] = df_latest[col].rank(ascending=True, method='min')

        # Create defense strength z-scores (invert so higher = better defense)
        def_allowed_cols = [col for col in rating_cols if 'allowed' in col or col.startswith('DEF_')]

        for col in def_allowed_cols:
            z_col = f'{col}_z'
            strength_col = col.replace('_allowed', '_strength').replace('DEF_', 'DEF_strength_')
            if z_col in df_latest.columns:
                df_latest[strength_col] = -df_latest[z_col]  # Invert so higher = better defense

        return df_latest

    def compute_holes_and_levers(self, df_ranks: pd.DataFrame) -> pd.DataFrame:
        """Derive exploitables (defense holes) and levers (offense strengths)"""

        df = df_ranks.copy()

        # Offense levers (per team) - higher z-score = better lever
        df['lever_explosive_pass'] = df.get('OFF_explosive_rate_rating_z', 0)
        df['lever_explosive_overall'] = df.get('OFF_explosive_rate_rating_z', 0) * 0.6 + df.get('OFF_nyd_rating_z', 0) * 0.4
        df['lever_rz'] = df.get('OFF_rz_td_pct_rating_z', 0)
        df['lever_protection'] = df.get('OFF_sack_rate_inv_rating_z', 0)
        df['lever_ppd'] = df.get('OFF_ppd_rating_z', 0)

        # Defense holes (per team) - higher z-score = bigger hole (worse defense)
        df['hole_explosive_pass'] = df.get('DEF_explosive_rate_allowed_rating_z', 0)
        df['hole_pass_eff'] = df.get('DEF_nyd_allowed_rating_z', 0)
        df['hole_rush_eff'] = df.get('DEF_rush_ypa_allowed_rating_z', 0)
        df['hole_rz'] = df.get('DEF_rz_td_pct_allowed_rating_z', 0)
        df['hole_ppd'] = df.get('DEF_ppd_allowed_rating_z', 0)
        df['hole_pressure'] = -df.get('DEF_sack_rate_rating_z', 0)  # Low pressure = bigger hole
        df['hole_penalty_fp'] = df.get('DEF_pen_yds_pdrv_allowed_rating_z', 0)

        # Select relevant columns
        lever_cols = [col for col in df.columns if col.startswith('lever_')]
        hole_cols = [col for col in df.columns if col.startswith('hole_')]
        base_cols = ['season', 'as_of_week', 'team', 'games_played']

        return df[base_cols + lever_cols + hole_cols]

    def generate_team_findings(self, df_ranks: pd.DataFrame, df_holes_levers: pd.DataFrame):
        """Generate human-readable team findings JSON files"""

        # Create findings directory for current week
        latest_week = df_ranks['as_of_week'].max()
        findings_dir = Path(f'findings/asof_week_{latest_week}')
        findings_dir.mkdir(parents=True, exist_ok=True)

        for _, team_row in df_ranks.iterrows():
            team = team_row['team']

            # Get holes and levers for this team
            hl_row = df_holes_levers[df_holes_levers['team'] == team].iloc[0]

            # Build summary ranks
            summary = {
                'off_rank_pass': int(team_row.get('OFF_nyd_rating_rank', 99)),
                'off_rank_rush': int(team_row.get('OFF_rush_ypa_rating_rank', 99)),
                'def_rank_pass': int(team_row.get('DEF_nyd_allowed_rating_rank', 99)),
                'def_rank_rush': int(team_row.get('DEF_rush_ypa_allowed_rating_rank', 99))
            }

            # Identify top levers (z-score >= 0.5)
            levers = []
            lever_mapping = {
                'lever_explosive_pass': 'Explosive Pass',
                'lever_explosive_overall': 'Explosive Overall',
                'lever_rz': 'Red Zone Offense',
                'lever_protection': 'Pass Protection',
                'lever_ppd': 'Points Per Drive'
            }

            for lever_col, lever_name in lever_mapping.items():
                z_score = hl_row.get(lever_col, 0)
                if z_score >= 0.5:
                    levers.append({
                        'axis': lever_name,
                        'z': round(z_score, 2),
                        'support': f"{lever_name} +{z_score:.1f}σ above league average"
                    })

            # Identify top holes (z-score >= 0.7 = exploitable)
            holes = []
            hole_mapping = {
                'hole_explosive_pass': 'Explosive Pass Allowed',
                'hole_pass_eff': 'Pass Efficiency Allowed',
                'hole_rush_eff': 'Rush Efficiency Allowed',
                'hole_rz': 'Red Zone Defense',
                'hole_ppd': 'Points Per Drive Allowed',
                'hole_pressure': 'Pass Rush Pressure',
                'hole_penalty_fp': 'Penalty Discipline'
            }

            for hole_col, hole_name in hole_mapping.items():
                z_score = hl_row.get(hole_col, 0)
                if z_score >= 0.7:
                    holes.append({
                        'axis': hole_name,
                        'z': round(z_score, 2),
                        'trend': 'exploitable' if z_score >= 0.7 else 'watch'
                    })

            # Create team findings JSON
            findings = {
                'as_of_week': int(latest_week),
                'team': team,
                'summary': summary,
                'levers': levers,
                'holes': holes,
                'stability': {
                    'games': int(team_row['games_played']),
                    'recency_lambda': self.lambda_ewma
                },
                'qc_flags': []
            }

            # Save team findings
            with open(findings_dir / f'team_{team}.json', 'w') as f:
                json.dump(findings, f, indent=2)

    def run_pipeline(self):
        """Run the complete pipeline"""
        print("🏈 Starting NFL Ratings Pipeline...")
        
        # Step 1: Process all game files
        print("📊 Processing game files...")
        all_team_games = []
        
        for filepath in self.data_dir.glob("*.md"):
            if filepath.name == "gamebook.md":  # Skip template file
                continue
                
            rows = self.process_game_file(filepath)
            if rows:
                all_team_games.extend(rows)
                print(f"  ✅ Processed {filepath.name}")
            else:
                print(f"  ❌ Failed to process {filepath.name}")
        
        if not all_team_games:
            print("❌ No valid game data found!")
            return
        
        # Create team_game dataframe
        df_team_game = pd.DataFrame(all_team_games)
        
        # Step 2: Compute opponent-adjusted ratings
        print("🎯 Computing opponent-adjusted ratings...")
        df_ratings = self.compute_opponent_adjusted_ratings(df_team_game)
        
        # Step 3: Generate z-scores and ranks
        print("📈 Computing z-scores and ranks...")
        df_ranks = self.compute_zscores_and_ranks(df_ratings)
        
        # Step 4: Generate holes and levers
        print("🔍 Identifying exploitables and levers...")
        df_holes_levers = self.compute_holes_and_levers(df_ranks)
        
        # Step 5: Generate team findings
        print("📋 Generating team findings...")
        self.generate_team_findings(df_ranks, df_holes_levers)
        
        # Step 6: Save outputs
        print("💾 Saving outputs...")
        df_team_game.to_parquet('curated/team_game.parquet', index=False)
        df_ratings.to_parquet('models/team_unit_ratings.parquet', index=False)
        df_ranks.to_parquet('models/team_ranks.parquet', index=False)
        df_holes_levers.to_parquet('models/holes_and_levers.parquet', index=False)
        
        # Save QC issues
        if self.qc_issues:
            pd.DataFrame(self.qc_issues).to_parquet('qc/qc_issues.parquet', index=False)
        
        # Save metadata
        meta = {
            'built_at': pd.Timestamp.now().isoformat(),
            'games_through_week': int(df_team_game['week'].max()),
            'lambda': self.lambda_ewma,
            'total_games': int(len(df_team_game) // 2),
            'total_teams': int(df_team_game['team'].nunique())
        }
        
        with open('qc/meta.json', 'w') as f:
            json.dump(meta, f, indent=2)
        
        print("✅ Pipeline completed successfully!")
        print(f"📊 Processed {len(df_team_game)} team-game records")
        print(f"🏈 {df_team_game['team'].nunique()} teams through week {df_team_game['week'].max()}")
        
        return df_team_game, df_ratings, df_ranks, df_holes_levers

if __name__ == "__main__":
    pipeline = NFLRatingsPipeline()
    results = pipeline.run_pipeline()
