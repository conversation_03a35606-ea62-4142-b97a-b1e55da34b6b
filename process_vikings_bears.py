#!/usr/bin/env python3
"""
Process just the Vikings vs Bears gamebook to demonstrate the system
"""

from gamebook_intelligence import NFLGamebookEngine
import json

def main():
    print("🏈 NFL GAMEBOOK INTELLIGENCE ENGINE - SINGLE GAME DEMO")
    print("="*60)
    print("Processing: Vikings vs Bears")
    print("="*60)
    
    # Initialize the engine
    engine = NFLGamebookEngine()
    
    # Process just the Vikings vs Bears game
    game_record = engine.process_single_gamebook("Vikings vs Bears.md")
    
    if game_record:
        print("\n🎉 SUCCESS! Game processed successfully")
        
        # Display the extracted JSON-like output
        print("\n" + "="*80)
        print("📊 EXTRACTED GAME RECORD (JSON FORMAT)")
        print("="*80)
        
        # Create the output in the requested format
        output = {
            "game": game_record.game,
            "away": game_record.away,
            "home": game_record.home,
            "advanced_metrics": game_record.advanced_metrics
        }
        
        print(json.dumps(output, indent=2))
        
        # Show team profiles
        print("\n" + "="*80)
        print("🏈 UPDATED TEAM PROFILES")
        print("="*80)
        
        for team_name, profile in engine.team_profiles.items():
            print(f"\n📊 {team_name.upper()} PROFILE:")
            print(f"   Games Played: {profile.games_played}")
            print(f"   Points/Game: {profile.points_per_game:.1f}")
            print(f"   Yards/Game: {profile.total_yards_per_game:.1f}")
            print(f"   Yards/Play: {profile.yards_per_play:.2f}")
            print(f"   3rd Down %: {profile.third_down_conversion_rate:.1%}")
            print(f"   Red Zone %: {profile.red_zone_td_rate:.1%}")
            print(f"   Explosive Plays/Game: {profile.explosive_plays_per_game:.1f}")
            print(f"   Penalties/Game: {profile.penalties_per_game:.1f}")
            print(f"   Offensive Rating: {profile.offensive_rating:.3f}")
        
        # Show rankings
        print("\n" + "="*80)
        print("🏆 CURRENT LEAGUE RANKINGS")
        print("="*80)
        engine.display_current_rankings()
        
    else:
        print("❌ Failed to process the game")

if __name__ == "__main__":
    main()
