#!/usr/bin/env python3
"""
IMPROVED NFL Projection Engine v2.0
Incorporating learnings from WAS @ GB post-game analysis
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any
import json


class ImprovedProjectionEngine:
    """Enhanced projection engine with post-game learnings integrated."""
    
    def __init__(self):
        # Updated weighting based on analysis
        self.weights = {
            'props': 0.40,      # Reduced from 0.50
            'models': 0.35,     # Increased from 0.30  
            'context': 0.25     # Increased from 0.20
        }
        
        # Sportsbook accuracy weighting (based on WAS@GB analysis)
        self.book_weights = {
            'circa': 0.35,      # Most accurate
            'pinnacle': 0.35,   # Most accurate
            'betmgm': 0.20,     # Moderately accurate
            'betrivers': 0.10,  # Less accurate
            'fanduel': 0.10     # Least accurate
        }
        
        # Position-specific adjustments
        self.position_adjustments = {
            'mobile_qb_road_rush': 0.75,    # 25% reduction
            'home_te_targets': 1.15,        # 15% increase
            'trailing_rb_touches': 0.85,    # 15% reduction when trailing
            'trailing_pass_attempts': 1.10  # 10% increase when trailing
        }
        
        # Injury risk discounts
        self.injury_discounts = {
            'questionable': 0.90,           # 10% discount
            'recent_injury_history': 0.95   # 5% discount
        }
    
    def analyze_sharp_money_consensus(self, prop_data: Dict) -> Dict[str, Any]:
        """Enhanced sharp money detection requiring 3+ book consensus."""
        
        sharp_books = ['circa', 'pinnacle', 'betcris', 'bookmaker']
        consensus_threshold = 3
        
        sharp_signals = {}
        
        for market in prop_data:
            market_data = prop_data[market]
            sharp_book_lines = []
            
            for book, line_data in market_data.items():
                if book.lower() in sharp_books:
                    sharp_book_lines.append(line_data['line'])
            
            if len(sharp_book_lines) >= consensus_threshold:
                # Calculate consensus line
                consensus_line = np.median(sharp_book_lines)
                line_std = np.std(sharp_book_lines)
                
                # Determine if there's sharp money movement
                public_books = [book for book in market_data.keys() 
                              if book.lower() not in sharp_books]
                
                if public_books:
                    public_lines = [market_data[book]['line'] for book in public_books]
                    public_median = np.median(public_lines)
                    
                    # Sharp money signal if 2+ point difference
                    if abs(consensus_line - public_median) >= 2.0:
                        sharp_signals[market] = {
                            'sharp_consensus': consensus_line,
                            'public_median': public_median,
                            'confidence': min(len(sharp_book_lines) / 5.0, 1.0),
                            'direction': 'sharp_under' if consensus_line < public_median else 'sharp_over'
                        }
        
        return sharp_signals
    
    def apply_game_script_adjustments(self, projections: Dict, game_info: Dict) -> Dict:
        """Apply enhanced game script adjustments based on spread and game flow."""
        
        spread = game_info.get('spread', 0)
        total = game_info.get('total', 45)
        
        adjusted_projections = projections.copy()
        
        # Heavy favorite adjustments (spread > 7)
        if abs(spread) > 7:
            favorite_team = game_info.get('favorite_team')
            underdog_team = game_info.get('underdog_team')
            
            for player, projection in projections.items():
                player_info = game_info.get('players', {}).get(player, {})
                player_team = player_info.get('team')
                position = player_info.get('position')
                
                if player_team == favorite_team:
                    # Favorite team adjustments
                    if position == 'RB':
                        adjusted_projections[player] *= 1.15  # More touches
                    elif position in ['WR', 'TE']:
                        adjusted_projections[player] *= 0.95  # Fewer garbage time targets
                        
                elif player_team == underdog_team:
                    # Underdog team adjustments  
                    if position == 'RB':
                        adjusted_projections[player] *= self.position_adjustments['trailing_rb_touches']
                    elif position == 'QB':
                        # More pass attempts when trailing
                        pass_component = projection * 0.6  # Assume 60% from passing
                        adjusted_projections[player] = (pass_component * self.position_adjustments['trailing_pass_attempts'] + 
                                                      projection * 0.4)
                    elif position in ['WR', 'TE']:
                        adjusted_projections[player] *= 1.08  # More targets when trailing
        
        return adjusted_projections
    
    def apply_position_specific_adjustments(self, projections: Dict, player_info: Dict) -> Dict:
        """Apply position-specific adjustments based on learnings."""
        
        adjusted_projections = projections.copy()
        
        for player, projection in projections.items():
            player_data = player_info.get(player, {})
            position = player_data.get('position')
            is_home = player_data.get('is_home', False)
            is_mobile_qb = player_data.get('is_mobile_qb', False)
            
            # Mobile QB road rushing adjustment
            if position == 'QB' and is_mobile_qb and not is_home:
                # Reduce rushing component (assume 30% of QB projection is rushing)
                rush_component = projection * 0.30
                pass_component = projection * 0.70
                
                adjusted_rush = rush_component * self.position_adjustments['mobile_qb_road_rush']
                adjusted_projections[player] = pass_component + adjusted_rush
            
            # Home TE target share boost
            elif position == 'TE' and is_home:
                adjusted_projections[player] *= self.position_adjustments['home_te_targets']
        
        return adjusted_projections
    
    def apply_injury_risk_adjustments(self, projections: Dict, injury_report: Dict) -> Dict:
        """Apply injury probability discounts."""
        
        adjusted_projections = projections.copy()
        
        for player, projection in projections.items():
            injury_data = injury_report.get(player, {})
            
            # Current week injury status
            if injury_data.get('status') == 'Questionable':
                adjusted_projections[player] *= self.injury_discounts['questionable']
            
            # Recent injury history
            if injury_data.get('recent_injury_history', False):
                adjusted_projections[player] *= self.injury_discounts['recent_injury_history']
        
        return adjusted_projections
    
    def calculate_improved_projections(self, 
                                    prop_data: Dict,
                                    model_projections: Dict,
                                    game_context: Dict,
                                    player_info: Dict,
                                    injury_report: Dict = None) -> Dict[str, float]:
        """Calculate projections using improved methodology."""
        
        # 1. Enhanced sharp money analysis
        sharp_signals = self.analyze_sharp_money_consensus(prop_data)
        
        # 2. Calculate weighted prop projections with improved book weighting
        prop_projections = {}
        for player in model_projections:
            if player in prop_data:
                weighted_line = 0
                total_weight = 0
                
                for book, line_data in prop_data[player].items():
                    book_weight = self.book_weights.get(book.lower(), 0.15)  # Default weight
                    weighted_line += line_data['implied_projection'] * book_weight
                    total_weight += book_weight
                
                if total_weight > 0:
                    prop_projections[player] = weighted_line / total_weight
                else:
                    prop_projections[player] = model_projections[player]
            else:
                prop_projections[player] = model_projections[player]
        
        # 3. Apply sharp money adjustments
        for player in prop_projections:
            if player in sharp_signals:
                signal = sharp_signals[player]
                confidence = signal['confidence']
                
                if signal['direction'] == 'sharp_over':
                    prop_projections[player] *= (1 + 0.1 * confidence)
                else:
                    prop_projections[player] *= (1 - 0.1 * confidence)
        
        # 4. Combine projections with new weighting
        combined_projections = {}
        for player in model_projections:
            prop_proj = prop_projections.get(player, model_projections[player])
            model_proj = model_projections[player]
            context_proj = game_context.get(player, model_projections[player])
            
            combined_projections[player] = (
                prop_proj * self.weights['props'] +
                model_proj * self.weights['models'] +
                context_proj * self.weights['context']
            )
        
        # 5. Apply game script adjustments
        combined_projections = self.apply_game_script_adjustments(
            combined_projections, game_context
        )
        
        # 6. Apply position-specific adjustments
        combined_projections = self.apply_position_specific_adjustments(
            combined_projections, player_info
        )
        
        # 7. Apply injury risk adjustments
        if injury_report:
            combined_projections = self.apply_injury_risk_adjustments(
                combined_projections, injury_report
            )
        
        return combined_projections
    
    def generate_prop_recommendations_v2(self, 
                                       projections: Dict,
                                       prop_lines: Dict,
                                       confidence_threshold: float = 0.08) -> List[Dict]:
        """Generate improved prop recommendations with higher confidence threshold."""
        
        recommendations = []
        
        for player, projection in projections.items():
            if player in prop_lines:
                for market, line_data in prop_lines[player].items():
                    line = line_data['line']
                    
                    # Calculate edge with improved confidence threshold
                    edge = abs(projection - line) / line
                    
                    if edge >= confidence_threshold:  # Increased from 0.05
                        recommendation = {
                            'player': player,
                            'market': market,
                            'line': line,
                            'projection': projection,
                            'recommendation': 'OVER' if projection > line else 'UNDER',
                            'edge': edge,
                            'confidence': min(edge / 0.15, 1.0)  # Scale confidence
                        }
                        recommendations.append(recommendation)
        
        # Sort by edge size
        recommendations.sort(key=lambda x: x['edge'], reverse=True)
        
        return recommendations


def main():
    """Example usage of improved projection engine."""
    
    engine = ImprovedProjectionEngine()
    
    print("🚀 IMPROVED NFL PROJECTION ENGINE v2.0")
    print("=" * 50)
    
    print("\n📊 Key Improvements:")
    print(f"  • Prop weighting reduced: 50% → {engine.weights['props']:.0%}")
    print(f"  • Model weighting increased: 30% → {engine.weights['models']:.0%}")
    print(f"  • Context weighting increased: 20% → {engine.weights['context']:.0%}")
    
    print(f"\n🏈 Position Adjustments:")
    print(f"  • Mobile QB road rushing: {engine.position_adjustments['mobile_qb_road_rush']:.0%} of original")
    print(f"  • Home TE targets: {engine.position_adjustments['home_te_targets']:.0%} of original")
    print(f"  • Trailing RB touches: {engine.position_adjustments['trailing_rb_touches']:.0%} of original")
    
    print(f"\n📚 Sportsbook Weighting:")
    for book, weight in engine.book_weights.items():
        print(f"  • {book.title()}: {weight:.0%}")
    
    print(f"\n🏥 Injury Discounts:")
    print(f"  • Questionable status: {engine.injury_discounts['questionable']:.0%}")
    print(f"  • Recent injury history: {engine.injury_discounts['recent_injury_history']:.0%}")
    
    print(f"\n✅ Ready for next game analysis!")


if __name__ == "__main__":
    main()
