#!/usr/bin/env python3
"""
Focused projections script for Ravens vs Bills game only.
Runs full pipeline with offense and defense projections.
"""

import pandas as pd
import numpy as np
import json
import os
from pathlib import Path
from typing import Dict, List, Tuple, Any
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def load_ravens_bills_props():
    """Load Ravens and Bills player props from the text file."""
    print("Loading Ravens and Bills player props...")
    
    props_file = Path('csvs/buff and ravens odds.txt')
    if not props_file.exists():
        print(f"Warning: {props_file} not found")
        return pd.DataFrame()
    
    # Read the file and parse the first line which contains the props data
    with open(props_file, 'r') as f:
        first_line = f.readline().strip()
    
    # Split by spaces and parse the prop data
    props_data = []
    parts = first_line.split(' ')
    
    i = 0
    while i < len(parts):
        if ',' in parts[i] and len(parts[i].split(',')) >= 4:
            # This looks like a prop line: player_name,market,line,over_odds,under_odds
            prop_parts = parts[i].split(',')
            if len(prop_parts) >= 4:
                props_data.append({
                    'player_name': prop_parts[0],
                    'market': prop_parts[1],
                    'line': float(prop_parts[2]) if prop_parts[2].replace('.', '').replace('-', '').isdigit() else 0,
                    'over_odds': int(prop_parts[3]) if prop_parts[3].replace('-', '').isdigit() else 0,
                    'under_odds': int(prop_parts[4]) if len(prop_parts) > 4 and prop_parts[4].replace('-', '').isdigit() else 0
                })
        i += 1
    
    return pd.DataFrame(props_data)

def load_depth_charts():
    """Load Ravens and Bills depth charts from the text file."""
    print("Loading Ravens and Bills depth charts...")
    
    depth_file = Path('csvs/buff and ravens odds.txt')
    if not depth_file.exists():
        print(f"Warning: {depth_file} not found")
        return pd.DataFrame()
    
    # Parse depth chart data from the file
    ravens_depth = {
        'QB': [('Lamar Jackson', 1), ('Cooper Rush', 2)],
        'RB': [('Derrick Henry', 1), ('Justice Hill', 2), ('Keaton Mitchell', 3), ('Rasheen Ali', 4)],
        'WR': [('Zay Flowers', 1), ('Rashod Bateman', 2), ('DeAndre Hopkins', 3)],
        'TE': [('Mark Andrews', 1), ('Isaiah Likely', 2), ('Charlie Kolar', 3)]
    }
    
    bills_depth = {
        'QB': [('Josh Allen', 1), ('Mitchell Trubisky', 2)],
        'RB': [('James Cook', 1), ('Ray Davis', 2), ('Ty Johnson', 3)],
        'WR': [('Khalil Shakir', 1), ('Keon Coleman', 2), ('Joshua Palmer', 3)],
        'TE': [('Dalton Kincaid', 1), ('Dawson Knox', 2), ('Jackson Hawes', 3)]
    }
    
    depth_data = []
    
    # Ravens players
    for pos, players in ravens_depth.items():
        for player_name, depth_order in players:
            depth_data.append({
                'player_name': player_name,
                'team': 'BAL',
                'position': pos,
                'depth_chart_order': depth_order,
                'is_starter': depth_order == 1
            })
    
    # Bills players
    for pos, players in bills_depth.items():
        for player_name, depth_order in players:
            depth_data.append({
                'player_name': player_name,
                'team': 'BUF',
                'position': pos,
                'depth_chart_order': depth_order,
                'is_starter': depth_order == 1
            })
    
    return pd.DataFrame(depth_data)

def fetch_current_odds():
    """Fetch current Ravens vs Bills odds from The Odds API."""
    print("Fetching current Ravens vs Bills odds...")
    
    api_key = os.getenv('ODDS_API_KEY')
    if not api_key:
        print("Warning: ODDS_API_KEY not found, using default odds")
        return get_default_game_context()
    
    try:
        from src.proj.fetch_odds import get_totals_spreads, implied_team_totals
        
        # Fetch current odds
        odds_data = get_totals_spreads(api_key)
        
        # Find Ravens vs Bills game
        ravens_bills_game = None
        for game in odds_data:
            home_team = game['home_team'].lower()
            away_team = game['away_team'].lower()
            
            if (('ravens' in home_team or 'baltimore' in home_team) and 
                ('bills' in away_team or 'buffalo' in away_team)) or \
               (('bills' in home_team or 'buffalo' in home_team) and 
                ('ravens' in away_team or 'baltimore' in away_team)):
                ravens_bills_game = game
                break
        
        if not ravens_bills_game:
            print("Ravens vs Bills game not found in current odds, using defaults")
            return get_default_game_context()
        
        # Extract odds from first bookmaker
        bookmaker = ravens_bills_game['bookmakers'][0]
        spreads = next((m for m in bookmaker['markets'] if m['key'] == 'spreads'), None)
        totals = next((m for m in bookmaker['markets'] if m['key'] == 'totals'), None)
        
        if spreads and totals:
            # Get spread and total
            total = totals['outcomes'][0]['point']
            
            # Find Bills spread (assuming Bills are home team)
            bills_spread = 0
            for outcome in spreads['outcomes']:
                if 'bills' in outcome['name'].lower() or 'buffalo' in outcome['name'].lower():
                    bills_spread = outcome['point']
                    break
            
            # Calculate implied team totals
            bills_total = (total - bills_spread) / 2
            ravens_total = (total + bills_spread) / 2
            
            return {
                'total': total,
                'spread': bills_spread,  # Bills perspective
                'bills_total': bills_total,
                'ravens_total': ravens_total,
                'game_script': 'competitive' if abs(bills_spread) < 3 else 'blowout_risk',
                'weather': get_weather_context()
            }
    
    except Exception as e:
        print(f"Error fetching odds: {e}")
        return get_default_game_context()

def get_default_game_context():
    """Get default game context if API fails."""
    return {
        'total': 47.0,
        'spread': -1.5,  # Bills favored by 1.5
        'bills_total': 24.25,
        'ravens_total': 22.75,
        'game_script': 'competitive',
        'weather': get_weather_context()
    }

def get_weather_context():
    """Get weather context for Buffalo game."""
    # Based on current forecasts for Buffalo tonight
    return {
        'temperature': 25,  # Fahrenheit
        'wind_speed': 15,   # mph
        'precipitation': 'light_snow',
        'conditions': 'cold_windy'
    }

def create_offensive_projections(depth_df, props_df, game_context):
    """Create offensive projections for Ravens and Bills players."""
    print("Creating offensive projections...")
    
    projections = []
    
    for _, player in depth_df.iterrows():
        team = player['team']
        player_name = player['player_name']
        position = player['position']
        depth_order = player['depth_chart_order']
        is_starter = player['is_starter']
        
        # Skip non-offensive positions
        if position not in ['QB', 'RB', 'WR', 'TE']:
            continue
        
        # Base projection based on position and depth
        base_proj = get_base_projection(position, depth_order, is_starter)
        
        # Apply player props adjustments
        player_props = props_df[props_df['player_name'] == player_name]
        if len(player_props) > 0:
            base_proj = adjust_for_props(base_proj, player_props, position)
            print(f"  {player_name} ({position}): Props adjustment applied, base={base_proj:.2f}")
        
        # Apply game context adjustments
        final_proj = apply_game_context(base_proj, team, position, game_context)
        
        projections.append({
            'Player': player_name,
            'Projection': round(final_proj, 2),
            'team': team,
            'position': position,
            'depth_order': depth_order,
            'is_starter': is_starter,
            'type': 'offense'
        })
    
    return pd.DataFrame(projections)

def create_defensive_projections(game_context):
    """Create team defense projections for Ravens and Bills."""
    print("Creating defensive projections...")
    
    # Base defensive projections
    ravens_def_base = 9.2  # Ravens defense base (strong defense)
    bills_def_base = 8.1   # Bills defense base
    
    # Adjust based on opponent totals and weather
    weather_factor = 0.9 if game_context['weather']['conditions'] == 'cold_windy' else 1.0
    
    ravens_def_proj = ravens_def_base * (1 + (25 - game_context['bills_total']) / 40) * weather_factor
    bills_def_proj = bills_def_base * (1 + (25 - game_context['ravens_total']) / 40) * weather_factor
    
    projections = [
        {
            'Player': 'Baltimore Ravens Defense',
            'Projection': round(ravens_def_proj, 2),
            'team': 'BAL',
            'position': 'DEF',
            'depth_order': 1,
            'type': 'defense'
        },
        {
            'Player': 'Buffalo Bills Defense',
            'Projection': round(bills_def_proj, 2),
            'team': 'BUF',
            'position': 'DEF',
            'depth_order': 1,
            'type': 'defense'
        }
    ]
    
    return pd.DataFrame(projections)

def get_base_projection(position, depth_order, is_starter):
    """Get base projection by position and depth."""
    if position == 'QB':
        if depth_order == 1:
            return 20.5  # Starting QB
        else:
            return 4.0   # Backup QB
    elif position == 'RB':
        if depth_order == 1:
            return 15.0  # RB1
        elif depth_order == 2:
            return 8.0   # RB2
        else:
            return 3.0   # Deep backup
    elif position == 'WR':
        if depth_order == 1:
            return 14.5  # WR1
        elif depth_order == 2:
            return 10.5  # WR2
        elif depth_order == 3:
            return 7.0   # WR3
        else:
            return 4.0   # WR4+
    elif position == 'TE':
        if depth_order == 1:
            return 10.0  # TE1
        elif depth_order == 2:
            return 5.0   # TE2
        else:
            return 2.5   # Deep TE
    
    return 2.0  # Default

def adjust_for_props(base_proj, player_props, position):
    """Adjust projection based on player props."""
    if len(player_props) == 0:
        return base_proj
    
    adjustment = 1.0
    
    # Look for key volume props
    if position == 'QB':
        pass_yards = player_props[player_props['market'] == 'pass_yards']
        if len(pass_yards) > 0:
            line = pass_yards['line'].iloc[0]
            if line > 250:
                adjustment *= 1.3
            elif line > 220:
                adjustment *= 1.15
            elif line < 200:
                adjustment *= 0.9
    
    elif position == 'RB':
        rush_yards = player_props[player_props['market'] == 'rush_yards']
        rec_yards = player_props[player_props['market'] == 'rec_yards']
        
        total_yards_line = 0
        if len(rush_yards) > 0:
            total_yards_line += rush_yards['line'].iloc[0]
        if len(rec_yards) > 0:
            total_yards_line += rec_yards['line'].iloc[0]
        
        if total_yards_line > 100:
            adjustment *= 1.4
        elif total_yards_line > 70:
            adjustment *= 1.2
        elif total_yards_line < 40:
            adjustment *= 0.8
    
    elif position in ['WR', 'TE']:
        rec_yards = player_props[player_props['market'] == 'rec_yards']
        if len(rec_yards) > 0:
            yards_line = rec_yards['line'].iloc[0]
            if yards_line > 60:
                adjustment *= 1.4
            elif yards_line > 40:
                adjustment *= 1.25
            elif yards_line < 25:
                adjustment *= 0.8
    
    # TD props boost
    td_props = player_props[player_props['market'] == 'td_anytime']
    if len(td_props) > 0:
        adjustment *= 1.1
    
    return base_proj * adjustment

def apply_game_context(base_proj, team, position, game_context):
    """Apply game context adjustments including weather."""
    adjustment = 1.0
    
    # Team total adjustments
    team_total = game_context['bills_total'] if team == 'BUF' else game_context['ravens_total']
    
    if team_total > 25:
        adjustment *= 1.2
    elif team_total < 22:
        adjustment *= 0.85
    
    # Weather adjustments for cold/windy conditions
    weather = game_context['weather']
    if weather['conditions'] == 'cold_windy':
        if position == 'QB':
            adjustment *= 0.95  # Slightly harder to throw
        elif position == 'RB':
            adjustment *= 1.05  # More rushing in bad weather
        elif position in ['WR', 'TE']:
            adjustment *= 0.92  # Harder to catch in cold/wind
    
    # Game script adjustments
    spread = game_context['spread']
    if team == 'BUF' and spread < 0:  # Bills favored
        adjustment *= 1.02
    elif team == 'BAL' and spread > 0:  # Ravens underdog (more passing)
        if position == 'QB':
            adjustment *= 1.08
        elif position in ['WR', 'TE']:
            adjustment *= 1.05
    
    return base_proj * adjustment

def main():
    """Main pipeline for Ravens vs Bills projections."""
    print("=== RAVENS vs BILLS FOCUSED PROJECTIONS ===")
    
    # Load data
    props_df = load_ravens_bills_props()
    depth_df = load_depth_charts()
    
    # Get current game context (odds + weather)
    game_context = fetch_current_odds()
    
    print(f"\nGame Context:")
    print(f"  Total: {game_context['total']}")
    print(f"  Bills spread: {game_context['spread']}")
    print(f"  Bills total: {game_context['bills_total']:.1f}")
    print(f"  Ravens total: {game_context['ravens_total']:.1f}")
    print(f"  Weather: {game_context['weather']['temperature']}°F, {game_context['weather']['conditions']}")
    
    # Create projections
    offensive_proj = create_offensive_projections(depth_df, props_df, game_context)
    defensive_proj = create_defensive_projections(game_context)
    
    # Combine projections
    all_projections = pd.concat([offensive_proj, defensive_proj], ignore_index=True)
    
    # Sort by projection descending
    all_projections = all_projections.sort_values('Projection', ascending=False)
    
    # Display results
    print(f"\n=== FINAL PROJECTIONS ({len(all_projections)} players) ===")
    print(all_projections[['Player', 'Projection', 'team', 'position']].to_string(index=False))
    
    # Save results
    output_file = 'ravens_bills_projections.csv'
    all_projections[['Player', 'Projection']].to_csv(output_file, index=False)
    print(f"\nProjections saved to {output_file}")
    
    return all_projections

if __name__ == "__main__":
    projections = main()
