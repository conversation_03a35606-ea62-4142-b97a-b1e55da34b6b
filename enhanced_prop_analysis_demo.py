"""Demonstration of enhanced prop odds analysis for better projections."""

import pandas as pd
from pathlib import Path
from src.proj.prop_signal_extractor import PropSignalExtractor
from src.proj.advanced_game_context import AdvancedGameContextAnalyzer
from src.proj.prop_driven_projections import <PERSON>pDrivenProjector


def parse_ravens_bills_props():
    """Parse the Ravens-Bills props from the text file."""
    props_file = Path('csvs/buff and ravens odds.txt')
    
    with open(props_file, 'r') as f:
        content = f.read()
    
    # Parse the first line which contains all the props
    first_line = content.split('\n')[0]
    
    # Split and parse props
    props = []
    parts = first_line.split(' ')
    
    current_prop = []
    for part in parts:
        if ',' in part:
            prop_parts = part.split(',')
            if len(prop_parts) >= 5:
                try:
                    props.append({
                        'player_name': prop_parts[0],
                        'market': prop_parts[1], 
                        'line': float(prop_parts[2]),
                        'over_odds': int(prop_parts[3]),
                        'under_odds': int(prop_parts[4])
                    })
                except (ValueError, IndexError):
                    continue
    
    return props


def demonstrate_prop_signal_extraction():
    """Demonstrate advanced prop signal extraction."""
    print("=== ENHANCED PROP ODDS ANALYSIS ===\n")
    
    # Parse props
    props_data = parse_ravens_bills_props()
    print(f"Parsed {len(props_data)} prop markets")
    
    # Initialize extractor
    extractor = PropSignalExtractor()
    
    # Extract signals
    player_signals = extractor.extract_all_signals(props_data)
    
    print(f"\nExtracted signals for {len(player_signals)} players")
    
    # Analyze key players
    key_players = ['Josh Allen', 'Lamar Jackson', 'Derrick Henry', 'Zay Flowers', 'Keon Coleman']
    
    for player in key_players:
        if player in player_signals:
            print(f"\n--- {player.upper()} SIGNAL ANALYSIS ---")
            signals = player_signals[player]
            
            for signal in signals:
                print(f"Market: {signal.market}")
                print(f"  Line: {signal.line}")
                print(f"  Implied Mean: {signal.implied_mean:.2f}")
                print(f"  Volume Signal: {signal.volume_signal:.2f}")
                print(f"  Efficiency Signal: {signal.efficiency_signal:.2f}")
                print(f"  Scoring Signal: {signal.scoring_signal:.3f}")
                print(f"  Market Confidence: {signal.market_confidence:.2f}")
                print(f"  Sharp Money Indicator: {signal.sharp_money_indicator:.2f}")
                print(f"  Game Script Bias: {signal.game_script_bias:.2f}")
                print()


def demonstrate_game_context_analysis():
    """Demonstrate advanced game context analysis."""
    print("\n=== ADVANCED GAME CONTEXT ANALYSIS ===\n")
    
    # Parse props for context
    props_data = parse_ravens_bills_props()
    extractor = PropSignalExtractor()
    player_signals = extractor.extract_all_signals(props_data)
    
    # Initialize context analyzer
    analyzer = AdvancedGameContextAnalyzer()
    
    # Example game parameters (you'd get these from odds API)
    total = 47.0
    spread = -1.5  # Bills favored by 1.5
    home_team = 'BUF'
    away_team = 'BAL'
    
    # Weather data example
    weather_data = {
        'temperature': 25,
        'wind_speed': 15,
        'precipitation': 'light_snow'
    }
    
    # Build comprehensive context
    context = analyzer.build_comprehensive_context(
        total, spread, home_team, away_team, player_signals, weather_data
    )
    
    print("GAME CONTEXT INSIGHTS:")
    print(f"Total: {context.total}")
    print(f"Spread: {context.spread} (Bills favored)")
    print(f"Implied Pace: {context.implied_pace:.1f} plays/game")
    print(f"Pass Rate - BUF: {context.pass_rate_home:.1%}")
    print(f"Pass Rate - BAL: {context.pass_rate_away:.1%}")
    print(f"Weather Impact: {context.weather_impact:.2f}")
    
    print(f"\nSCRIPT PROBABILITIES:")
    for script, prob in context.script_probabilities.items():
        print(f"  {script}: {prob:.1%}")
    
    print(f"\nQB RUSHING BOOSTS:")
    for team, boost in context.qb_rushing_boost.items():
        print(f"  {team}: {boost:.2f}x")
    
    print(f"\nRED ZONE EFFICIENCY:")
    for team, eff in context.rz_efficiency.items():
        print(f"  {team}: {eff:.1%}")
    
    print(f"\nMARKET SENTIMENT:")
    print(f"  Sharp Money Lean: {context.sharp_money_lean}")
    print(f"  Public Money Lean: {context.public_money_lean}")


def demonstrate_prop_driven_projections():
    """Demonstrate prop-driven projections."""
    print("\n=== PROP-DRIVEN PROJECTIONS ===\n")
    
    # Read props file
    props_file = Path('csvs/buff and ravens odds.txt')
    with open(props_file, 'r') as f:
        props_text = f.read()
    
    # Initialize projector
    projector = PropDrivenProjector()
    
    # Game parameters
    total = 47.0
    spread = -1.5
    home_team = 'BUF'
    away_team = 'BAL'
    
    weather_data = {
        'temperature': 25,
        'wind_speed': 15
    }
    
    # Create projections
    projections = projector.create_projections_from_props(
        props_text, total, spread, home_team, away_team, weather_data
    )
    
    print("PROP-DRIVEN PROJECTIONS:")
    print("Player                Position  Team  Fantasy Pts  Floor   Ceiling  Confidence")
    print("-" * 80)
    
    for proj in projections[:10]:  # Top 10
        print(f"{proj.player_name:<18} {proj.position:<8} {proj.team:<4} "
              f"{proj.fantasy_points:>8.1f}    {proj.floor:>5.1f}   {proj.ceiling:>6.1f}   "
              f"{proj.projection_confidence:>6.1%}")
    
    # Show detailed breakdown for top player
    if projections:
        top_proj = projections[0]
        print(f"\n--- DETAILED BREAKDOWN: {top_proj.player_name} ---")
        print(f"Volume Projections:")
        for stat, value in top_proj.volume_projection.items():
            print(f"  {stat}: {value:.1f}")
        
        print(f"Efficiency Projections:")
        for stat, value in top_proj.efficiency_projection.items():
            print(f"  {stat}: {value:.2f}")
        
        print(f"Scoring Projections:")
        for stat, value in top_proj.scoring_projection.items():
            print(f"  {stat}: {value:.3f}")


def analyze_prop_market_inefficiencies():
    """Analyze potential market inefficiencies."""
    print("\n=== PROP MARKET INEFFICIENCY ANALYSIS ===\n")
    
    props_data = parse_ravens_bills_props()
    extractor = PropSignalExtractor()
    player_signals = extractor.extract_all_signals(props_data)
    
    print("POTENTIAL MARKET INEFFICIENCIES:")
    print("Player               Market          Sharp Signal  Vig    Inefficiency")
    print("-" * 75)
    
    inefficiencies = []
    
    for player, signals in player_signals.items():
        for signal in signals:
            # Calculate vig from the original props data
            # Find the original prop for this signal
            original_prop = None
            for prop in props_data:
                if prop['player_name'] == player and prop['market'] == signal.market:
                    original_prop = prop
                    break

            if original_prop:
                p_over = extractor.american_to_prob(original_prop['over_odds'])
                p_under = extractor.american_to_prob(original_prop['under_odds'])
                vig = (p_over + p_under) - 1.0
            else:
                vig = 0.05  # Default vig
            
            # Look for high sharp money signal with reasonable vig
            if signal.sharp_money_indicator > 0.7 and vig < 0.1:
                inefficiency_score = signal.sharp_money_indicator * (1 - vig)
                inefficiencies.append({
                    'player': player,
                    'market': signal.market,
                    'sharp_signal': signal.sharp_money_indicator,
                    'vig': vig,
                    'inefficiency': inefficiency_score
                })
    
    # Sort by inefficiency score
    inefficiencies.sort(key=lambda x: x['inefficiency'], reverse=True)
    
    for ineff in inefficiencies[:10]:
        print(f"{ineff['player']:<18} {ineff['market']:<14} "
              f"{ineff['sharp_signal']:>8.2f}     {ineff['vig']:>4.1%}   "
              f"{ineff['inefficiency']:>8.2f}")


def main():
    """Run all demonstrations."""
    try:
        demonstrate_prop_signal_extraction()
        demonstrate_game_context_analysis()
        demonstrate_prop_driven_projections()
        analyze_prop_market_inefficiencies()
        
        print("\n=== KEY TAKEAWAYS FOR BETTER PROP READING ===")
        print("1. Extract volume, efficiency, and scoring signals separately")
        print("2. Weight props by market confidence (inverse of vig)")
        print("3. Identify sharp vs public money through multiple indicators")
        print("4. Adjust for game script probabilities derived from spread/total")
        print("5. Use QB rushing props to gauge mobile QB upside")
        print("6. Correlate TD props with red zone efficiency expectations")
        print("7. Factor weather impact into passing game projections")
        print("8. Look for market inefficiencies in low-vig, high-sharp-signal props")
        
    except Exception as e:
        print(f"Error running demonstration: {e}")
        print("Make sure the props file exists at csvs/buff and ravens odds.txt")


if __name__ == "__main__":
    main()
