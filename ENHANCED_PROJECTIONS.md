# Enhanced NFL Projections Engine

The Enhanced Projections Engine integrates the improved logic developed from the DET vs GB analysis into the main projection system. This provides more accurate projections by incorporating player props, depth charts, and game context.

## Key Improvements

### 1. **Enhanced Base Projections**
- More realistic position-based projections
- Depth chart order consideration
- Starter vs backup differentiation

### 2. **Advanced Player Props Integration**
- Position-specific props logic
- Volume-based adjustments (passing yards, rushing yards, receiving yards)
- TD props boost for scoring potential
- Market-implied usage patterns

### 3. **Sophisticated Game Context**
- Real odds data integration
- Team total adjustments
- Game script awareness (competitive, high-scoring, low-scoring)
- Spread-based adjustments

### 4. **Team Defense Projections**
- Opponent-adjusted defense projections
- Game context integration for defensive scoring

## Usage

### Command Line Interface

```bash
# Generate enhanced projections for specific teams
PYTHONPATH=src python -m proj.cli enhanced-project \
  --depth data/depth_week1.parquet \
  --player-props data/player_props.parquet \
  --odds data/odds_week1.json \
  --teams DET GB \
  --output enhanced_projections.csv
```

### Python API

```python
from proj.enhanced_projections import EnhancedProjectionEngine

# Initialize engine
engine = EnhancedProjectionEngine()

# Load data
engine.load_data(
    depth_file='data/depth_week1.parquet',
    props_file='data/player_props.parquet', 
    odds_file='data/odds_week1.json'
)

# Generate projections
projections = engine.generate_projections(['DET', 'GB'], 'output.csv')
```

## Projection Logic

### Base Projections by Position

**Quarterbacks:**
- QB1: 19.5 points
- Backup: 4.0 points

**Running Backs:**
- RB1: 14.2 points
- RB2: 8.5 points
- Deep backup: 3.5 points

**Wide Receivers:**
- WR1: 13.8 points
- WR2: 10.2 points
- WR3: 6.8 points
- WR4+: 4.2 points

**Tight Ends:**
- TE1: 9.5 points
- TE2: 4.8 points
- Deep TE: 2.5 points

**Team Defense:**
- Base: 8.0 points (adjusted by opponent strength)

### Player Props Adjustments

**Quarterbacks:**
- Pass yards > 260: +25% boost
- Pass yards > 230: +10% boost
- Pass yards < 200: -15% penalty

**Running Backs:**
- Combined rush + receiving yards > 90: +30% boost
- Combined yards > 60: +15% boost
- Combined yards < 30: -20% penalty

**Wide Receivers / Tight Ends:**
- Receiving yards > 70: +35% boost
- Receiving yards > 50: +20% boost
- Receiving yards < 25: -25% penalty

**TD Props:**
- Any TD props: +10% boost

### Game Context Adjustments

**Team Total Impact:**
- Team total > 25: +15% boost
- Team total > 23: +8% boost
- Team total < 20: -15% penalty
- Team total < 22: -8% penalty

**Game Script Adjustments:**

*Competitive Games:*
- QB: +5% (more passing)
- RB: +2% (balanced attack)

*High-Scoring Games:*
- QB/WR/TE: +10% (more passing)
- RB: -5% (less running)

*Low-Scoring Games:*
- RB: +8% (more running)
- WR/TE: -10% (less passing)

**Spread Adjustments:**
- Favored teams: +3% boost
- Underdogs (QB only): +5% boost (garbage time)

## Output Format

The enhanced projections include:

- `player_name`: Player name
- `team`: Team abbreviation
- `position`: Position (QB, RB, WR, TE, DEF)
- `depth_order`: Depth chart position
- `is_starter`: Boolean starter status
- `proj_mean`: Main projection
- `p50`: 50th percentile projection
- `p75`: 75th percentile projection
- `p90`: 90th percentile projection
- `confidence`: Confidence score (0-1)

## Example Results

For DET vs GB (based on actual odds: Total 47.5, DET -1.5):

**Top Projections:**
1. Jared Goff (QB): 24.30
2. Jordan Love (QB): 24.30
3. Amon-Ra St. Brown (WR): 20.64
4. Jahmyr Gibbs (RB): 19.87
5. Josh Jacobs (RB): 17.58

## Integration with Existing System

The enhanced engine is fully integrated with the existing CLI and can be used alongside the current projection system. It uses the same data sources but applies more sophisticated logic for better accuracy.

## Future Enhancements

- Weather integration
- Injury status adjustments
- Historical matchup data
- Advanced game script modeling
- Machine learning integration
