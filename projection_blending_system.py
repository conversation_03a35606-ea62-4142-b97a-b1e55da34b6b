"""
Practical implementation of the 50/30/20 projection blending system.
Based on Ravens-Bills analysis showing 84% correlation success.
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional


class ProjectionBlender:
    """Blend props, models, and context into optimal projections."""
    
    def __init__(self):
        # DraftKings scoring system
        self.scoring = {
            'pass_yards': 0.04, 'pass_tds': 4, 'interceptions': -1,
            'rush_yards': 0.1, 'rush_tds': 6, 'fumbles': -1,
            'rec_yards': 0.1, 'receptions': 1, 'rec_tds': 6
        }
        
        # Model weights based on Ravens-Bills correlation analysis
        self.model_weights = {
            'fc_proj': 0.6,      # 84.4% correlation
            'my_proj': 0.4,      # 81.6% correlation
            'vegas_pts': 0.0     # -8.3% correlation (ignore)
        }
    
    def extract_props_signals(self, player_props: Dict) -> Dict[str, float]:
        """
        Extract the 50% props-based component.
        
        Args:
            player_props: Dict with keys like 'rec_yards_line', 'td_anytime_prob', etc.
        
        Returns:
            Dict with volume, efficiency, and scoring projections
        """
        signals = {
            'volume_projection': 0,
            'efficiency_projection': 0, 
            'scoring_projection': 0
        }
        
        # VOLUME SIGNALS (from props lines)
        volume_points = 0
        
        # Receiving volume
        if 'rec_yards_line' in player_props:
            # Convert receiving yards to fantasy points
            rec_yards = player_props['rec_yards_line']
            # Estimate receptions: rec_yards / 11.2 (avg YPR)
            est_receptions = rec_yards / 11.2
            volume_points += (rec_yards * self.scoring['rec_yards'] + 
                            est_receptions * self.scoring['receptions'])
        
        # Rushing volume  
        if 'rush_yards_line' in player_props:
            rush_yards = player_props['rush_yards_line']
            volume_points += rush_yards * self.scoring['rush_yards']
        
        # Passing volume
        if 'pass_yards_line' in player_props:
            pass_yards = player_props['pass_yards_line']
            # Estimate pass TDs: pass_yards / 300 * 2 (rough ratio)
            est_pass_tds = (pass_yards / 300) * 2
            volume_points += (pass_yards * self.scoring['pass_yards'] + 
                            est_pass_tds * self.scoring['pass_tds'])
        
        signals['volume_projection'] = volume_points
        
        # EFFICIENCY SIGNALS (from prop odds)
        efficiency_multiplier = 1.0
        
        # If props have tight lines (market confident), boost efficiency
        if 'market_confidence' in player_props:
            confidence = player_props['market_confidence']
            efficiency_multiplier = 1.0 + (confidence - 0.5) * 0.2
        
        signals['efficiency_projection'] = volume_points * (efficiency_multiplier - 1.0)
        
        # SCORING SIGNALS (from TD props)
        scoring_points = 0
        
        if 'td_anytime_prob' in player_props:
            td_prob = player_props['td_anytime_prob']
            # Convert probability to expected TDs
            expected_tds = td_prob
            scoring_points = expected_tds * self.scoring['rec_tds']  # Use rec_tds as default
        
        signals['scoring_projection'] = scoring_points
        
        return signals
    
    def blend_models(self, fc_proj: float, my_proj: float, vegas_pts: float) -> float:
        """
        Create the 30% model-based component.
        
        Args:
            fc_proj: FC Projection value
            my_proj: My Projection value  
            vegas_pts: Vegas Points (will be ignored due to poor correlation)
        
        Returns:
            Blended model projection
        """
        # Weight by correlation performance from Ravens-Bills analysis
        blended = (fc_proj * self.model_weights['fc_proj'] + 
                  my_proj * self.model_weights['my_proj'] + 
                  vegas_pts * self.model_weights['vegas_pts'])
        
        return blended
    
    def apply_game_context(self, base_projection: float, context_factors: Dict) -> float:
        """
        Apply the 20% game context component.
        
        Args:
            base_projection: Combined props + models projection
            context_factors: Dict with game context adjustments
        
        Returns:
            Context-adjusted projection
        """
        adjustment_multiplier = 1.0
        
        # Game script adjustments
        if 'game_script' in context_factors:
            script = context_factors['game_script']
            if script == 'competitive':
                # Competitive games boost QB rushing, WR targets
                if context_factors.get('position') == 'QB':
                    adjustment_multiplier *= 1.15  # 15% boost for QB in competitive games
                elif context_factors.get('position') == 'WR':
                    adjustment_multiplier *= 1.08  # 8% boost for WR
            elif script == 'blowout_favorite':
                # Blowout favorites boost RB usage
                if context_factors.get('position') == 'RB':
                    adjustment_multiplier *= 1.12  # 12% boost for RB
        
        # Weather adjustments
        if 'weather_impact' in context_factors:
            weather = context_factors['weather_impact']
            if weather > 0.3:  # Significant weather
                # Reduce passing game, boost rushing
                if context_factors.get('position') in ['QB', 'WR', 'TE']:
                    adjustment_multiplier *= (1.0 - weather * 0.2)
                elif context_factors.get('position') == 'RB':
                    adjustment_multiplier *= (1.0 + weather * 0.15)
        
        # Pace adjustments
        if 'pace_factor' in context_factors:
            pace = context_factors['pace_factor']
            adjustment_multiplier *= pace  # Direct pace multiplier
        
        # Depth chart boost
        if 'depth_position' in context_factors:
            depth = context_factors['depth_position']
            depth_boosts = {
                'QB1': 1.0, 'RB1': 1.0, 'WR1': 1.0,  # No boost (baseline)
                'WR2': 0.85, 'TE1': 0.75, 'RB2': 0.6  # Reduce for lower depth
            }
            adjustment_multiplier *= depth_boosts.get(depth, 0.5)
        
        return base_projection * adjustment_multiplier
    
    def create_final_projection(self, player_data: Dict) -> Dict:
        """
        Create final blended projection using 50/30/20 framework.
        
        Args:
            player_data: Dict containing all player information
        
        Returns:
            Dict with final projection and component breakdown
        """
        # 50% PROPS COMPONENT
        props_signals = self.extract_props_signals(player_data.get('props', {}))
        props_projection = (props_signals['volume_projection'] + 
                          props_signals['efficiency_projection'] + 
                          props_signals['scoring_projection'])
        
        # 30% MODELS COMPONENT  
        models_projection = self.blend_models(
            player_data.get('fc_proj', 0),
            player_data.get('my_proj', 0), 
            player_data.get('vegas_pts', 0)
        )
        
        # Combine props and models (80% of final projection)
        base_projection = (props_projection * 0.5 + models_projection * 0.3) / 0.8
        
        # 20% CONTEXT COMPONENT
        final_projection = self.apply_game_context(
            base_projection, 
            player_data.get('context', {})
        )
        
        return {
            'final_projection': final_projection,
            'props_component': props_projection,
            'models_component': models_projection,
            'context_adjustment': final_projection - base_projection,
            'breakdown': {
                'props_weight': 0.5,
                'models_weight': 0.3, 
                'context_weight': 0.2
            }
        }


def example_usage():
    """Example of how to use the projection blender."""
    
    blender = ProjectionBlender()
    
    # Example player data (Josh Allen from Ravens-Bills)
    josh_allen_data = {
        'props': {
            'pass_yards_line': 231.5,
            'rush_yards_line': 31.5,
            'td_anytime_prob': 0.487,  # 48.7% from analysis
            'market_confidence': 0.8   # High confidence market
        },
        'fc_proj': 35.36,     # From your CSV
        'my_proj': 35.36,     # From your CSV  
        'vegas_pts': 24.75,   # From your CSV
        'context': {
            'position': 'QB',
            'game_script': 'competitive',
            'weather_impact': 0.2,  # Light weather impact
            'pace_factor': 1.05,    # Slightly faster pace
            'depth_position': 'QB1'
        }
    }
    
    # Create projection
    result = blender.create_final_projection(josh_allen_data)
    
    print("JOSH ALLEN PROJECTION BREAKDOWN:")
    print(f"Final Projection: {result['final_projection']:.1f}")
    print(f"Props Component (50%): {result['props_component']:.1f}")
    print(f"Models Component (30%): {result['models_component']:.1f}")
    print(f"Context Adjustment (20%): {result['context_adjustment']:.1f}")
    print(f"Actual Result: 41.8 points")
    
    # Example for Derrick Henry
    henry_data = {
        'props': {
            'rush_yards_line': 82.5,
            'rec_yards_line': 4.5,
            'td_anytime_prob': 0.576,  # 57.6% highest TD prob
            'market_confidence': 0.9
        },
        'fc_proj': 29.78,
        'my_proj': 29.78,
        'vegas_pts': 25.75,
        'context': {
            'position': 'RB',
            'game_script': 'competitive', 
            'weather_impact': 0.2,
            'pace_factor': 1.0,
            'depth_position': 'RB1'
        }
    }
    
    henry_result = blender.create_final_projection(henry_data)
    
    print(f"\nDERRICK HENRY PROJECTION BREAKDOWN:")
    print(f"Final Projection: {henry_result['final_projection']:.1f}")
    print(f"Props Component (50%): {henry_result['props_component']:.1f}")
    print(f"Models Component (30%): {henry_result['models_component']:.1f}")
    print(f"Context Adjustment (20%): {henry_result['context_adjustment']:.1f}")
    print(f"Actual Result: 33.2 points")


if __name__ == "__main__":
    example_usage()
