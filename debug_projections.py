#!/usr/bin/env python3
"""
Debug the exact calculations for <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON>
"""

from jaguars_bengals_projections import EliteProjectionSystem

def debug_player_calculation(system, player_name, position, team):
    """Debug a specific player's calculation step by step."""
    
    print(f"\n🔍 DEBUGGING {player_name} ({position}, {team})")
    print("=" * 60)
    
    # Get market data
    market_data = system.fetch_comprehensive_props()
    player_props = market_data['player_props']
    
    if player_name not in player_props:
        print(f"❌ No props found for {player_name}")
        return
    
    props = player_props[player_name]
    print(f"📊 Props: {props}")
    
    # Apply elite prop methodology
    prop_analysis = system.apply_elite_prop_methodology(player_name, props)
    print(f"🧠 Prop Analysis: {prop_analysis}")
    
    # Calculate base projection
    base_projection = system.calculate_prop_based_projection(
        player_name, position, props, prop_analysis
    )
    print(f"📈 Base Projection: {base_projection:.2f}")
    
    # Apply team context
    context_multiplier = system.apply_team_context_multipliers(
        player_name, position, team, system.team_edges
    )
    print(f"🏈 Context Multiplier: {context_multiplier:.3f}")
    
    # Final calculation
    final_projection = base_projection * context_multiplier
    print(f"🎯 FINAL: {base_projection:.2f} × {context_multiplier:.3f} = {final_projection:.2f}")
    
    # Let's also manually trace the prop-based calculation
    print(f"\n🔬 MANUAL CALCULATION TRACE:")
    
    # Get the factors from prop analysis
    market_factor = prop_analysis['market_strength_factor']
    sharp_factor = prop_analysis['sharp_money_factor']
    psych_factor = prop_analysis['psychological_factor']
    value_factor = prop_analysis['line_value_factor']
    combined_factor = market_factor * sharp_factor * psych_factor * value_factor
    
    print(f"Combined Factor: {market_factor:.3f} × {sharp_factor:.3f} × {psych_factor:.3f} × {value_factor:.3f} = {combined_factor:.3f}")
    
    projection = 0.0
    
    if position == 'QB':
        # Passing yards
        if 'player_pass_yds' in props:
            pass_yards = props['player_pass_yds']['line'] * combined_factor
            pass_points = pass_yards * system.dk_scoring['pass_yard']
            projection += pass_points
            print(f"Pass Yards: {props['player_pass_yds']['line']} × {combined_factor:.3f} = {pass_yards:.1f} → {pass_points:.2f} pts")
        
        # Passing TDs
        if 'player_pass_tds' in props:
            pass_tds = props['player_pass_tds']['line'] * combined_factor
            td_points = pass_tds * system.dk_scoring['pass_td']
            projection += td_points
            print(f"Pass TDs: {props['player_pass_tds']['line']} × {combined_factor:.3f} = {pass_tds:.2f} → {td_points:.2f} pts")
        
        # Interceptions
        int_points = 1.2 * system.dk_scoring['pass_int']
        projection += int_points
        print(f"Interceptions: 1.2 × {system.dk_scoring['pass_int']} = {int_points:.2f} pts")
        
        # Rushing yards
        if 'player_rush_yds' in props:
            rush_yards = props['player_rush_yds']['line'] * combined_factor
            rush_points = rush_yards * system.dk_scoring['rush_yard']
            projection += rush_points
            print(f"Rush Yards: {props['player_rush_yds']['line']} × {combined_factor:.3f} = {rush_yards:.1f} → {rush_points:.2f} pts")
        
        # Anytime TD
        if 'player_anytime_td' in props:
            td_prob = system.calculate_anytime_td_probability(props['player_anytime_td'])
            td_points = td_prob * system.dk_scoring['rush_td']
            projection += td_points
            print(f"Rush TD: {td_prob:.3f} × {system.dk_scoring['rush_td']} = {td_points:.2f} pts")
    
    elif position in ['WR', 'TE']:
        # Receiving yards
        if 'player_reception_yds' in props:
            rec_yards = props['player_reception_yds']['line'] * combined_factor
            rec_points = rec_yards * system.dk_scoring['rec_yard']
            projection += rec_points
            print(f"Rec Yards: {props['player_reception_yds']['line']} × {combined_factor:.3f} = {rec_yards:.1f} → {rec_points:.2f} pts")
        
        # Receptions
        if 'player_receptions' in props:
            receptions = props['player_receptions']['line'] * combined_factor
            rec_points = receptions * system.dk_scoring['reception']
            projection += rec_points
            print(f"Receptions: {props['player_receptions']['line']} × {combined_factor:.3f} = {receptions:.1f} → {rec_points:.2f} pts")
        
        # Receiving TDs
        if 'player_anytime_td' in props:
            td_prob = system.calculate_anytime_td_probability(props['player_anytime_td'])
            td_points = td_prob * system.dk_scoring['rec_td']
            projection += td_points
            print(f"Rec TD: {td_prob:.3f} × {system.dk_scoring['rec_td']} = {td_points:.2f} pts")
    
    print(f"Manual Total: {projection:.2f}")
    print(f"System Total: {base_projection:.2f}")
    print(f"Difference: {abs(projection - base_projection):.2f}")
    
    return final_projection

def main():
    """Debug the three key players."""
    
    system = EliteProjectionSystem()
    
    print("🏈 ELITE PROJECTION SYSTEM DEBUG")
    print("Showing exact calculations for key players")
    
    # Debug each player
    burrow_proj = debug_player_calculation(system, 'Joe Burrow', 'QB', 'CIN')
    tlaw_proj = debug_player_calculation(system, 'Trevor Lawrence', 'QB', 'JAX')
    chase_proj = debug_player_calculation(system, 'JaMarr Chase', 'WR', 'CIN')
    
    print(f"\n🎯 FINAL RESULTS:")
    print(f"Joe Burrow: {burrow_proj:.2f}")
    print(f"Trevor Lawrence: {tlaw_proj:.2f}")
    print(f"JaMarr Chase: {chase_proj:.2f}")

if __name__ == "__main__":
    main()
