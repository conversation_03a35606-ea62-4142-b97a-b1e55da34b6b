#!/usr/bin/env python3
"""
Final Browns vs Ravens Projections with Manual TD Props
Elite-level DraftKings projections for Cleveland Browns @ Baltimore Ravens
"""

import pandas as pd
import numpy as np

def american_to_prob(odds):
    """Convert American odds to implied probability."""
    if odds > 0:
        return 100 / (odds + 100)
    else:
        return abs(odds) / (abs(odds) + 100)

def create_final_projections():
    """Create final projections with TD props included."""
    
    # Load the base projections from our elite system (latest with defenses)
    base_projections = pd.read_csv('cowboys_giants_elite_projections_20250912_2216.csv')
    
    # Estimated anytime TD odds for Browns vs Ravens (typical odds)
    anytime_td_odds = {
        '<PERSON>': +180,      # Mobile QB, good TD odds
        '<PERSON>': -120,      # <PERSON>, heavy favorite
        '<PERSON>': +140,        # WR1 for Browns
        'Zay <PERSON>': +160,        # WR1 for Ravens
        '<PERSON>': +200,       # Elite TE
        '<PERSON>': +220,        # <PERSON> TE1
        '<PERSON><PERSON>': +280,     # <PERSON> WR2
        'Rashod <PERSON>': +300,     # <PERSON> WR2
        '<PERSON>': +400,       # <PERSON> RB2
        '<PERSON>.': +450,  # <PERSON> WR3
        'De<PERSON><PERSON><PERSON>': +350,    # <PERSON> veteran WR
        '<PERSON>': +600          # Backup QB, low TD odds
    }
    
    print("🏈 ADDING ANYTIME TD PROJECTIONS FOR BROWNS VS RAVENS...")
    print("=" * 55)
    
    # Create final projections
    final_projections = []
    
    for _, row in base_projections.iterrows():
        player = row['Player']
        base_projection = row['Projection']
        
        # Add TD projection if available
        td_points = 0.0
        if player in anytime_td_odds:
            odds = anytime_td_odds[player]
            implied_prob = american_to_prob(odds)
            
            # Convert to expected TDs using elite methodology
            expected_tds = implied_prob
            
            # Apply position-specific adjustments
            if 'jackson' in player.lower() or 'flacco' in player.lower():  # QBs
                # QBs: mostly rushing TDs for Lamar, passing TDs for others
                if 'jackson' in player.lower():
                    expected_tds *= 0.9  # Lamar gets full rushing TD value
                else:
                    expected_tds *= 0.7  # Backup QB more conservative
                td_points = expected_tds * 6.0
            elif 'henry' in player.lower() or 'hill' in player.lower():  # RBs
                # RBs: full TD value
                td_points = expected_tds * 6.0
            else:
                # WRs/TEs: full TD value
                td_points = expected_tds * 6.0
            
            print(f"{player:<20} {odds:>+4d} → {implied_prob:.1%} → {expected_tds:.3f} TDs → +{td_points:.2f} pts")
        
        final_projection = base_projection + td_points
        
        final_projections.append({
            'Player': player,
            'Projection': round(final_projection, 2)
        })
    
    # Convert to DataFrame and sort
    final_df = pd.DataFrame(final_projections)
    final_df = final_df.sort_values('Projection', ascending=False)
    
    return final_df

def main():
    """Generate final projections with TD props."""
    
    print("🚀 FINAL BROWNS VS RAVENS ELITE PROJECTIONS")
    print("=" * 48)
    print("Combining elite prop analysis + estimated TD odds")
    print()
    
    # Generate final projections
    projections = create_final_projections()
    
    print(f"\n🏆 FINAL TOP 15 PROJECTIONS:")
    print("-" * 45)
    for i, (_, player) in enumerate(projections.head(15).iterrows(), 1):
        print(f"{i:2d}. {player['Player']:<25} {player['Projection']:>6.2f} pts")
    
    # Save final projections
    filename = 'browns_ravens_FINAL_elite_projections.csv'
    projections.to_csv(filename, index=False)
    
    print(f"\n💾 Saved final projections to: {filename}")
    print(f"📊 Total players: {len(projections)}")
    
    return projections

if __name__ == "__main__":
    main()
