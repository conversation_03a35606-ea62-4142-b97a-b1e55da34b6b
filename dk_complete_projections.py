#!/usr/bin/env python3
"""
Complete DraftKings Projections for WAS @ GB
Includes all skill positions + team defenses using proper DK scoring.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List
from dataclasses import dataclass


@dataclass
class DKProjection:
    """DraftKings projection with proper scoring."""
    player_name: str
    team: str
    position: str
    salary: int
    dk_projection: float
    confidence: float
    value_score: float
    prop_count: int
    breakdown: Dict[str, float]


class DKCompleteProjections:
    """Complete DraftKings projections using proper scoring system."""
    
    def __init__(self):
        # DraftKings Scoring System (OFFICIAL)
        self.dk_scoring = {
            # Passing
            'pass_yards': 0.04,  # 1 pt per 25 yards
            'pass_tds': 4.0,
            'pass_interceptions': -1.0,
            'pass_300_bonus': 3.0,  # 300+ yard bonus
            'fumble_lost': -1.0,

            # Rushing
            'rush_yards': 0.1,   # 1 pt per 10 yards
            'rush_tds': 6.0,
            'rush_100_bonus': 3.0,  # 100+ yard bonus

            # Receiving
            'reception_yards': 0.1,  # 1 pt per 10 yards
            'receptions': 1.0,       # 1 pt per reception
            'reception_tds': 6.0,
            'rec_100_bonus': 3.0,    # 100+ yard bonus

            # Other
            'two_pt_conversion': 2.0,
            'return_td': 6.0,
            'offensive_fumble_recovery_td': 6.0,

            # Kicking (points scored by kicker)
            'kicking_points': 1.0,

            # Defense (team) - CONFIRMED CORRECT
            'sack': 1.0,
            'interception': 2.0,
            'fumble_recovery': 2.0,
            'defensive_td': 6.0,  # Any defensive TD
            'safety': 2.0,
            'blocked_kick': 2.0,
            'two_pt_return': 2.0,

            # Points allowed (CONFIRMED CORRECT)
            'points_0': 10.0,
            'points_1_6': 7.0,
            'points_7_13': 4.0,
            'points_14_20': 1.0,
            'points_21_27': 0.0,
            'points_28_34': -1.0,
            'points_35_plus': -4.0
        }
    
    def load_all_data(self) -> Dict[str, pd.DataFrame]:
        """Load all projection data."""
        data = {}
        
        try:
            # DraftKings player pool
            data['dk'] = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-2_players.csv', skiprows=1)
            
            # Our elite projections (skill positions)
            data['elite'] = pd.read_csv('csvs/was_gb_FINAL_elite_projections_20250911_2202.csv')
            
            # Defense projections
            data['defense'] = pd.read_csv('csvs/was_gb_defense_projections_20250911_2205.csv')
            
            # Detailed props for breakdown
            data['props_detail'] = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')
            
            print(f"✅ Loaded all data sources")
            return data
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return {}
    
    def convert_to_dk_scoring(self, player_name: str, props_detail: pd.DataFrame) -> Dict[str, float]:
        """Convert prop projections to DraftKings scoring."""
        player_props = props_detail[
            props_detail['player_name'].str.contains(player_name, case=False, na=False)
        ]
        
        dk_breakdown = {}
        total_dk_points = 0.0
        
        for _, prop in player_props.iterrows():
            market = prop['market']
            projection = prop['final_projection']
            line = prop['line_consensus']
            
            # Convert each market to DK points
            if market == 'player_pass_yds':
                # Convert implied yards to DK points
                implied_yards = line * (1 + (projection / max(line * 0.04, 1)))
                dk_points = implied_yards * self.dk_scoring['pass_yards']

                # Add 300+ yard bonus probability
                if implied_yards > 250:  # Reasonable chance of 300+
                    bonus_prob = max(0, min(0.4, (implied_yards - 250) / 100))
                    dk_points += bonus_prob * self.dk_scoring['pass_300_bonus']
                    dk_breakdown['pass_300_bonus'] = round(bonus_prob * 3, 2)

                dk_breakdown['pass_yards'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_pass_tds':
                implied_tds = projection / 4.0  # Convert fantasy points back to TDs
                dk_points = implied_tds * self.dk_scoring['pass_tds']
                dk_breakdown['pass_tds'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_rush_yds':
                implied_yards = line * (1 + (projection / max(line * 0.1, 1)))
                dk_points = implied_yards * self.dk_scoring['rush_yards']

                # Add 100+ yard bonus probability
                if implied_yards > 80:  # Reasonable chance of 100+
                    bonus_prob = max(0, min(0.3, (implied_yards - 80) / 40))
                    dk_points += bonus_prob * self.dk_scoring['rush_100_bonus']
                    dk_breakdown['rush_100_bonus'] = round(bonus_prob * 3, 2)

                dk_breakdown['rush_yards'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_rush_tds':
                implied_tds = projection / 6.0
                dk_points = implied_tds * self.dk_scoring['rush_tds']
                dk_breakdown['rush_tds'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_reception_yds':
                implied_yards = line * (1 + (projection / max(line * 0.1, 1)))
                dk_points = implied_yards * self.dk_scoring['reception_yards']

                # Add 100+ yard bonus probability
                if implied_yards > 80:  # Reasonable chance of 100+
                    bonus_prob = max(0, min(0.25, (implied_yards - 80) / 50))
                    dk_points += bonus_prob * self.dk_scoring['rec_100_bonus']
                    dk_breakdown['rec_100_bonus'] = round(bonus_prob * 3, 2)

                dk_breakdown['rec_yards'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_receptions':
                dk_points = projection * self.dk_scoring['receptions']
                dk_breakdown['receptions'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_reception_tds':
                implied_tds = projection / 6.0
                dk_points = implied_tds * self.dk_scoring['reception_tds']
                dk_breakdown['rec_tds'] = round(dk_points, 2)
                total_dk_points += dk_points
                
            elif market == 'player_kicking_points':
                dk_points = projection * self.dk_scoring['kicking_points']
                dk_breakdown['kicking'] = round(dk_points, 2)
                total_dk_points += dk_points
        
        dk_breakdown['total'] = round(total_dk_points, 2)
        return dk_breakdown
    
    def create_complete_projections(self) -> List[DKProjection]:
        """Create complete DraftKings projections."""
        print("=== COMPLETE DRAFTKINGS PROJECTIONS ===")
        
        data = self.load_all_data()
        if not data:
            return []
        
        projections = []
        
        # Process skill position players
        for _, elite_proj in data['elite'].iterrows():
            player_name = elite_proj['player_name']
            team = elite_proj['team']
            position = elite_proj['position']
            salary = elite_proj['salary']
            prop_count = elite_proj['prop_count']
            confidence = elite_proj['confidence']
            
            # Convert to proper DK scoring
            dk_breakdown = self.convert_to_dk_scoring(player_name, data['props_detail'])
            dk_projection = dk_breakdown.get('total', elite_proj['final_projection'])
            
            # Calculate value
            value_score = (dk_projection / salary * 1000) if salary > 0 else 0
            
            projections.append(DKProjection(
                player_name=player_name,
                team=team,
                position=position,
                salary=salary,
                dk_projection=dk_projection,
                confidence=confidence,
                value_score=round(value_score, 2),
                prop_count=prop_count,
                breakdown=dk_breakdown
            ))
        
        # Add defense projections
        for _, def_proj in data['defense'].iterrows():
            team_name = def_proj['team']
            dk_projection = def_proj['fantasy_projection']
            confidence = def_proj['confidence']
            
            # Find salary from DK data
            dk_defense = data['dk'][data['dk']['Player'] == team_name]
            salary = dk_defense['Salary'].iloc[0] if not dk_defense.empty else 4600
            
            value_score = (dk_projection / salary * 1000) if salary > 0 else 0
            
            # Defense breakdown
            breakdown = {
                'sacks': def_proj['sacks'],
                'interceptions': def_proj['interceptions'],
                'fumble_recoveries': def_proj['fumble_recoveries'],
                'defensive_tds': def_proj['defensive_tds'],
                'total': dk_projection
            }
            
            projections.append(DKProjection(
                player_name=team_name,
                team=team_name.split()[0][:3].upper(),
                position='DST',
                salary=salary,
                dk_projection=dk_projection,
                confidence=confidence,
                value_score=round(value_score, 2),
                prop_count=5,  # Multiple defensive stats
                breakdown=breakdown
            ))
        
        # Sort by projection
        projections.sort(key=lambda x: x.dk_projection, reverse=True)
        
        print(f"✅ Created {len(projections)} complete DK projections")
        return projections
    
    def save_complete_projections(self, projections: List[DKProjection]) -> str:
        """Save complete projections."""
        # Main projections file
        proj_data = []
        for proj in projections:
            proj_data.append({
                'player_name': proj.player_name,
                'team': proj.team,
                'position': proj.position,
                'salary': proj.salary,
                'dk_projection': proj.dk_projection,
                'confidence': proj.confidence,
                'value_score': proj.value_score,
                'prop_count': proj.prop_count
            })
        
        proj_df = pd.DataFrame(proj_data)
        filename = f"csvs/was_gb_COMPLETE_DK_projections_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        proj_df.to_csv(filename, index=False)
        
        # Breakdown file
        breakdown_data = []
        for proj in projections:
            row = {
                'player_name': proj.player_name,
                'position': proj.position,
                'dk_projection': proj.dk_projection
            }
            # Add breakdown components
            for key, value in proj.breakdown.items():
                if key != 'total':
                    row[f'{key}_component'] = value
            
            breakdown_data.append(row)
        
        breakdown_df = pd.DataFrame(breakdown_data)
        breakdown_filename = f"csvs/was_gb_DK_breakdown_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        breakdown_df.to_csv(breakdown_filename, index=False)
        
        print(f"💾 Complete DK projections saved to: {filename}")
        print(f"💾 Breakdown saved to: {breakdown_filename}")
        
        return filename, breakdown_filename


def main():
    """Main execution."""
    projector = DKCompleteProjections()
    
    projections = projector.create_complete_projections()
    
    if not projections:
        print("❌ Could not create projections")
        return
    
    # Save projections
    proj_file, breakdown_file = projector.save_complete_projections(projections)
    
    # Display results
    print(f"\n🏆 TOP 20 COMPLETE DK PROJECTIONS:")
    print(f"{'Rank':<4} {'Player':<18} {'Pos':<3} {'DK Proj':<8} {'Value':<6} {'Conf':<5}")
    print("-" * 65)
    
    for i, proj in enumerate(projections[:20]):
        print(f"{i+1:<4} {proj.player_name:<18} {proj.position:<3} "
              f"{proj.dk_projection:<8.1f} {proj.value_score:<6.2f} {proj.confidence:<5.3f}")
    
    # Position breakdown
    positions = {}
    for proj in projections:
        pos = proj.position
        if pos not in positions:
            positions[pos] = []
        positions[pos].append(proj)
    
    print(f"\n📊 BY POSITION:")
    for pos in ['QB', 'RB', 'WR', 'TE', 'K', 'DST']:
        if pos in positions:
            top_player = max(positions[pos], key=lambda x: x.dk_projection)
            print(f"   {pos}: {top_player.player_name} ({top_player.dk_projection:.1f} pts)")
    
    return projections


if __name__ == "__main__":
    main()
