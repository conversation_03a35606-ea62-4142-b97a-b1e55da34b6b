#!/usr/bin/env python3
"""
Corrected Defense Analysis - Manual Calculation
"""

def calculate_defense_manually():
    """Manual defense calculation to verify projections."""
    
    print("=== CORRECTED DEFENSE ANALYSIS ===")
    print("🔧 Fixing the calculation errors...")
    
    # DraftKings Defense Scoring
    scoring = {
        'sack': 1.0,
        'interception': 2.0,
        'fumble_recovery': 2.0,
        'defensive_td': 6.0,
        'safety': 2.0,
        'blocked_kick': 2.0,
        'points_0': 10.0,
        'points_1_6': 7.0,
        'points_7_13': 4.0,
        'points_14_20': 1.0,
        'points_21_27': 0.0,
        'points_28_34': -1.0,
        'points_35_plus': -4.0
    }
    
    # Packers DST vs WAS (22.75 implied total)
    print("\n🟢 PACKERS DST vs WAS:")
    print("   Opponent Implied Total: 22.75 points")
    
    # Individual stats from props
    gb_sacks = 5.25  # From individual player props
    gb_ints = 0.8    # Conservative estimate
    gb_fumbles = 0.6 # League average
    gb_def_tds = 0.1 # Very rare
    
    # Points calculation
    individual_points = (
        gb_sacks * scoring['sack'] +
        gb_ints * scoring['interception'] +
        gb_fumbles * scoring['fumble_recovery'] +
        gb_def_tds * scoring['defensive_td']
    )
    
    print(f"   Individual Stats:")
    print(f"     Sacks: {gb_sacks} × 1 = {gb_sacks:.1f} pts")
    print(f"     INTs: {gb_ints} × 2 = {gb_ints * 2:.1f} pts")
    print(f"     Fumbles: {gb_fumbles} × 2 = {gb_fumbles * 2:.1f} pts")
    print(f"     Def TDs: {gb_def_tds} × 6 = {gb_def_tds * 6:.1f} pts")
    print(f"   Individual Total: {individual_points:.1f} pts")
    
    # Points allowed (22.75 implied)
    # Most likely outcomes:
    # 21-27 points (50% chance) = 0 pts
    # 14-20 points (30% chance) = +1 pt  
    # 28+ points (20% chance) = -1 pt
    points_allowed_expected = (0.5 * 0) + (0.3 * 1) + (0.2 * -1)
    points_allowed_expected = 0.1  # Very slight positive
    
    print(f"   Points Allowed (22.75 implied):")
    print(f"     21-27 pts (50%): 0 × 0.5 = 0.0")
    print(f"     14-20 pts (30%): 1 × 0.3 = 0.3") 
    print(f"     28+ pts (20%): -1 × 0.2 = -0.2")
    print(f"   Points Allowed Expected: {points_allowed_expected:.1f} pts")
    
    gb_total = individual_points + points_allowed_expected
    print(f"   🟢 PACKERS DST TOTAL: {gb_total:.1f} pts")
    
    # Commanders DST vs GB (26.25 implied total)
    print("\n🔴 COMMANDERS DST vs GB:")
    print("   Opponent Implied Total: 26.25 points")
    
    was_sacks = 4.25
    was_ints = 0.8
    was_fumbles = 0.6
    was_def_tds = 0.1
    
    individual_points_was = (
        was_sacks * scoring['sack'] +
        was_ints * scoring['interception'] +
        was_fumbles * scoring['fumble_recovery'] +
        was_def_tds * scoring['defensive_td']
    )
    
    print(f"   Individual Stats:")
    print(f"     Sacks: {was_sacks} × 1 = {was_sacks:.1f} pts")
    print(f"     INTs: {was_ints} × 2 = {was_ints * 2:.1f} pts")
    print(f"     Fumbles: {was_fumbles} × 2 = {was_fumbles * 2:.1f} pts")
    print(f"     Def TDs: {was_def_tds} × 6 = {was_def_tds * 6:.1f} pts")
    print(f"   Individual Total: {individual_points_was:.1f} pts")
    
    # Points allowed (26.25 implied) - worse for defense
    # 21-27 points (60% chance) = 0 pts
    # 28-34 points (30% chance) = -1 pt
    # 14-20 points (10% chance) = +1 pt
    points_allowed_was = (0.6 * 0) + (0.3 * -1) + (0.1 * 1)
    points_allowed_was = -0.2
    
    print(f"   Points Allowed (26.25 implied):")
    print(f"     21-27 pts (60%): 0 × 0.6 = 0.0")
    print(f"     28-34 pts (30%): -1 × 0.3 = -0.3")
    print(f"     14-20 pts (10%): 1 × 0.1 = 0.1")
    print(f"   Points Allowed Expected: {points_allowed_was:.1f} pts")
    
    was_total = individual_points_was + points_allowed_was
    print(f"   🔴 COMMANDERS DST TOTAL: {was_total:.1f} pts")
    
    print(f"\n📊 CORRECTED DEFENSE PROJECTIONS:")
    print(f"   1. Packers DST: {gb_total:.1f} pts (vs 22.75 implied)")
    print(f"   2. Commanders DST: {was_total:.1f} pts (vs 26.25 implied)")
    
    print(f"\n🔍 ANALYSIS:")
    print(f"   • Packers DST benefits from lower opponent total")
    print(f"   • Both defenses project similarly due to points allowed impact")
    print(f"   • Sacks are the main differentiator (GB has more)")
    print(f"   • Neither defense is a standout play")
    
    return {
        'packers': gb_total,
        'commanders': was_total
    }

if __name__ == "__main__":
    calculate_defense_manually()
