#!/usr/bin/env python3
"""
UNM @ UCLA — Team-level 100,000-run simulation with cover/total probabilities.
- Anchors to market implied totals from data/unm_ucla_game_context.csv
- Optionally pulls CFBD team metrics (PPA) when CFBD_API_KEY is provided to shape variance/edge
- Outputs team distribution and betting probabilities

Saves CSV: data/unm_ucla_team_sim_100k.csv
"""

import os
import math
import json
import numpy as np
import pandas as pd
import requests
from pathlib import Path
from typing import Dict, Any, Tuple

CTX_PATH = Path('data/unm_ucla_game_context.csv')
OUT_PATH = Path('data/unm_ucla_team_sim_100k.csv')
CFBD_BASE = 'https://api.collegefootballdata.com'
N_RUNS = 100_000


def load_context() -> Dict[str, Any]:
    df = pd.read_csv(CTX_PATH)
    row = df.iloc[0].to_dict()
    return {
        'home_team': row['home_team'],
        'away_team': row['away_team'],
        'spread': float(row['spread']),  # home spread (negative favored)
        'total': float(row['total']),
        'home_it': float(row['home_implied_total']),
        'away_it': float(row['away_implied_total']),
    }


def get_cfbd_key() -> str:
    return os.getenv('CFBD_API_KEY', '').strip()


def fetch_cfbd_ppa(year: int = 2025) -> Dict[str, Dict[str, float]]:
    """Fetch team PPA offense/defense from CFBD. Returns team -> {'off_ppa': x, 'def_ppa': y}.
    Tries the given year, then falls back to previous season if response is empty or non-JSON.
    """
    key = get_cfbd_key()
    if not key:
        return {}
    years = [year, year - 1]
    headers = {'Authorization': f'Bearer {key}'}
    for yr in years:
        try:
            url = f"{CFBD_BASE}/metrics/ppa/teams?year={yr}"
            r = requests.get(url, headers=headers, timeout=30)
            if r.status_code != 200:
                continue
            try:
                data = r.json()
            except Exception:
                continue
            if not isinstance(data, list) or not data:
                continue
            out: Dict[str, Dict[str, float]] = {}
            for t in data:
                team = t.get('team')
                off = t.get('offense', {})
                de = t.get('defense', {})
                if team:
                    out[team] = {
                        'off_ppa': float(off.get('overall', {}).get('ppa', 0.0) or 0.0),
                        'def_ppa': float(de.get('overall', {}).get('ppa', 0.0) or 0.0)
                    }
            if out:
                return out
        except Exception:
            continue
    return {}


def name_alias(name: str) -> str:
    # Simple aliasing for matching CFBD team names
    lookup = {
        'UCLA Bruins': 'UCLA',
        'New Mexico Lobos': 'New Mexico',
    }
    return lookup.get(name, name)


def compute_edge_factors(cfbd: Dict[str, Dict[str, float]], home: str, away: str) -> Tuple[float, float]:
    """Return (home_edge, away_edge) from PPA offense vs defense differentials.
    Scale to modest percentages affecting dispersion and slight mean skew.
    """
    h = cfbd.get(name_alias(home), {})
    a = cfbd.get(name_alias(away), {})
    if not h or not a:
        return 0.0, 0.0
    # Offense vs opponent defense
    h_adv = (h.get('off_ppa', 0.0) - a.get('def_ppa', 0.0))
    a_adv = (a.get('off_ppa', 0.0) - h.get('def_ppa', 0.0))
    # Scale — typical PPA per play order ~ 0.0–0.3. Map to small edges.
    scale = 0.25
    return float(h_adv * scale), float(a_adv * scale)


def negbin_params(mean: float, var: float) -> Tuple[float, float]:
    """Return (r, p) for Negative Binomial with given mean and variance.
    mean = r*(1-p)/p ; var = r*(1-p)/p^2 => r = mean^2/(var-mean), p = r/(r+mean)
    """
    var = max(var, mean + 1e-6)
    r = (mean * mean) / (var - mean)
    p = r / (r + mean)
    return r, p


def simulate_team_points(home_it: float, away_it: float, spread: float, cfbd_edges: Tuple[float, float]) -> pd.DataFrame:
    """Run N_RUNS draws of team points using Negative Binomial per team, anchored to implied totals.
    CFBD edges slightly skew means (<= ±10%) and scale variance.
    """
    home_edge, away_edge = cfbd_edges

    # Adjust means slightly by edges but re-center to preserve total (market anchor)
    h_mu_raw = home_it * (1.0 + max(min(home_edge, 0.10), -0.10))
    a_mu_raw = away_it * (1.0 + max(min(away_edge, 0.10), -0.10))
    # Recenter to keep sum equal to market total
    total_market = home_it + away_it
    scale_back = total_market / max(h_mu_raw + a_mu_raw, 1e-6)
    h_mu = h_mu_raw * scale_back
    a_mu = a_mu_raw * scale_back

    # Variance model: overdispersed vs mean; stronger teams a bit more stable
    base_k = 1.6  # overdispersion multiplier
    h_var = h_mu * (1.0 + base_k * (1.0 - min(0.8, abs(home_edge))))
    a_var = a_mu * (1.0 + base_k * (1.0 - min(0.8, abs(away_edge))))

    # NegBin params and sampling via Gamma-Poisson approximation
    def sample_nb(mean, var, size):
        if mean <= 0:
            return np.zeros(size, dtype=int)
        var = max(var, mean + 1e-6)
        r, p = negbin_params(mean, var)
        # Guard against degenerate params
        r = max(r, 1e-6)
        p = min(max(p, 1e-9), 1 - 1e-9)
        lam = np.random.gamma(shape=r, scale=(1 - p) / p, size=size)
        return np.random.poisson(lam)

    # Sample touchdowns and field goals breakdown consistent with football scoring
    # Approximate expected TDs ~ mean/7, FGs ~ residual/3; then sample counts
    h_td_mu = max(0.0, h_mu / 7.0)
    a_td_mu = max(0.0, a_mu / 7.0)
    h_fg_mu = max(0.0, (h_mu - h_td_mu * 7.0) / 3.0)
    a_fg_mu = max(0.0, (a_mu - a_td_mu * 7.0) / 3.0)

    # Add modest overdispersion to counts
    td_k = 1.2
    fg_k = 1.0
    h_td = sample_nb(h_td_mu, h_td_mu * (1 + td_k), N_RUNS)
    a_td = sample_nb(a_td_mu, a_td_mu * (1 + td_k), N_RUNS)
    h_fg = sample_nb(h_fg_mu, h_fg_mu * (1 + fg_k), N_RUNS)
    a_fg = sample_nb(a_fg_mu, a_fg_mu * (1 + fg_k), N_RUNS)

    # Convert to points with XP success ~ 0.98 and occasional two-pointers (~3%)
    xp = 0.98
    two_pt = 0.03
    def td_points(n):
        # Mix of XP (1 pt) and rare 2-pt attempts
        return n * (6 + xp * (1 - two_pt) + 2 * two_pt)

    home_pts = td_points(h_td) + 3 * h_fg
    away_pts = td_points(a_td) + 3 * a_fg

    # Clip plausible bounds
    home_pts = np.clip(home_pts, 0, 100)
    away_pts = np.clip(away_pts, 0, 100)

    diff = home_pts - away_pts

    df = pd.DataFrame({
        'home_pts': home_pts,
        'away_pts': away_pts,
        'diff': diff,
        'cover_home': (diff + spread) > 0,  # spread is negative when home favored
        'over_total': (home_pts + away_pts) > total_market,
        'home_win': diff > 0,
    })
    return df


def summarize_team_sim(df: pd.DataFrame, ctx: Dict[str, Any]) -> pd.DataFrame:
    rows = []
    rows.append({
        'metric': 'home_mean', 'value': float(np.mean(df['home_pts']))
    })
    rows.append({
        'metric': 'away_mean', 'value': float(np.mean(df['away_pts']))
    })
    for side in ['home_pts', 'away_pts']:
        rows.append({'metric': f'{side}_median', 'value': float(np.median(df[side]))})
        rows.append({'metric': f'{side}_p05', 'value': float(np.percentile(df[side], 5))})
        rows.append({'metric': f'{side}_p25', 'value': float(np.percentile(df[side], 25))})
        rows.append({'metric': f'{side}_p75', 'value': float(np.percentile(df[side], 75))})
        rows.append({'metric': f'{side}_p95', 'value': float(np.percentile(df[side], 95))})

    rows.append({'metric': 'home_cover_prob', 'value': float(np.mean(df['cover_home']))})
    rows.append({'metric': 'home_win_prob', 'value': float(np.mean(df['home_win']))})
    rows.append({'metric': 'over_total_prob', 'value': float(np.mean(df['over_total']))})
    rows.append({'metric': 'spread', 'value': ctx['spread']})
    rows.append({'metric': 'total', 'value': ctx['total']})

    return pd.DataFrame(rows)


def main():
    ctx = load_context()
    cfbd = fetch_cfbd_ppa(2025)
    edges = compute_edge_factors(cfbd, ctx['home_team'], ctx['away_team']) if cfbd else (0.0, 0.0)

    df = simulate_team_points(ctx['home_it'], ctx['away_it'], ctx['spread'], edges)

    OUT_PATH.parent.mkdir(parents=True, exist_ok=True)
    df_summary = summarize_team_sim(df, ctx)
    df_summary.to_csv(OUT_PATH, index=False)

    print(f"Saved team simulation summary to {OUT_PATH}")
    print(df_summary.to_string(index=False))


if __name__ == '__main__':
    main()

