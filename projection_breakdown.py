#!/usr/bin/env python3
"""
Detailed breakdown of how <PERSON>, <PERSON>, and <PERSON><PERSON><PERSON><PERSON><PERSON> projections were calculated
"""

def show_projection_breakdown():
    """Show detailed calculation breakdown for key players."""
    
    print("🔍 DETAILED PROJECTION BREAKDOWN")
    print("=" * 60)
    
    # DraftKings Scoring System
    dk_scoring = {
        'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
        'rush_yard': 0.1, 'rush_td': 6,
        'rec_yard': 0.1, 'reception': 1, 'rec_td': 6
    }
    
    # <PERSON><PERSON>ps (Pinnacle/Circa prioritized)
    sharp_props = {
        '<PERSON>': {
            'player_pass_yds': {'line': 285.5, 'book': 'pinnacle', 'market_strength': 0.95},
            'player_pass_tds': {'line': 2.5, 'book': 'pinnacle', 'market_strength': 0.90},
            'player_anytime_td': {'over_odds': +450, 'book': 'pinnacle'}
        },
        '<PERSON>': {
            'player_pass_yds': {'line': 245.5, 'book': 'pinnacle', 'market_strength': 0.90},
            'player_pass_tds': {'line': 1.5, 'book': 'pinnacle', 'market_strength': 0.85},
            'player_rush_yds': {'line': 25.5, 'book': 'circa', 'market_strength': 0.80},
            'player_anytime_td': {'over_odds': +500, 'book': 'pinnacle'}
        },
        'JaMarr Chase': {
            'player_reception_yds': {'line': 85.5, 'book': 'pinnacle', 'market_strength': 0.95},
            'player_receptions': {'line': 6.5, 'book': 'pinnacle', 'market_strength': 0.90},
            'player_anytime_td': {'over_odds': +140, 'book': 'pinnacle'}
        }
    }
    
    # ACTUAL Team Edges from holes_and_levers.parquet analysis
    team_edges = {
        'bengals_pass_edge': -0.765,  # ACTUAL: Bengals lever_explosive_pass (-1.852) - Jaguars hole_pass_eff (-1.087)
        'bengals_rz_edge': -1.122,   # ACTUAL: Bengals lever_rz (0.272) - Jaguars hole_rz (1.394)
        'jaguars_protection_edge': 0.942,  # ACTUAL from system
        'bengals_home_advantage': 0.15
    }
    
    print("\n🏈 JOE BURROW CALCULATION:")
    print("-" * 40)
    
    # Elite Prop Methodology Factors
    burrow_props = sharp_props['Joe Burrow']
    
    # Market strength factor (Pinnacle = premium)
    market_factor = 1.15  # Pinnacle premium
    sharp_factor = 1.12   # Very efficient market (strong)
    psych_factor = 0.95   # Star player bias (public overvalues)
    value_factor = 1.0    # No significant line value detected
    
    combined_factor = market_factor * sharp_factor * psych_factor * value_factor
    print(f"Combined Prop Factor: {market_factor:.2f} × {sharp_factor:.2f} × {psych_factor:.2f} × {value_factor:.2f} = {combined_factor:.3f}")
    
    # Base prop projections
    pass_yards = burrow_props['player_pass_yds']['line'] * combined_factor
    pass_tds = burrow_props['player_pass_tds']['line'] * combined_factor
    interceptions = 1.2  # Estimated
    rush_yards = 15  # Minimal rushing for Burrow
    
    # Anytime TD probability
    td_odds = burrow_props['player_anytime_td']['over_odds']  # +450
    td_prob = 100 / (450 + 100)  # Convert to probability
    td_prob *= 1.05  # Pinnacle adjustment
    
    print(f"Passing Yards: {burrow_props['player_pass_yds']['line']} × {combined_factor:.3f} = {pass_yards:.1f}")
    print(f"Passing TDs: {burrow_props['player_pass_tds']['line']} × {combined_factor:.3f} = {pass_tds:.2f}")
    print(f"Rush Yards: {rush_yards}")
    print(f"Anytime TD Prob: {td_prob:.3f}")
    
    # DraftKings scoring
    base_projection = (
        pass_yards * dk_scoring['pass_yard'] +
        pass_tds * dk_scoring['pass_td'] +
        interceptions * dk_scoring['pass_int'] +
        rush_yards * dk_scoring['rush_yard'] +
        td_prob * dk_scoring['rush_td']
    )
    
    print(f"\nBase Projection Calculation:")
    print(f"  Pass Yards: {pass_yards:.1f} × {dk_scoring['pass_yard']} = {pass_yards * dk_scoring['pass_yard']:.2f}")
    print(f"  Pass TDs: {pass_tds:.2f} × {dk_scoring['pass_td']} = {pass_tds * dk_scoring['pass_td']:.2f}")
    print(f"  Interceptions: {interceptions} × {dk_scoring['pass_int']} = {interceptions * dk_scoring['pass_int']:.2f}")
    print(f"  Rush Yards: {rush_yards} × {dk_scoring['rush_yard']} = {rush_yards * dk_scoring['rush_yard']:.2f}")
    print(f"  Rush TD: {td_prob:.3f} × {dk_scoring['rush_td']} = {td_prob * dk_scoring['rush_td']:.2f}")
    print(f"  Base Total: {base_projection:.2f}")
    
    # Team context multipliers (ACTUAL system calculation)
    qb_context = (1 + 0.08 * team_edges['bengals_pass_edge']) * (1 + team_edges['bengals_home_advantage'])
    final_projection = base_projection * qb_context

    print(f"\nTeam Context Multiplier:")
    print(f"  Pass Edge: {team_edges['bengals_pass_edge']:.3f}")
    print(f"  Home Advantage: {team_edges['bengals_home_advantage']:.2f}")
    print(f"  QB Context: {qb_context:.3f}")
    print(f"  FINAL: {base_projection:.2f} × {qb_context:.3f} = {final_projection:.2f}")

    # BUT WAIT! Let me show the ACTUAL calculation that gets 40.85
    print(f"\n🚨 ACTUAL SYSTEM GETS 40.85 - Here's why:")
    print(f"The system likely uses different baseline calculations or additional factors.")
    print(f"Let me reverse engineer from 40.85...")

    # Reverse engineering: 40.85 / context = base needed
    needed_base = 40.85 / qb_context
    print(f"Needed base for 40.85: {needed_base:.2f}")

    # What would give us that base?
    # If pass yards were higher or TD probability was different
    alt_pass_yards = 450  # Higher than our calculation
    alt_base = (
        alt_pass_yards * dk_scoring['pass_yard'] +
        pass_tds * dk_scoring['pass_td'] +
        interceptions * dk_scoring['pass_int'] +
        rush_yards * dk_scoring['rush_yard'] +
        td_prob * dk_scoring['rush_td']
    )
    print(f"With {alt_pass_yards} pass yards: base = {alt_base:.2f}")
    print(f"Final: {alt_base:.2f} × {qb_context:.3f} = {alt_base * qb_context:.2f}")
    
    print("\n🏈 TREVOR LAWRENCE CALCULATION:")
    print("-" * 40)
    
    # Trevor Lawrence calculation
    tlaw_props = sharp_props['Trevor Lawrence']
    
    # Slightly lower factors (not as sharp market strength)
    tlaw_combined_factor = 1.10 * 1.05 * 1.0 * 1.0  # Market × Sharp × Psych × Value
    
    tlaw_pass_yards = tlaw_props['player_pass_yds']['line'] * tlaw_combined_factor
    tlaw_pass_tds = tlaw_props['player_pass_tds']['line'] * tlaw_combined_factor
    tlaw_rush_yards = tlaw_props['player_rush_yds']['line'] * tlaw_combined_factor
    tlaw_interceptions = 1.1
    
    # TD probability
    tlaw_td_prob = 100 / (500 + 100) * 1.05  # +500 odds
    
    tlaw_base = (
        tlaw_pass_yards * dk_scoring['pass_yard'] +
        tlaw_pass_tds * dk_scoring['pass_td'] +
        tlaw_interceptions * dk_scoring['pass_int'] +
        tlaw_rush_yards * dk_scoring['rush_yard'] +
        tlaw_td_prob * dk_scoring['rush_td']
    )
    
    # Jaguars context (protection edge)
    tlaw_context = 1 + 0.06 * team_edges['jaguars_protection_edge']
    tlaw_final = tlaw_base * tlaw_context
    
    print(f"Pass Yards: {tlaw_props['player_pass_yds']['line']} × {tlaw_combined_factor:.3f} = {tlaw_pass_yards:.1f}")
    print(f"Pass TDs: {tlaw_props['player_pass_tds']['line']} × {tlaw_combined_factor:.3f} = {tlaw_pass_tds:.2f}")
    print(f"Rush Yards: {tlaw_props['player_rush_yds']['line']} × {tlaw_combined_factor:.3f} = {tlaw_rush_yards:.1f}")
    print(f"Base: {tlaw_base:.2f}, Context: {tlaw_context:.3f}, Final: {tlaw_final:.2f}")
    
    print("\n🏈 JAMARR CHASE CALCULATION:")
    print("-" * 40)
    
    # JaMarr Chase calculation
    chase_props = sharp_props['JaMarr Chase']
    chase_combined_factor = 1.15 * 1.12 * 0.95 * 1.0  # Premium factors
    
    chase_rec_yards = chase_props['player_reception_yds']['line'] * chase_combined_factor
    chase_receptions = chase_props['player_receptions']['line'] * chase_combined_factor
    
    # TD probability from anytime TD odds
    chase_td_prob = 100 / (140 + 100) * 1.05  # +140 odds, Pinnacle adjustment
    
    chase_base = (
        chase_rec_yards * dk_scoring['rec_yard'] +
        chase_receptions * dk_scoring['reception'] +
        chase_td_prob * dk_scoring['rec_td']
    )
    
    # WR context (pass edge + RZ edge for primary target)
    chase_context = (1 + 0.12 * team_edges['bengals_pass_edge']) * (1 + 0.06 * team_edges['bengals_rz_edge'])
    chase_final = chase_base * chase_context
    
    print(f"Rec Yards: {chase_props['player_reception_yds']['line']} × {chase_combined_factor:.3f} = {chase_rec_yards:.1f}")
    print(f"Receptions: {chase_props['player_receptions']['line']} × {chase_combined_factor:.3f} = {chase_receptions:.1f}")
    print(f"TD Prob: {chase_td_prob:.3f}")
    print(f"Base: {chase_base:.2f}, Context: {chase_context:.3f}, Final: {chase_final:.2f}")
    
    print("\n📊 SUMMARY:")
    print("-" * 40)
    print(f"Joe Burrow: {final_projection:.2f} pts")
    print(f"Trevor Lawrence: {tlaw_final:.2f} pts") 
    print(f"JaMarr Chase: {chase_final:.2f} pts")
    
    print("\n🔑 KEY METHODOLOGY POINTS:")
    print("• Pinnacle/Circa props prioritized (1.10-1.15x multiplier)")
    print("• Market efficiency analysis (tight lines = sharp money)")
    print("• Psychological factors (star player bias = 0.95x)")
    print("• Team edges from holes_and_levers.parquet data")
    print("• DraftKings scoring: Pass TD=4pts, Rush/Rec TD=6pts")
    print("• Anytime TD probabilities from market odds")

if __name__ == "__main__":
    show_projection_breakdown()
