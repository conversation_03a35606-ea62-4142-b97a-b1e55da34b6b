#!/usr/bin/env python3
"""
Focused projections script for DET vs GB game only.
Runs full pipeline with offense and defense projections.
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any

def load_game_data():
    """Load all necessary data for DET vs GB game."""
    print("Loading game data...")
    
    # Load depth charts
    depth_df = pd.read_parquet('data/depth_week1.parquet')
    det_gb_depth = depth_df[depth_df['team'].isin(['Detroit Lions', 'Green Bay Packers'])].copy()
    
    # Load player props
    props_df = pd.read_parquet('data/player_props.parquet')
    det_gb_props = props_df[props_df['team'].isin(['DET', 'GB'])].copy()
    
    # Load odds data for game context
    with open('data/odds_week1.json', 'r') as f:
        odds_data = json.load(f)
    
    print(f"Loaded {len(det_gb_depth)} depth chart entries for DET/GB")
    print(f"Loaded {len(det_gb_props)} player props for DET/GB")
    
    return det_gb_depth, det_gb_props, odds_data

def extract_game_context(odds_data):
    """Extract game context for DET vs GB from odds data."""
    print("Extracting game context...")
    
    # Find DET vs GB game
    det_gb_game = None
    for game in odds_data:
        if ((game['home_team'] == 'Green Bay Packers' and game['away_team'] == 'Detroit Lions') or
            (game['home_team'] == 'Detroit Lions' and game['away_team'] == 'Green Bay Packers')):
            det_gb_game = game
            break
    
    if not det_gb_game:
        print("Warning: DET vs GB game not found in odds data, using defaults")
        return {
            'total': 47.5,
            'spread': 1.5,  # DET favored
            'det_total': 24.5,
            'gb_total': 23.0,
            'game_script': 'competitive'
        }
    
    # Extract spreads and totals from first bookmaker
    bookmaker = det_gb_game['bookmakers'][0]
    spreads = next((m for m in bookmaker['markets'] if m['key'] == 'spreads'), None)
    totals = next((m for m in bookmaker['markets'] if m['key'] == 'totals'), None)
    
    if spreads and totals:
        # Get spread (DET perspective)
        det_spread = 0
        for outcome in spreads['outcomes']:
            if 'Detroit' in outcome['name']:
                det_spread = outcome['point']
                break
        
        # Get total
        total = totals['outcomes'][0]['point']
        
        # Calculate implied team totals
        det_total = (total - det_spread) / 2
        gb_total = (total + det_spread) / 2
        
        context = {
            'total': total,
            'spread': det_spread,
            'det_total': det_total,
            'gb_total': gb_total,
            'game_script': 'competitive' if abs(det_spread) < 3 else 'blowout_risk'
        }
        
        print(f"Game context: Total={total}, DET spread={det_spread}, DET total={det_total:.1f}, GB total={gb_total:.1f}")
        return context
    
    # Fallback
    return {
        'total': 47.5,
        'spread': 1.5,
        'det_total': 24.5,
        'gb_total': 23.0,
        'game_script': 'competitive'
    }

def create_offensive_projections(depth_df, props_df, game_context):
    """Create offensive projections for DET and GB players."""
    print("Creating offensive projections...")

    projections = []

    # Team mapping
    team_map = {'Detroit Lions': 'DET', 'Green Bay Packers': 'GB'}

    for _, player in depth_df.iterrows():
        team_full = player['team']
        team_abbr = team_map.get(team_full, team_full)
        player_name = player['player_name']
        position = player['position']
        depth_order = player['depth_chart_order']  # Fixed column name
        is_starter = player['is_starter']

        # Skip non-offensive positions
        if position not in ['QB', 'RB', 'WR', 'TE']:
            continue

        # Base projection based on position and depth
        base_proj = get_base_projection(position, depth_order, is_starter)

        # Apply player props adjustments
        player_props = props_df[props_df['player_name'] == player_name]
        if len(player_props) > 0:
            base_proj = adjust_for_props(base_proj, player_props, position)
            print(f"  {player_name} ({position}): Props adjustment applied, base={base_proj:.2f}")

        # Apply game context adjustments
        final_proj = apply_game_context(base_proj, team_abbr, position, game_context)

        projections.append({
            'Player': player_name,
            'Projection': round(final_proj, 2),
            'team': team_abbr,
            'position': position,
            'depth_order': depth_order,
            'is_starter': is_starter,
            'type': 'offense'
        })

    return pd.DataFrame(projections)

def create_defensive_projections(game_context):
    """Create team defense projections for DET and GB."""
    print("Creating defensive projections...")
    
    # Base defensive projections
    det_def_base = 8.5  # Detroit defense base
    gb_def_base = 7.2   # Green Bay defense base
    
    # Adjust based on game context
    det_def_proj = det_def_base * (1 + (game_context['gb_total'] - 23.5) / 50)
    gb_def_proj = gb_def_base * (1 + (game_context['det_total'] - 23.5) / 50)
    
    projections = [
        {
            'Player': 'Detroit Lions Defense',
            'Projection': round(det_def_proj, 2),
            'team': 'DET',
            'position': 'DEF',
            'depth_order': 1,
            'type': 'defense'
        },
        {
            'Player': 'Green Bay Packers Defense',
            'Projection': round(gb_def_proj, 2),
            'team': 'GB',
            'position': 'DEF',
            'depth_order': 1,
            'type': 'defense'
        }
    ]
    
    return pd.DataFrame(projections)

def get_base_projection(position, depth_order, is_starter):
    """Get base projection by position and depth."""
    # More realistic base projections for fantasy points
    if position == 'QB':
        if depth_order == 1:
            return 19.5  # Starting QB
        else:
            return 4.0   # Backup QB
    elif position == 'RB':
        if depth_order == 1:
            return 14.2  # RB1
        elif depth_order == 2:
            return 8.5   # RB2
        else:
            return 3.5   # Deep backup
    elif position == 'WR':
        if depth_order == 1:
            return 13.8  # WR1
        elif depth_order == 2:
            return 10.2  # WR2
        elif depth_order == 3:
            return 6.8   # WR3
        else:
            return 4.2   # WR4+
    elif position == 'TE':
        if depth_order == 1:
            return 9.5   # TE1
        elif depth_order == 2:
            return 4.8   # TE2
        else:
            return 2.5   # Deep TE

    return 2.0  # Default

def adjust_for_props(base_proj, player_props, position):
    """Adjust projection based on player props."""
    if len(player_props) == 0:
        return base_proj

    adjustment = 1.0

    # Look for key volume props to gauge usage
    if position == 'QB':
        pass_yards = player_props[player_props['market'] == 'pass_yards']
        if len(pass_yards) > 0:
            line = pass_yards['line'].iloc[0]
            if line > 260:
                adjustment *= 1.25  # High volume passer
            elif line > 230:
                adjustment *= 1.1
            elif line < 200:
                adjustment *= 0.85  # Low volume

    elif position == 'RB':
        rush_yards = player_props[player_props['market'] == 'rush_yards']
        rec_yards = player_props[player_props['market'] == 'rec_yards']

        total_yards_line = 0
        if len(rush_yards) > 0:
            total_yards_line += rush_yards['line'].iloc[0]
        if len(rec_yards) > 0:
            total_yards_line += rec_yards['line'].iloc[0]

        if total_yards_line > 90:
            adjustment *= 1.3   # High usage RB
        elif total_yards_line > 60:
            adjustment *= 1.15
        elif total_yards_line < 30:
            adjustment *= 0.8

    elif position in ['WR', 'TE']:
        rec_yards = player_props[player_props['market'] == 'rec_yards']
        receptions = player_props[player_props['market'] == 'receptions']

        if len(rec_yards) > 0:
            yards_line = rec_yards['line'].iloc[0]
            if yards_line > 70:
                adjustment *= 1.35  # High target share
            elif yards_line > 50:
                adjustment *= 1.2
            elif yards_line < 25:
                adjustment *= 0.75

    # TD props adjustment
    td_props = player_props[player_props['market'] == 'td_anytime']
    if len(td_props) > 0:
        # TD props boost scoring potential
        adjustment *= 1.1

    return base_proj * adjustment

def apply_game_context(base_proj, team, position, game_context):
    """Apply game context adjustments."""
    adjustment = 1.0
    
    # Team total adjustments
    team_total = game_context['det_total'] if team == 'DET' else game_context['gb_total']
    
    if team_total > 25:  # High-scoring team
        adjustment *= 1.15
    elif team_total < 21:  # Low-scoring team
        adjustment *= 0.9
    
    # Game script adjustments
    if game_context['game_script'] == 'competitive':
        if position == 'QB':
            adjustment *= 1.05  # More passing in competitive games
        elif position == 'RB':
            adjustment *= 1.02  # Balanced attack
    
    # Spread adjustments
    spread = game_context['spread']
    if team == 'DET' and spread > 0:  # DET favored
        adjustment *= 1.03
    elif team == 'GB' and spread < 0:  # GB favored
        adjustment *= 1.03
    
    return base_proj * adjustment

def main():
    """Main pipeline for DET vs GB projections."""
    print("=== DET vs GB FOCUSED PROJECTIONS ===")
    
    # Load data
    depth_df, props_df, odds_data = load_game_data()
    
    # Extract game context
    game_context = extract_game_context(odds_data)
    
    # Create projections
    offensive_proj = create_offensive_projections(depth_df, props_df, game_context)
    defensive_proj = create_defensive_projections(game_context)
    
    # Combine projections
    all_projections = pd.concat([offensive_proj, defensive_proj], ignore_index=True)
    
    # Sort by projection descending
    all_projections = all_projections.sort_values('Projection', ascending=False)
    
    # Display results
    print(f"\n=== FINAL PROJECTIONS ({len(all_projections)} players) ===")
    print(all_projections[['Player', 'Projection', 'team', 'position']].to_string(index=False))
    
    # Save results
    output_file = 'det_gb_projections.csv'
    all_projections[['Player', 'Projection']].to_csv(output_file, index=False)
    print(f"\nProjections saved to {output_file}")
    
    return all_projections

if __name__ == "__main__":
    projections = main()
