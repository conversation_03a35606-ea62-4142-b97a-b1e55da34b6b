# WAS @ GB Player Props Setup Instructions

## Quick Start (5 minutes)

### Step 1: Get Your Free API Key
1. Go to https://the-odds-api.com/
2. Sign up for a free account (500 requests/month free)
3. Copy your API key

### Step 2: Add API Key to Environment
1. Open your `.env` file
2. Replace `ODDS_API_KEY=your_odds_api_key_here` with your actual key
3. Save the file

### Step 3: Fetch Live Props
```bash
python was_gb_props_fetcher.py
```

This will:
- ✅ Find the WAS @ GB game automatically
- ✅ Fetch ALL available player props (passing, rushing, receiving, TDs, etc.)
- ✅ Process into your standard format
- ✅ Save as CSV for projections
- ✅ Show coverage analysis

## What Props Are Available

The Odds API provides these NFL player prop markets:

### Core Props
- **Passing**: Yards, TDs, Completions, Attempts, Interceptions
- **Rushing**: Yards, TDs, Attempts, Longest Rush
- **Receiving**: Yards, TDs, Receptions, Longest Reception
- **Combined**: Rush+Rec Yards, Pass+Rush+Rec Yards/TDs
- **Scoring**: Anytime TD, First TD, Last TD
- **Kicking**: Field Goals, Kicking Points, PATs
- **Defense**: Tackles, Sacks, Interceptions

### Alternate Lines
- Multiple lines for key props (different over/under values)
- Sharp money indicators from line movement

## Integration with Your System

Once you have the props data, it automatically integrates with your existing:

1. **50% Props Weight** - Your preferred projection methodology
2. **Prop Signal Extractor** - Already built and ready
3. **Game Context Engine** - Weather, game script, etc.
4. **DraftKings Data** - Blends with your existing player pool

## Fallback Options

If The Odds API doesn't have full coverage:

### Option A: Manual Props Gathering
1. Visit major sportsbooks (DraftKings, FanDuel, BetMGM)
2. Copy props into this format:
```csv
player_name,market,line,over_odds,under_odds,bookmaker
Jayden Daniels,player_pass_yds,249.5,-110,-110,DraftKings
Josh Jacobs,player_rush_yds,89.5,-115,+105,DraftKings
```

### Option B: Hybrid Approach
1. Use API for available props
2. Manually add missing key players
3. System will blend both sources

## Expected Coverage

Based on The Odds API documentation, you should get:
- ✅ All starting QBs, RBs, top 2 WRs, TE1
- ✅ Major props (yards, TDs, receptions)
- ✅ Multiple sportsbooks for line shopping
- ⚠️  May miss some bench players or exotic props

## Next Steps After Props Fetch

1. **Review Coverage**: Check which key players have props
2. **Create Projections**: Run your existing projection system
3. **Blend with Models**: Apply your 50/30/20 weighting
4. **Weather Integration**: Factor in Green Bay conditions
5. **Game Script**: Use spread/total for context

## Cost Considerations

- **Free Tier**: 500 requests/month
- **Each Game**: ~2-3 requests (odds + props)
- **Season Usage**: ~50-100 games = well within free limits

## Troubleshooting

If props aren't available:
1. Check if game is in The Odds API coverage
2. Try different team name variations
3. Fall back to manual gathering
4. Use DraftKings data as baseline

The system is designed to work with whatever data you can get!
