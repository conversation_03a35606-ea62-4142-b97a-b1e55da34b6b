#!/usr/bin/env python3
"""
UNM @ UCLA — 100,000-run simulation of player fantasy points using props-only distributions.
- Reuses the fitting functions from unm_ucla_build_projections to get per-player market mu/sigma
- Samples correlated receptions/receiving yards per player; Poisson for TDs/INTs when available
- Applies anytime TD only if no explicit rush/rec TD props for that player
- Outputs per-player distribution summary CSV
"""

import json
import math
import numpy as np
import pandas as pd
from pathlib import Path
from typing import Dict, List, Tuple, Any

from scipy.stats import norm, poisson

# Reuse builder to keep one source of truth
import importlib
_builder = importlib.import_module('unm_ucla_build_projections')

RAW_PATH = Path('data/unm_ucla_props_raw.json')
OUT_SIM_PATH = Path('data/unm_ucla_player_sim_100k.csv')
N_RUNS = 100_000

# Correlation between receptions and receiving yards within a player
R_REC_RECY = 0.6

# Minimum sigma floors to avoid degenerate normals
SIGMA_FLOORS = {
    'player_pass_yds': 20.0,
    'player_rush_yds': 12.0,
    'player_reception_yds': 10.0,
    'player_receptions': 1.2,
}

# Fallback variance multipliers if fit returns tiny sigma
SIGMA_BOOST = 1.15


def build_player_distributions(raw: Dict[str, Any]):
    """Return player-> { market_key: (mu, sigma) } plus anytime TD probs.
    Uses the same market aggregation as the projections builder.
    """
    pts = _builder.collect_points_from_markets(raw)

    # Fit mu/sigma per market
    dists: Dict[str, Dict[str, Tuple[float, float]]] = {}
    for player, markets in pts.items():
        d: Dict[str, Tuple[float, float]] = {}
        for mkey, plist in markets.items():
            if mkey == _builder.ANYTIME_KEY:
                continue
            fit = _builder.fit_mu_sigma(plist)
            if fit is None:
                fit = _builder.single_point_mu(plist, mkey)
            if fit is None:
                continue
            mu, sigma = fit
            # sigma floors
            sf = SIGMA_FLOORS.get(mkey, 0.0)
            sigma = max(abs(float(sigma)) * SIGMA_BOOST, sf) if sf else max(abs(float(sigma)) * SIGMA_BOOST, 1e-6)
            d[mkey] = (max(0.0, float(mu)), float(sigma))
        if d:
            dists[player] = d

    anytime = _builder.attach_anytime_td_bonus(pts, {})  # independent of stats
    return dists, anytime


def simulate_player_fp(player: str,
                        dists: Dict[str, Tuple[float, float]],
                        anytime_p: float,
                        n: int) -> np.ndarray:
    """Simulate fantasy points for a single player for n runs.
    Scoring mirrors SCORING in builder.
    """
    # Map keys
    to_stat = _builder.BASE_MARKET_MAP
    scoring = _builder.SCORING

    # Extract mus/sigmas for relevant stats
    mu_py, sd_py = dists.get('player_pass_yds', (0.0, 0.0))
    mu_pry, sd_pry = dists.get('player_rush_yds', (0.0, 0.0))
    mu_recy, sd_recy = dists.get('player_reception_yds', (0.0, 0.0))
    mu_rec, sd_rec = dists.get('player_receptions', (0.0, 0.0))

    mu_ptd, _ = dists.get('player_pass_tds', (0.0, 0.0))
    mu_rtd, _ = dists.get('player_rush_tds', (0.0, 0.0))
    mu_retd, _ = dists.get('player_reception_tds', (0.0, 0.0))
    mu_ints, _ = dists.get('player_pass_interceptions', (0.0, 0.0))

    mu_kp, sd_kp = dists.get('player_kicking_points', (0.0, 0.0))

    # Sample shared normal for receptions & rec yards to induce correlation
    z = np.random.normal(size=n)
    rec = np.maximum(0.0, mu_rec + sd_rec * z)
    recy = np.maximum(0.0, mu_recy + sd_recy * (R_REC_RECY * z + math.sqrt(max(1e-6, 1 - R_REC_RECY**2)) * np.random.normal(size=n)))

    # Other continuous
    pass_y = np.maximum(0.0, mu_py + sd_py * np.random.normal(size=n))
    rush_y = np.maximum(0.0, mu_pry + sd_pry * np.random.normal(size=n))
    kp = np.maximum(0.0, mu_kp + sd_kp * np.random.normal(size=n))

    # TDs and INTs as Poisson counts when means available
    pass_tds = poisson.rvs(mu_ptd, size=n) if mu_ptd > 0 else np.zeros(n)
    rush_tds = poisson.rvs(mu_rtd, size=n) if mu_rtd > 0 else np.zeros(n)
    rec_tds = poisson.rvs(mu_retd, size=n) if mu_retd > 0 else np.zeros(n)
    ints = poisson.rvs(mu_ints, size=n) if mu_ints > 0 else np.zeros(n)

    # Anytime TD as Bernoulli only if no specific rush/rec TD props (avoid double counting)
    add_any = (mu_rtd <= 0 and mu_retd <= 0 and anytime_p > 0)
    anytime_draw = (np.random.uniform(size=n) < anytime_p).astype(float) if add_any else np.zeros(n)

    fp = (
        pass_y * scoring['pass_yards'] + pass_tds * scoring['pass_tds'] + (-ints) * abs(scoring['pass_ints']) +
        rush_y * scoring['rush_yards'] + rush_tds * scoring['rush_tds'] +
        recy * scoring['rec_yards'] + rec * scoring['receptions'] + rec_tds * scoring['rec_tds'] +
        kp * scoring['kicking_points'] + anytime_draw * 6.0
    )
    return fp


def run_simulation(n_runs: int = N_RUNS) -> pd.DataFrame:
    raw = json.loads(RAW_PATH.read_text())
    dists, anytime = build_player_distributions(raw)

    results = []
    for player, markets in dists.items():
        fp = simulate_player_fp(player, markets, anytime.get(player, 0.0), n_runs)
        results.append({
            'player_name': player,
            'mean': float(np.mean(fp)),
            'median': float(np.median(fp)),
            'stdev': float(np.std(fp)),
            'p05': float(np.percentile(fp, 5)),
            'p25': float(np.percentile(fp, 25)),
            'p75': float(np.percentile(fp, 75)),
            'p95': float(np.percentile(fp, 95)),
        })

    df = pd.DataFrame(results).sort_values('mean', ascending=False)
    OUT_SIM_PATH.parent.mkdir(parents=True, exist_ok=True)
    df.to_csv(OUT_SIM_PATH, index=False)
    return df


def main():
    df = run_simulation()
    print(f"Saved player simulation to {OUT_SIM_PATH}")
    print("Top 10 by mean:")
    print(df.head(10).to_string(index=False))


if __name__ == '__main__':
    main()

