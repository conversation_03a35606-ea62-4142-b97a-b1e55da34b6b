#!/usr/bin/env python3
"""
Elite DraftKings Fantasy Projections: Dallas Cowboys vs NY Giants
Implements elite-level prop reading methodology with team ratings integration.
"""

import os
import sys
import pandas as pd
import numpy as np
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import json
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append('src')

from proj.fetch_odds import (
    get_totals_spreads,
    get_player_props,
    find_game_event_id,
    process_player_props_response
)

load_dotenv()


@dataclass
class GameContext:
    """Game context with market data and team ratings."""
    home_team: str = "Bengals"
    away_team: str = "Jaguars"
    spread: float = 0.0
    total: float = 0.0
    home_implied_total: float = 0.0
    away_implied_total: float = 0.0
    market_strength: float = 0.0
    market_uncertainty: float = 0.0
    event_id: Optional[str] = None


@dataclass
class TeamEdges:
    """Computed matchup edges for a team."""
    edge_pass: float = 0.0
    edge_rush: float = 0.0
    edge_rz: float = 0.0
    edge_explosive: float = 0.0
    edge_protection: float = 0.0
    edge_penalty: float = 0.0
    edge_plays: float = 0.0


@dataclass
class TeamProjections:
    """Model-based team projections before prop blending."""
    plays: float = 0.0
    pass_attempts: float = 0.0
    pass_yards: float = 0.0
    pass_tds: float = 0.0
    rush_attempts: float = 0.0
    rush_yards: float = 0.0
    rush_tds: float = 0.0
    total_tds: float = 0.0
    sacks_allowed: float = 0.0
    turnovers: float = 0.0


class CowboysGiantsEliteProjections:
    """Elite projection system for Cowboys vs Giants."""
    
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game_context = GameContext()
        
        # DraftKings scoring system (CORRECT)
        self.dk_scoring = {
            'pass_yards': 0.04,  # 25 yards = 1 pt
            'pass_tds': 4.0,     # 4 pts per TD
            'pass_completions': 0.0,  # NO POINTS for completions in DK
            'pass_interceptions': -1.0,
            'rush_yards': 0.1,   # 10 yards = 1 pt
            'rush_tds': 6.0,
            'reception_yards': 0.1,  # 10 yards = 1 pt
            'receptions': 1.0,   # 1 pt per reception
            'reception_tds': 6.0,
            'fumbles_lost': -1.0,
            'kicking_points': 1.0,
            'sacks': 1.0,
            'interceptions': 2.0,
            'fumble_recoveries': 2.0,
            'defensive_tds': 6.0,
            'safeties': 2.0,
            'blocked_kicks': 2.0,
            'points_allowed_0': 10.0,
            'points_allowed_1_6': 7.0,
            'points_allowed_7_13': 4.0,
            'points_allowed_14_20': 1.0,
            'points_allowed_21_27': 0.0,
            'points_allowed_28_34': -1.0,
            'points_allowed_35_plus': -4.0
        }
        
        # Load team data
        self.team_ratings = None
        self.holes_levers = None
        self.player_pool = None
        self.props_data = None
        
    def load_team_data(self):
        """Load team ratings and holes/levers data."""
        try:
            print("📊 Loading team ratings and holes/levers data...")
            
            # Load team unit ratings
            self.team_ratings = pd.read_parquet('models/team_unit_ratings.parquet')
            print(f"✅ Loaded team ratings: {len(self.team_ratings)} records")
            
            # Load holes and levers
            self.holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
            print(f"✅ Loaded holes/levers: {len(self.holes_levers)} records")
            
            # Load player pool (skip first empty row)
            self.player_pool = pd.read_csv('csvs/draftkings_NFL_2025-week-2_players.csv', skiprows=1)
            print(f"✅ Loaded player pool: {len(self.player_pool)} players")
            
            return True
            
        except Exception as e:
            print(f"❌ Error loading team data: {e}")
            return False
    
    def fetch_market_data(self) -> bool:
        """Step 1: Ingest market data from Odds API."""
        if not self.api_key:
            print("⚠️ No Odds API key - using default game context")
            self.game_context = GameContext(
                spread=-3.0,  # Cowboys favored by 3
                total=45.5,
                home_implied_total=21.25,
                away_implied_total=24.25,
                market_strength=0.6,
                market_uncertainty=0.3
            )
            return True
            
        try:
            print("🔍 Fetching Cowboys vs Giants market data...")
            
            # Fetch game odds
            odds_data = get_totals_spreads(self.api_key)
            
            # Find Jaguars vs Bengals game
            game_found = False
            for game in odds_data:
                home = game.get('home_team', '').lower()
                away = game.get('away_team', '').lower()

                if (('bengals' in home or 'cincinnati' in home) and
                    ('jaguars' in away or 'jacksonville' in away)) or \
                   (('jaguars' in home or 'jacksonville' in home) and
                    ('bengals' in away or 'cincinnati' in away)):
                    
                    # Extract game data
                    spread = game.get('spread', -3.0)
                    total = game.get('total', 45.5)
                    
                    # Calculate implied totals
                    home_implied = (total - spread) / 2
                    away_implied = (total + spread) / 2
                    
                    # Compute market strength and uncertainty
                    bookmakers = game.get('bookmakers', [])
                    book_count = len(bookmakers)
                    
                    # Market strength based on book count and consensus
                    market_strength = min(1.0, book_count / 10.0) * 0.8
                    
                    # Market uncertainty based on line variance
                    spreads = [bm.get('spread', spread) for bm in bookmakers if bm.get('spread')]
                    totals = [bm.get('total', total) for bm in bookmakers if bm.get('total')]
                    
                    spread_variance = np.var(spreads) if len(spreads) > 1 else 0
                    total_variance = np.var(totals) if len(totals) > 1 else 0
                    market_uncertainty = min(1.0, (spread_variance + total_variance) / 2)
                    
                    self.game_context = GameContext(
                        spread=spread,
                        total=total,
                        home_implied_total=home_implied,
                        away_implied_total=away_implied,
                        market_strength=market_strength,
                        market_uncertainty=market_uncertainty,
                        event_id=game.get('id')
                    )
                    
                    print(f"✅ Found game: {away.title()} @ {home.title()}")
                    print(f"   Spread: {spread}")
                    print(f"   Total: {total}")
                    print(f"   Market Strength: {market_strength:.2f}")
                    print(f"   Market Uncertainty: {market_uncertainty:.2f}")
                    
                    game_found = True
                    break
            
            if not game_found:
                print("⚠️ Jaguars vs Bengals game not found, using defaults")
                self.game_context = GameContext(
                    spread=-7.5,  # Bengals favored at home
                    total=44.5,
                    home_implied_total=26.0,
                    away_implied_total=18.5,
                    market_strength=0.6,
                    market_uncertainty=0.3
                )
            
            # Fetch player props if event ID available
            if self.game_context.event_id:
                print("🎯 Fetching comprehensive player props...")
                try:
                    # Define all the markets we want including TDs
                    all_markets = [
                        'player_pass_yds', 'player_pass_tds', 'player_pass_completions',
                        'player_pass_interceptions', 'player_pass_attempts',
                        'player_rush_yds', 'player_rush_tds', 'player_rush_attempts',
                        'player_reception_yds', 'player_receptions', 'player_reception_tds',
                        'player_rush_reception_yds', 'player_rush_reception_tds',
                        'player_anytime_td', 'player_1st_td', 'player_last_td',
                        'player_sacks', 'player_tackles_assists', 'player_defensive_interceptions',
                        'player_kicking_points', 'player_field_goals'
                    ]

                    props_data = get_player_props(self.api_key, self.game_context.event_id, markets=all_markets)
                    if props_data:
                        self.props_data = process_player_props_response(props_data)
                        print(f"✅ Loaded {len(self.props_data)} player props")

                        # Show what markets we actually got
                        markets_found = self.props_data['market'].value_counts()
                        print("📊 Markets found:")
                        for market, count in markets_found.items():
                            print(f"   {market}: {count} props")
                    else:
                        print("⚠️ No player props data available")
                except Exception as e:
                    print(f"⚠️ Error fetching player props: {e}")
            
            return True
            
        except Exception as e:
            print(f"❌ Error fetching market data: {e}")
            return False
    
    def compute_team_edges(self) -> Tuple[TeamEdges, TeamEdges]:
        """Step 2: Compute matchup edges for both teams."""
        print("⚔️ Computing team matchup edges...")
        
        # Get latest team data (week 2)
        jaguars_ratings = self.team_ratings[
            (self.team_ratings['team'] == 'Jaguars') &
            (self.team_ratings['as_of_week'] == 2)
        ].iloc[0] if len(self.team_ratings[
            (self.team_ratings['team'] == 'Jaguars') &
            (self.team_ratings['as_of_week'] == 2)
        ]) > 0 else None

        bengals_ratings = self.team_ratings[
            (self.team_ratings['team'] == 'Bengals') &
            (self.team_ratings['as_of_week'] == 2)
        ].iloc[0] if len(self.team_ratings[
            (self.team_ratings['team'] == 'Bengals') &
            (self.team_ratings['as_of_week'] == 2)
        ]) > 0 else None

        jaguars_hl = self.holes_levers[
            (self.holes_levers['team'] == 'Jaguars') &
            (self.holes_levers['as_of_week'] == 2)
        ].iloc[0] if len(self.holes_levers[
            (self.holes_levers['team'] == 'Jaguars') &
            (self.holes_levers['as_of_week'] == 2)
        ]) > 0 else None

        bengals_hl = self.holes_levers[
            (self.holes_levers['team'] == 'Bengals') &
            (self.holes_levers['as_of_week'] == 2)
        ].iloc[0] if len(self.holes_levers[
            (self.holes_levers['team'] == 'Bengals') &
            (self.holes_levers['as_of_week'] == 2)
        ]) > 0 else None

        if not all([jaguars_ratings is not None, bengals_ratings is not None,
                   jaguars_hl is not None, bengals_hl is not None]):
            print("⚠️ Missing team data, using default edges")
            return TeamEdges(), TeamEdges()

        # Jaguars edges (away team)
        jaguars_edges = TeamEdges(
            edge_pass=jaguars_hl['lever_explosive_pass'] - bengals_hl['hole_pass_eff'],
            edge_rush=jaguars_ratings['OFF_rush_ypa_rating'] - bengals_ratings['DEF_rush_ypa_allowed_rating'],
            edge_rz=jaguars_hl['lever_rz'] - bengals_hl['hole_rz'],
            edge_explosive=jaguars_hl['lever_explosive_overall'] - bengals_hl['hole_explosive_pass'],
            edge_protection=jaguars_hl['lever_protection'] - bengals_hl['hole_pressure'],
            edge_penalty=jaguars_hl.get('lever_penalty_discipline', 0) - bengals_hl['hole_penalty_fp'],
            edge_plays=jaguars_ratings['OFF_ppd_rating'] - bengals_ratings['DEF_ppd_allowed_rating']
        )

        # Bengals edges (home team)
        bengals_edges = TeamEdges(
            edge_pass=bengals_hl['lever_explosive_pass'] - jaguars_hl['hole_pass_eff'],
            edge_rush=bengals_ratings['OFF_rush_ypa_rating'] - jaguars_ratings['DEF_rush_ypa_allowed_rating'],
            edge_rz=bengals_hl['lever_rz'] - jaguars_hl['hole_rz'],
            edge_explosive=bengals_hl['lever_explosive_overall'] - jaguars_hl['hole_explosive_pass'],
            edge_protection=bengals_hl['lever_protection'] - jaguars_hl['hole_pressure'],
            edge_penalty=bengals_hl.get('lever_penalty_discipline', 0) - jaguars_hl['hole_penalty_fp'],
            edge_plays=bengals_ratings['OFF_ppd_rating'] - jaguars_ratings['DEF_ppd_allowed_rating']
        )

        print(f"✅ Jaguars edges - Pass: {jaguars_edges.edge_pass:.2f}, Rush: {jaguars_edges.edge_rush:.2f}, RZ: {jaguars_edges.edge_rz:.2f}")
        print(f"✅ Bengals edges - Pass: {bengals_edges.edge_pass:.2f}, Rush: {bengals_edges.edge_rush:.2f}, RZ: {bengals_edges.edge_rz:.2f}")

        return jaguars_edges, bengals_edges

    def model_team_totals(self, team_edges: TeamEdges, base_implied_total: float) -> TeamProjections:
        """Step 3: Model pre-market team totals using edges."""

        # Base projections from implied total
        base_plays = base_implied_total * 2.8  # ~2.8 plays per point
        base_pass_attempts = base_plays * 0.6  # 60% pass plays
        base_rush_attempts = base_plays * 0.4  # 40% rush plays
        base_pass_ypp = 6.5  # Base yards per pass attempt
        base_rush_ypa = 4.2  # Base yards per rush attempt
        base_rz_td_pct = 0.55  # Base red zone TD%
        base_sack_rate = 0.07  # Base sack rate

        # Apply edge adjustments
        plays_adj = base_plays * (1 + 0.05 * team_edges.edge_plays)
        pass_att_adj = base_pass_attempts * (1 + 0.03 * team_edges.edge_pass)
        rush_att_adj = base_rush_attempts * (1 + 0.03 * team_edges.edge_rush)

        pass_ypp_adj = base_pass_ypp * (1 + 0.06 * team_edges.edge_pass + 0.04 * team_edges.edge_explosive)
        rush_ypa_adj = base_rush_ypa * (1 + 0.05 * team_edges.edge_rush)
        rz_td_pct_adj = base_rz_td_pct * (1 + 0.07 * team_edges.edge_rz)
        sack_rate_adj = base_sack_rate * (1 - 0.06 * team_edges.edge_protection)

        # Calculate totals
        pass_yards = pass_att_adj * pass_ypp_adj
        rush_yards = rush_att_adj * rush_ypa_adj

        # TD calculations
        red_zone_trips = (pass_yards + rush_yards) / 80  # Rough RZ trips estimate
        total_tds = red_zone_trips * rz_td_pct_adj
        pass_tds = total_tds * 0.65  # 65% passing TDs
        rush_tds = total_tds * 0.35  # 35% rushing TDs

        # Sacks and turnovers
        sacks_allowed = pass_att_adj * sack_rate_adj
        turnovers = (pass_att_adj * 0.025) + (rush_att_adj * 0.015)  # Base turnover rates

        return TeamProjections(
            plays=plays_adj,
            pass_attempts=pass_att_adj,
            pass_yards=pass_yards,
            pass_tds=pass_tds,
            rush_attempts=rush_att_adj,
            rush_yards=rush_yards,
            rush_tds=rush_tds,
            total_tds=total_tds,
            sacks_allowed=sacks_allowed,
            turnovers=turnovers
        )

    def analyze_props_like_elite_bettor(self) -> Dict[str, Any]:
        """Step 4: Analyze props with elite-level methodology - read between the lines."""
        if self.props_data is None or self.props_data.empty:
            print("❌ No props data to analyze")
            return {}

        print("🧠 ANALYZING PROPS WITH ELITE METHODOLOGY...")
        print(f"📊 Total props available: {len(self.props_data)}")

        # Group props by player and market
        props_analysis = {}

        for _, prop in self.props_data.iterrows():
            player = prop['player_name']
            market = prop['market']
            line = prop['line']
            over_odds = prop['over_odds']
            under_odds = prop['under_odds']
            book = prop['bookmaker']

            if player not in props_analysis:
                props_analysis[player] = {}

            if market not in props_analysis[player]:
                props_analysis[player][market] = {
                    'lines': [],
                    'over_odds': [],
                    'under_odds': [],
                    'books': [],
                    'consensus_line': 0,
                    'sharp_line': 0,
                    'market_strength': 0,
                    'implied_total': 0,
                    'confidence': 0
                }

            props_analysis[player][market]['lines'].append(line)
            props_analysis[player][market]['over_odds'].append(over_odds)
            props_analysis[player][market]['under_odds'].append(under_odds)
            props_analysis[player][market]['books'].append(book)

        # Analyze each prop with elite methodology
        for player in props_analysis:
            for market in props_analysis[player]:
                prop_data = props_analysis[player][market]
                lines = prop_data['lines']
                over_odds = prop_data['over_odds']
                under_odds = prop_data['under_odds']
                books = prop_data['books']

                if not lines:
                    continue

                # Elite analysis: Look for sharp vs public money indicators
                book_count = len(lines)
                line_variance = np.var(lines) if len(lines) > 1 else 0

                # Identify sharp books (Pinnacle, Circa, etc.)
                sharp_books = ['pinnacle', 'circa', 'bookmaker']
                sharp_lines = [lines[i] for i, book in enumerate(books)
                              if any(sharp in book.lower() for sharp in sharp_books)]

                # Market strength based on book count and line agreement
                market_strength = min(1.0, book_count / 8.0) * (1.0 - min(1.0, line_variance))

                # Consensus line (weighted by market strength)
                consensus_line = np.mean(lines)
                sharp_line = np.mean(sharp_lines) if sharp_lines else consensus_line

                # Convert odds to implied probabilities
                over_probs = [self.american_to_prob(odds) for odds in over_odds]
                under_probs = [self.american_to_prob(odds) for odds in under_odds]

                # Devig and find true probability
                avg_over_prob = np.mean(over_probs)
                avg_under_prob = np.mean(under_probs)
                total_prob = avg_over_prob + avg_under_prob

                # Remove vig
                true_over_prob = avg_over_prob / total_prob if total_prob > 0 else 0.5

                # Calculate implied mean using elite methodology
                implied_mean = self.calculate_implied_mean_elite(
                    consensus_line, true_over_prob, market, sharp_line
                )

                # Confidence based on market factors
                confidence = (
                    0.4 * market_strength +
                    0.3 * (1.0 - line_variance / max(consensus_line, 1)) +
                    0.2 * min(1.0, book_count / 10.0) +
                    0.1 * (1.0 if sharp_lines else 0.5)
                )

                # Store analysis
                prop_data['consensus_line'] = consensus_line
                prop_data['sharp_line'] = sharp_line
                prop_data['market_strength'] = market_strength
                prop_data['implied_total'] = implied_mean
                prop_data['confidence'] = confidence
                prop_data['book_count'] = book_count
                prop_data['line_variance'] = line_variance

        return props_analysis

    def american_to_prob(self, odds: int) -> float:
        """Convert American odds to implied probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)

    def calculate_implied_mean_elite(self, line: float, true_prob: float, market: str, sharp_line: float) -> float:
        """Calculate implied mean using elite prop reading methodology."""

        # Base implied mean from line
        base_mean = line

        # Adjust based on market type and true probability
        if 'pass_yds' in market or 'rush_yds' in market or 'rec_yds' in market:
            # For yardage props, use line as baseline but adjust for probability
            if true_prob > 0.55:  # Market expects over
                implied_mean = line * 1.05
            elif true_prob < 0.45:  # Market expects under
                implied_mean = line * 0.95
            else:
                implied_mean = line

        elif 'receptions' in market or 'completions' in market:
            # For counting stats, similar logic
            if true_prob > 0.55:
                implied_mean = line * 1.03
            elif true_prob < 0.45:
                implied_mean = line * 0.97
            else:
                implied_mean = line

        elif 'tds' in market or 'touchdowns' in market:
            # For TD props, be more conservative
            if true_prob > 0.6:
                implied_mean = line * 1.1
            elif true_prob < 0.4:
                implied_mean = line * 0.9
            else:
                implied_mean = line

        else:
            implied_mean = line

        # Factor in sharp vs public divergence
        if abs(sharp_line - line) > 0.5:
            # Sharp money disagrees with consensus
            sharp_weight = 0.3
            implied_mean = implied_mean * (1 - sharp_weight) + sharp_line * sharp_weight

        return implied_mean

    def create_projections_from_props(self, props_analysis: Dict[str, Any]) -> pd.DataFrame:
        """Step 5: Create projections directly from prop analysis - the elite way."""

        print("🎯 CREATING PROJECTIONS FROM ELITE PROP ANALYSIS...")

        player_projections = []

        for player_name, markets in props_analysis.items():
            if not markets:
                continue

            print(f"📊 Analyzing {player_name}...")

            # Initialize projection components
            fantasy_points = 0.0
            confidence_scores = []

            # Process each market for this player
            for market, analysis in markets.items():
                implied_total = analysis['implied_total']
                confidence = analysis['confidence']
                market_strength = analysis['market_strength']

                confidence_scores.append(confidence)

                # Convert prop to fantasy points using elite methodology
                if 'pass_yds' in market:
                    fantasy_points += implied_total * self.dk_scoring['pass_yards']
                    print(f"  Pass Yards: {implied_total:.1f} → {implied_total * self.dk_scoring['pass_yards']:.2f} pts")

                elif 'pass_tds' in market or 'pass_touchdowns' in market:
                    fantasy_points += implied_total * self.dk_scoring['pass_tds']
                    print(f"  Pass TDs: {implied_total:.2f} → {implied_total * self.dk_scoring['pass_tds']:.2f} pts")

                elif 'pass_completions' in market:
                    # DraftKings does NOT score completions - skip this
                    print(f"  Completions: {implied_total:.1f} → 0.00 pts (DK doesn't score completions)")

                elif 'pass_interceptions' in market:
                    fantasy_points += implied_total * self.dk_scoring['pass_interceptions']
                    print(f"  Interceptions: {implied_total:.2f} → {implied_total * self.dk_scoring['pass_interceptions']:.2f} pts")

                elif 'rush_yds' in market:
                    fantasy_points += implied_total * self.dk_scoring['rush_yards']
                    print(f"  Rush Yards: {implied_total:.1f} → {implied_total * self.dk_scoring['rush_yards']:.2f} pts")

                elif 'rush_tds' in market or 'rush_touchdowns' in market:
                    fantasy_points += implied_total * self.dk_scoring['rush_tds']
                    print(f"  Rush TDs: {implied_total:.2f} → {implied_total * self.dk_scoring['rush_tds']:.2f} pts")

                elif 'reception_yds' in market or 'rec_yds' in market or 'receiving_yds' in market:
                    fantasy_points += implied_total * self.dk_scoring['reception_yards']
                    print(f"  Rec Yards: {implied_total:.1f} → {implied_total * self.dk_scoring['reception_yards']:.2f} pts")

                elif 'receptions' in market:
                    fantasy_points += implied_total * self.dk_scoring['receptions']
                    print(f"  Receptions: {implied_total:.1f} → {implied_total * self.dk_scoring['receptions']:.2f} pts")

                elif 'reception_tds' in market or 'rec_tds' in market or 'receiving_touchdowns' in market:
                    fantasy_points += implied_total * self.dk_scoring['reception_tds']
                    print(f"  Rec TDs: {implied_total:.2f} → {implied_total * self.dk_scoring['reception_tds']:.2f} pts")

                elif 'rush_tds' in market or 'rushing_touchdowns' in market:
                    fantasy_points += implied_total * self.dk_scoring['rush_tds']
                    print(f"  Rush TDs: {implied_total:.2f} → {implied_total * self.dk_scoring['rush_tds']:.2f} pts")

                elif 'rush_reception_tds' in market:
                    # Combined rush + reception TDs
                    fantasy_points += implied_total * 6.0  # 6 points per TD
                    print(f"  Rush+Rec TDs: {implied_total:.2f} → {implied_total * 6.0:.2f} pts")

                elif 'anytime_td' in market:
                    # Anytime TD is roughly equivalent to 0.5-0.8 TDs depending on position
                    td_equivalent = implied_total * 0.65  # Conservative conversion
                    fantasy_points += td_equivalent * 6.0  # 6 points per TD
                    print(f"  Anytime TD: {implied_total:.2f} → {td_equivalent * 6.0:.2f} pts")

                elif '1st_td' in market:
                    # First TD scorer - lower probability but still valuable
                    td_equivalent = implied_total * 0.4  # More conservative
                    fantasy_points += td_equivalent * 6.0
                    print(f"  1st TD: {implied_total:.2f} → {td_equivalent * 6.0:.2f} pts")

                elif 'last_td' in market:
                    # Last TD scorer - similar to first TD
                    td_equivalent = implied_total * 0.4
                    fantasy_points += td_equivalent * 6.0
                    print(f"  Last TD: {implied_total:.2f} → {td_equivalent * 6.0:.2f} pts")

                elif 'sacks' in market:
                    fantasy_points += implied_total * self.dk_scoring['sacks']
                    print(f"  Sacks: {implied_total:.1f} → {implied_total * self.dk_scoring['sacks']:.2f} pts")

                elif 'defensive_interceptions' in market:
                    fantasy_points += implied_total * self.dk_scoring['interceptions']
                    print(f"  Def INTs: {implied_total:.2f} → {implied_total * self.dk_scoring['interceptions']:.2f} pts")

                elif 'tackles_assists' in market:
                    # Tackles + Assists - DraftKings doesn't typically score these for individual players
                    # But we can use them for team defense calculations
                    print(f"  Tackles+Assists: {implied_total:.1f} → 0.00 pts (used for team D/ST)")

            # Overall confidence for this player
            overall_confidence = np.mean(confidence_scores) if confidence_scores else 0.5

            # Apply team context adjustments from our ratings
            context_multiplier = self.apply_team_context_to_player(player_name, fantasy_points)
            final_projection = fantasy_points * context_multiplier

            print(f"  Final: {fantasy_points:.2f} × {context_multiplier:.3f} = {final_projection:.2f} pts")

            player_projections.append({
                'Player': player_name,
                'Projection': round(final_projection, 2),
                'Confidence': round(overall_confidence, 3),
                'Markets': len(markets),
                'Raw_Points': round(fantasy_points, 2),
                'Context_Multiplier': round(context_multiplier, 3)
            })

        df = pd.DataFrame(player_projections)
        return df.sort_values('Projection', ascending=False)

    def create_defense_projections(self, props_analysis: Dict[str, Any]) -> pd.DataFrame:
        """Create team D/ST projections based on defensive props and game context."""

        print("🛡️ CREATING TEAM DEFENSE PROJECTIONS...")

        defense_projections = []

        # Get team edges for context
        team1_edges, team2_edges = self.compute_team_edges()

        # Create projections for both team defenses
        teams = [
            {'name': 'Jaguars D/ST', 'opponent_total': self.game_context.home_implied_total, 'edges': team1_edges},
            {'name': 'Bengals D/ST', 'opponent_total': self.game_context.away_implied_total, 'edges': team2_edges}
        ]

        for team in teams:
            defense_name = team['name']
            opponent_total = team['opponent_total']
            edges = team['edges']

            fantasy_points = 0.0

            # 1. Points Allowed scoring (biggest component)
            if opponent_total <= 6:
                points_allowed_pts = 10.0  # 0 points allowed
            elif opponent_total <= 13:
                points_allowed_pts = 7.0   # 1-6 points
            elif opponent_total <= 20:
                points_allowed_pts = 4.0   # 7-13 points
            elif opponent_total <= 27:
                points_allowed_pts = 1.0   # 14-20 points
            elif opponent_total <= 34:
                points_allowed_pts = 0.0   # 21-27 points
            elif opponent_total <= 41:
                points_allowed_pts = -1.0  # 28-34 points
            else:
                points_allowed_pts = -4.0  # 35+ points

            fantasy_points += points_allowed_pts
            print(f"  {defense_name} - Points Allowed ({opponent_total:.1f}): {points_allowed_pts:.1f} pts")

            # 2. Sacks - estimate based on team edges and individual props
            base_sacks = 2.5  # Average sacks per game
            if 'Bengals' in defense_name:
                # Bengals defense gets boost from edges
                sack_multiplier = 1.0 + (edges.edge_protection * 0.1)  # Protection edge helps get sacks
            else:
                sack_multiplier = 1.0 + (edges.edge_protection * 0.1)

            projected_sacks = base_sacks * sack_multiplier
            sack_points = projected_sacks * 1.0  # 1 pt per sack
            fantasy_points += sack_points
            print(f"  {defense_name} - Sacks: {projected_sacks:.1f} → {sack_points:.1f} pts")

            # 3. Turnovers - estimate based on opponent turnover tendency
            base_turnovers = 1.2  # Average turnovers forced per game
            if 'Bengals' in defense_name:
                # Bengals defense benefits from Jaguars' struggles
                turnover_multiplier = 1.0 + (abs(team1_edges.edge_protection) * 0.05)  # More pressure = more turnovers
            else:
                turnover_multiplier = 1.0 + (abs(team2_edges.edge_protection) * 0.05)

            projected_turnovers = base_turnovers * turnover_multiplier
            turnover_points = projected_turnovers * 2.0  # 2 pts per turnover
            fantasy_points += turnover_points
            print(f"  {defense_name} - Turnovers: {projected_turnovers:.1f} → {turnover_points:.1f} pts")

            # 4. Defensive TDs - rare but possible
            def_td_prob = 0.15  # ~15% chance of defensive TD
            def_td_points = def_td_prob * 6.0
            fantasy_points += def_td_points
            print(f"  {defense_name} - Def TDs: {def_td_prob:.2f} → {def_td_points:.1f} pts")

            defense_projections.append({
                'Player': defense_name,
                'Projection': round(fantasy_points, 2),
                'Confidence': 0.65,  # Moderate confidence for defense
                'Markets': 1,
                'Raw_Points': round(fantasy_points, 2),
                'Context_Multiplier': 1.0
            })

            print(f"  {defense_name} TOTAL: {fantasy_points:.2f} pts")

        return pd.DataFrame(defense_projections)

    def apply_team_context_to_player(self, player_name: str, base_points: float) -> float:
        """Apply team context from our ratings to individual player projections."""

        # Determine player's team based on common Jaguars/Bengals players
        jaguars_players = ['trevor lawrence', 'travis etienne', 'calvin ridley', 'christian kirk',
                          'evan engram', 'brian thomas', 'tank bigsby']
        bengals_players = ['joe burrow', 'joe mixon', 'ja\'marr chase', 'tee higgins',
                          'tyler boyd', 'chase brown', 'mike gesicki']

        player_lower = player_name.lower()

        if any(name in player_lower for name in jaguars_players):
            team = 'Jaguars'
        elif any(name in player_lower for name in bengals_players):
            team = 'Bengals'
        else:
            # Default neutral multiplier
            return 1.0

        # Get team edges we computed earlier
        jaguars_edges, bengals_edges = self.compute_team_edges()

        if team == 'Jaguars':
            edges = jaguars_edges
        else:
            edges = bengals_edges

        # Apply position-specific context
        multiplier = 1.0

        if 'prescott' in player_lower or 'jones' in player_lower:  # QBs
            # QB benefits from pass game edges and protection
            multiplier = 1.0 + (edges.edge_pass * 0.05) + (edges.edge_protection * 0.03)

        elif any(name in player_lower for name in ['lamb', 'nabers', 'robinson', 'tolbert', 'slayton']):  # WRs
            # WRs benefit from pass game and explosive play edges
            multiplier = 1.0 + (edges.edge_pass * 0.06) + (edges.edge_explosive * 0.04)

        elif any(name in player_lower for name in ['elliott', 'dowdle', 'singletary']):  # RBs
            # RBs benefit from rush game edges
            multiplier = 1.0 + (edges.edge_rush * 0.05)

        elif any(name in player_lower for name in ['ferguson', 'johnson']):  # TEs
            # TEs benefit from red zone edges
            multiplier = 1.0 + (edges.edge_rz * 0.04)

        # Cap multipliers to reasonable ranges
        return max(0.8, min(1.3, multiplier))

    def generate_projections(self) -> pd.DataFrame:
        """Main method to generate elite projections using proper methodology."""
        print("🚀 GENERATING ELITE BROWNS VS RAVENS PROJECTIONS")
        print("=" * 53)

        # Load data
        if not self.load_team_data():
            return pd.DataFrame()

        # Step 1: Fetch market data (props are the foundation)
        if not self.fetch_market_data():
            return pd.DataFrame()

        # Step 2: Compute team edges for context
        cowboys_edges, giants_edges = self.compute_team_edges()

        # Step 3: Analyze props with elite methodology
        props_analysis = self.analyze_props_like_elite_bettor()

        if not props_analysis:
            print("❌ No props analysis available - cannot generate elite projections")
            return pd.DataFrame()

        print(f"✅ Analyzed props for {len(props_analysis)} players")

        # Step 4: Create projections directly from props (the elite way)
        projections = self.create_projections_from_props(props_analysis)

        # Step 5: Add team defense projections
        defense_projections = self.create_defense_projections(props_analysis)

        # Combine offensive and defensive projections
        all_projections = pd.concat([projections, defense_projections], ignore_index=True)
        all_projections = all_projections.sort_values('Projection', ascending=False)

        print(f"✅ Generated elite projections for {len(projections)} players + {len(defense_projections)} defenses")

        return all_projections

    def save_projections(self, projections: pd.DataFrame, filename: str = None):
        """Save projections to CSV."""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M")
            filename = f"cowboys_giants_elite_projections_{timestamp}.csv"

        # Create the required 2-column format
        output_df = projections[['Player', 'Projection']].copy()

        # Save full projections with additional data
        full_filename = filename.replace('.csv', '_full.csv')
        projections.to_csv(full_filename, index=False)

        # Save simple 2-column format
        output_df.to_csv(filename, index=False)

        print(f"💾 Saved projections to {filename}")
        print(f"💾 Saved full data to {full_filename}")

        return filename


def main():
    """Run the elite projection system."""

    # Initialize system
    projector = CowboysGiantsEliteProjections()

    # Generate projections
    projections = projector.generate_projections()

    if len(projections) > 0:
        # Display top projections
        print("\n🏆 TOP 15 PROJECTIONS:")
        print("-" * 50)
        for i, (_, player) in enumerate(projections.head(15).iterrows(), 1):
            print(f"{i:2d}. {player['Player']:<25} {player['Projection']:>6.1f} pts (Conf: {player['Confidence']:.2f}, Markets: {player['Markets']})")

        # Save to file
        filename = projector.save_projections(projections)

        print(f"\n✅ ELITE PROJECTIONS COMPLETE")
        print(f"📁 Output: {filename}")

    else:
        print("❌ No projections generated")


if __name__ == "__main__":
    main()
