#!/usr/bin/env python3
"""Create team defense projections from individual defensive player props."""

import pandas as pd
import numpy as np
from pathlib import Path

# Defensive player to team mapping (based on 2025 rosters)
def_player_teams = {
    # Pass rushers and defensive players
    '<PERSON>': 'NY<PERSON>',
    '<PERSON><PERSON>': 'WA<PERSON>', 
    '<PERSON>': 'WA<PERSON>',
    '<PERSON>': 'BU<PERSON>',
    '<PERSON>': 'NY<PERSON>',
    '<PERSON><PERSON>': 'NYG',
    '<PERSON>': 'CH<PERSON>',
    '<PERSON><PERSON><PERSON>': 'NYJ',
    '<PERSON><PERSON>': 'WAS',
    '<PERSON>': 'CHI',
    '<PERSON>': 'DE<PERSON>',
    '<PERSON><PERSON><PERSON>': 'DE<PERSON>',
    '<PERSON>': 'DE<PERSON>',
    '<PERSON><PERSON>': 'T<PERSON>',
    '<PERSON>': 'DE<PERSON>',
    'Dre\'<PERSON>': 'SE<PERSON>',
    '<PERSON><PERSON>': 'T<PERSON>',
    '<PERSON>': 'DE<PERSON>',
    '<PERSON>': 'D<PERSON>',
    '<PERSON><PERSON>': '<PERSON>',
    '<PERSON><PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON>O<PERSON>',
    '<PERSON>': 'HO<PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>': '<PERSON><PERSON>',
    
    # Linebackers and defensive backs
    '<PERSON>': '<PERSON>',
    '<PERSON> <PERSON>ere<PERSON>': '<PERSON>G',
    '<PERSON> <PERSON><PERSON>n': '<PERSON><PERSON>',
    '<PERSON> M<PERSON><PERSON>': '<PERSON><PERSON>',
    'Je<PERSON>': '<PERSON><PERSON>',
    '<PERSON> <PERSON>': '<PERSON><PERSON>',
    '<PERSON> <PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON>-<PERSON>rit<PERSON>': '<PERSON><PERSON>',
    '<PERSON><PERSON> <PERSON>': '<PERSON><PERSON>',
    '<PERSON><PERSON>': '<PERSON><PERSON>',
    '<PERSON> Turner II': 'CIN',
    'Jamien Sherwood': 'NYJ',
    'Patrick Queen': 'PIT',
    'Quincy Williams': 'NYJ',
    'Tony Adams': 'NYJ',
    'Joey Porter Jr.': 'PIT',
    'Brandon Stephens': 'BAL',
    'Andre Cisco': 'JAC',
    'Darius Slay': 'PHI',
    'T.J. Watt': 'PIT',
    'Jalen Ramsey': 'MIA',
    'Demario Davis': 'NO',
    'Budda Baker': 'ARI',
    'Pete Werner': 'NO',
    'Mack Wilson': 'ARI',
    'Justin Reid': 'KC',
    'Jalen Thompson': 'ARI',
    'Alontae Taylor': 'NO',
    'Kool-Aid McKinstry': 'NO',
    'Kaden Elliss': 'TB',
    'Lavonte David': 'TB',
    'Antoine Winfield Jr.': 'TB',
    'Jessie Bates': 'ATL',
    'Jamel Dean': 'TB',
    'Zyon McCollum': 'TB',
    'A.J. Terrell': 'ATL',
    'Foyesade Oluokun': 'JAC',
    'Christian Rozeboom': 'LAR',
    'Trevon Moehrig': 'LV',
    'Devin Lloyd': 'JAC',
    'Tyson Campbell': 'JAC',
    'Eric Murray': 'HOU',
    'Jourdan Lewis': 'DAL',
    'Mike Jackson': 'SEA',
    'Jaycee Horn': 'CAR',
    'Derrick Brown': 'CAR',
    'Travon Walker': 'JAC',
    'Josh Hines-Allen': 'JAC',
    'Robert Spillane': 'LV',
    'Germaine Pratt': 'CIN',
    'Jeremy Chinn': 'WAS',
    'Isaiah Pola-Mao': 'LV',
    'Christian Elliss': 'NE',
    'Eric Stokes': 'GB',
    'Maxx Crosby': 'LV',
    'Adam Butler': 'MIA',
    'Alex Singleton': 'DEN',
    'Cody Barton': 'WAS',
    'Brandon Jones': 'DEN',
    'Amani Hooker': 'TEN',
    'Xavier Woods': 'CAR',
    'Jarvis Brownlee Jr.': 'NYG',
    'Talanoa Hufanga': 'SF',
    'L\'Jarius Sneed': 'TEN',
    'Ernest Jones': 'SEA',
    'Fred Warner': 'SF',
    'Devon Witherspoon': 'SEA',
    'Coby Bryant': 'SEA',
    'Julian Love': 'SEA',
    'Deommodore Lenoir': 'SF',
    'Jason Pinnock': 'NYG',
    'Renardo Green': 'SF',
    'Tariq Woolen': 'SEA',
    'Leonard Williams': 'SEA',
    'Nick Bosa': 'SF',
    'Jarran Reed': 'SEA',
    'DeMarcus Lawrence': 'DAL',
    'Quay Walker': 'GB',
    'Jack Campbell': 'DET',
    'Brian Branch': 'DET',
    'Alex Anzalone': 'DET',
    'Xavier McKinney': 'GB',
    'Keisean Nixon': 'GB',
    'Kerby Joseph': 'DET',
    'Terrion Arnold': 'DET',
    'D.J. Reed Jr.': 'NYJ',
    'Carrington Valentine': 'GB',
    'Nate Landman': 'ATL',
    'Azeez Al-Shaair': 'HOU',
    'Quentin Lake': 'LAR',
    'Henry To\'o To\'o': 'TEN',
    'Kamren Curl': 'LAR',
    'Chauncey Gardner-Johnson': 'PHI',
}

def create_team_defense_projections():
    """Create team defense projections from individual defensive player props."""
    
    # Load player props
    df = pd.read_parquet('data/player_props.parquet')
    
    # Filter for defensive markets
    def_df = df[df['market'].isin(['sacks', 'tackles'])].copy()
    
    # Map players to teams
    def_df['team'] = def_df['player_name'].map(def_player_teams)
    
    # Remove players we couldn't map
    def_df = def_df.dropna(subset=['team'])
    
    print(f"Mapped {len(def_df)} defensive props to teams")
    print(f"Teams with defensive props: {sorted(def_df['team'].unique())}")
    
    # Create team defense projections
    team_def_projections = []
    
    for team in def_df['team'].unique():
        team_players = def_df[def_df['team'] == team]
        
        # Calculate team sacks projection
        sacks_players = team_players[team_players['market'] == 'sacks']
        team_sacks = sacks_players['line'].sum() if len(sacks_players) > 0 else 0
        
        # Calculate team tackles projection  
        tackles_players = team_players[team_players['market'] == 'tackles']
        team_tackles = tackles_players['line'].sum() if len(tackles_players) > 0 else 0
        
        # Simple team defense scoring formula
        # Base points + sacks*2 + tackles*0.5 + some variance
        base_points = 8  # Base defense points
        sack_points = team_sacks * 2
        tackle_points = team_tackles * 0.5
        
        # Add some randomness/variance based on team strength
        variance = np.random.normal(0, 2)  # Small random component
        
        team_def_score = base_points + sack_points + tackle_points + variance
        
        team_def_projections.append({
            'Player': f"{team} Defense",
            'Projection': round(team_def_score, 2),
            'team': team,
            'sacks_proj': round(team_sacks, 2),
            'tackles_proj': round(team_tackles, 1),
            'player_count': len(team_players)
        })
    
    # Convert to DataFrame
    team_def_df = pd.DataFrame(team_def_projections)
    
    # Sort by projection descending
    team_def_df = team_def_df.sort_values('Projection', ascending=False)
    
    print(f"\nCreated {len(team_def_df)} team defense projections")
    print("\nTop 10 team defenses:")
    print(team_def_df[['Player', 'Projection', 'sacks_proj', 'tackles_proj']].head(10).to_string(index=False))
    
    # Save team defense projections
    fc_format = team_def_df[['Player', 'Projection']].copy()
    
    # Load existing projections and append team defenses
    existing_df = pd.read_csv('data/fc_week1.csv')
    
    # Remove any existing team defenses (in case we're re-running)
    existing_df = existing_df[~existing_df['Player'].str.contains('Defense|DST', na=False)]
    
    # Combine with team defenses
    combined_df = pd.concat([existing_df, fc_format], ignore_index=True)
    
    # Save updated file
    combined_df.to_csv('data/fc_week1_with_defenses.csv', index=False)
    
    print(f"\nSaved combined projections with {len(combined_df)} total players")
    print("File saved as: data/fc_week1_with_defenses.csv")
    
    return team_def_df

if __name__ == "__main__":
    team_def_df = create_team_defense_projections()
