#!/usr/bin/env python3
"""
🧪 Test the Gamebook Intelligence Engine
Test the per-gamebook parsing with strict validation gates
"""

from per_gamebook_parser import GamebookIntelligenceEngine

def main():
    """Test the Gamebook Intelligence Engine."""

    print("🧠 TESTING GAMEBOOK INTELLIGENCE ENGINE")
    print("=" * 50)

    # Initialize the engine
    engine = GamebookIntelligenceEngine()

    # Test with just the Vikings vs Bears file first
    print("🧪 Testing with Vikings vs Bears first...")
    test_file = "Vikings vs Bears.md"

    game_data = engine._process_single_gamebook(test_file)

    if game_data.validation_passed:
        print("✅ Vikings vs Bears passed validation!")
        engine.clean_games.append(game_data)
    else:
        print("❌ Vikings vs Bears failed validation")
        engine.failed_games.append(game_data)
        engine._print_validation_errors(game_data)

    print("\n🏁 Test completed!")

if __name__ == "__main__":
    main()
