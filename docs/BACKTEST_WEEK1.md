# Week 1 Backtest System

The Week 1 backtest system provides gentle, confidence-weighted nudges to projections based on historical Week 1 residuals from 2024. This addresses the unique chaos and unpredictability of NFL opening week.

## Philosophy

- **Gentle nudges only**: Maximum adjustments are capped at ±0.6 points
- **Confidence-weighted**: Low-confidence projections get smaller nudges
- **Position-aware**: Different adjustments for QB, RB, WR, TE, DST
- **Archetype-aware**: Separate adjustments for WR_alpha vs WR_depth, RB_feature vs RB_committee
- **Never override**: Nudges supplement, never replace, core projection logic

## Running a Backtest

### Step 1: Generate Adjusters from Historical Data

```bash
python -m src.proj.cli backtest-2024w1 \
  --csv csvs/draftkings_NFL_2024-week-1_players.csv \
  --out data/backtest_2024w1.csv \
  --report data/backtest_2024w1.txt \
  --emit-adjusters data/adjusters_2024w1.json
```

This will:
- Analyze projection vs actual performance from 2024 Week 1
- Generate position and archetype-specific adjustment parameters
- Create a detailed report with top smashes and busts
- Save adjustment parameters to JSON for use in projections

### Step 2: Apply Nudges to New Projections (Optional)

```bash
python -m src.proj.cli project \
  --canonical data/week1_canonical.parquet \
  --out data/week1_projections.csv \
  --use-2024w1-residuals \
  --adjusters data/adjusters_2024w1.json
```

## Key Findings from 2024 Week 1

Based on the backtest analysis:

### Global Performance
- **Mean Bias**: -1.09 points (projections were slightly optimistic)
- **MAE**: 3.17 points
- **RMSE**: 4.62 points  
- **Correlation**: 0.695

### Position-Specific Biases
- **WR**: -1.56 points (most under-projected)
- **QB**: -1.27 points
- **TE**: -1.34 points
- **RB**: -0.42 points (least biased)
- **DST**: +0.48 points (slightly over-projected)

### Archetype Insights
- **WR_alpha**: Severely under-projected (-4.18 points bias)
- **RB_feature**: Under-projected (-2.09 points bias)
- **WR_depth**: Slightly under-projected (-0.42 points bias)
- **RB_committee**: Over-projected (+0.68 points bias)

## Adjustment Parameters

The system applies these gentle nudges:

### Mu Adjustments (Point Nudges)
- Capped at ±0.6 points maximum
- Scaled by 35% of historical bias (shrinkage factor)
- Applied before market blending

### Sigma Adjustments (Uncertainty)
- Maximum 10% increase in uncertainty bands
- 3% increase per point of absolute historical bias
- Reflects increased Week 1 unpredictability

## Configuration

Environment variables (optional):
```bash
APPLY_2024_W1_NUDGES=false          # Enable/disable nudges
NUDGE_LAMBDA=0.35                   # Shrinkage factor
NUDGE_MU_CAP=0.6                    # Max point adjustment
NUDGE_SIGMA_SCALE=0.03              # Sigma scaling factor
NUDGE_SIGMA_CAP=1.10                # Max sigma multiplier
```

## Integration with Projection Pipeline

When enabled, nudges are applied in this order:
1. **Base projections** (priors + market blend)
2. **Week 1 nudges** (gentle historical adjustments)
3. **Psychology bias** (if applicable)
4. **Final market devig** (if applicable)

This ensures nudges supplement rather than override the core projection logic.

## Guardrails

- **Confidence scaling**: Low-confidence players get proportionally smaller nudges
- **Sample size filtering**: Groups with <3 observations are ignored
- **Caps enforced**: No adjustment exceeds configured maximums
- **Logging**: All applied nudges are logged for transparency
- **Opt-in only**: Nudges must be explicitly enabled

## Example Output

```
Josh Allen (QB): mu_adj=-0.445, sigma_mult=1.038
Tyreek Hill (WR_alpha): mu_adj=-0.600, sigma_mult=1.100  
Derrick Henry (RB_feature): mu_adj=-0.600, sigma_mult=1.063
```

This shows Josh Allen gets a small downward nudge based on QB bias, while WR_alpha and RB_feature players get larger adjustments due to their historical under-projection patterns.
