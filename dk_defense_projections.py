#!/usr/bin/env python3
"""
DraftKings Defense Projections for WAS @ GB
Uses individual defensive player props + game script to project team defense scoring.
"""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple


class DKDefenseProjector:
    """Project team defenses using DraftKings scoring system."""
    
    def __init__(self):
        # DraftKings Defense Scoring
        self.dk_scoring = {
            'sack': 1.0,
            'interception': 2.0,
            'fumble_recovery': 2.0,
            'defensive_td': 6.0,
            'safety': 2.0,
            'blocked_kick': 2.0,
            # Points allowed scoring
            'points_0': 10.0,
            'points_1_6': 7.0,
            'points_7_13': 4.0,
            'points_14_20': 1.0,
            'points_21_27': 0.0,
            'points_28_34': -1.0,
            'points_35_plus': -4.0,
            # Yards allowed (bonus thresholds)
            'yards_under_100': 10.0,
            'yards_100_199': 5.0,
            'yards_200_299': 2.0,
            'yards_300_399': 0.0,
            'yards_400_499': -1.0,
            'yards_500_plus': -3.0
        }
        
        self.game_context = {
            'total': 49.0,
            'spread': -3.5,  # GB favored
            'gb_implied_total': 26.25,
            'was_implied_total': 22.75
        }
    
    def load_data(self) -> Dict[str, pd.DataFrame]:
        """Load all required data."""
        data = {}
        
        try:
            # DraftKings data
            data['dk'] = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-2_players.csv', skiprows=1)
            
            # Individual defensive props
            data['props'] = pd.read_csv('csvs/was_gb_player_props_20250911_2151.csv')
            
            print(f"✅ Loaded DK data: {len(data['dk'])} players")
            print(f"✅ Loaded props: {len(data['props'])} individual props")
            
            return data
            
        except Exception as e:
            print(f"❌ Error loading data: {e}")
            return {}
    
    def get_defensive_players_by_team(self, props_df: pd.DataFrame) -> Dict[str, List[str]]:
        """Map defensive players to teams."""
        # Get all defensive props
        def_props = props_df[props_df['market'].isin(['player_sacks', 'player_tackles_assists', 'player_defensive_interceptions'])]
        
        # Manual mapping based on known players (could be enhanced with team rosters)
        was_defenders = [
            'Bobby Wagner', 'Frankie Luvu', 'Mike Sainristil', 'Xavier McKinney', 
            'Marshon Lattimore', 'Jartavius Martin', 'Will Harris', 'Micah Parsons',
            'Dorance Armstrong', 'Von Miller', 'Daron Payne', 'Javon Kinlaw'
        ]
        
        gb_defenders = [
            'Quay Walker', 'Rashan Gary', 'Keisean Nixon', 'Carrington Valentine', 
            'Edgerrin Cooper', 'Devonte Wyatt', 'Lukas Van Ness', 'Karl Brooks',
            'Evan Williams'
        ]
        
        team_players = {'WAS': [], 'GB': []}
        
        for _, prop in def_props.iterrows():
            player = prop['player_name']
            
            # Check which team this player belongs to
            if any(defender in player for defender in was_defenders):
                if player not in team_players['WAS']:
                    team_players['WAS'].append(player)
            elif any(defender in player for defender in gb_defenders):
                if player not in team_players['GB']:
                    team_players['GB'].append(player)
        
        return team_players
    
    def project_team_sacks(self, team: str, props_df: pd.DataFrame, team_players: Dict[str, List[str]]) -> float:
        """Project team sacks from individual player props."""
        team_sacks = 0.0
        
        sack_props = props_df[props_df['market'] == 'player_sacks']
        
        for _, prop in sack_props.iterrows():
            player = prop['player_name']
            line = prop['line']
            
            # Check if this player is on our team
            if player in team_players.get(team, []):
                # Use the line as expected sacks (conservative approach)
                team_sacks += line
        
        # Add baseline for unlisted players (typically 0.5-1.0 sacks per game from role players)
        baseline_sacks = 0.5
        team_sacks += baseline_sacks
        
        return team_sacks
    
    def project_team_interceptions(self, team: str, props_df: pd.DataFrame, team_players: Dict[str, List[str]]) -> float:
        """Project team interceptions."""
        team_ints = 0.0
        
        int_props = props_df[props_df['market'] == 'player_defensive_interceptions']
        
        for _, prop in int_props.iterrows():
            player = prop['player_name']
            line = prop['line']
            
            if player in team_players.get(team, []):
                team_ints += line
        
        # Baseline for team (average ~1 INT per game league-wide)
        if team_ints == 0:
            team_ints = 0.8  # Conservative baseline
        
        return team_ints
    
    def project_points_allowed(self, defending_team: str) -> Tuple[float, Dict[str, float]]:
        """Project points allowed and scoring distribution."""
        if defending_team == 'WAS':
            # WAS defense vs GB offense
            opponent_total = self.game_context['gb_implied_total']
            opponent_strength = 'above_average'  # GB offense
        else:
            # GB defense vs WAS offense  
            opponent_total = self.game_context['was_implied_total']
            opponent_strength = 'average'  # WAS offense
        
        # Points allowed distribution based on implied total
        points_probs = {}
        
        if opponent_total <= 17:
            points_probs = {
                'points_0': 0.05, 'points_1_6': 0.15, 'points_7_13': 0.35,
                'points_14_20': 0.30, 'points_21_27': 0.10, 'points_28_34': 0.04,
                'points_35_plus': 0.01
            }
        elif opponent_total <= 21:
            points_probs = {
                'points_0': 0.02, 'points_1_6': 0.08, 'points_7_13': 0.25,
                'points_14_20': 0.40, 'points_21_27': 0.20, 'points_28_34': 0.04,
                'points_35_plus': 0.01
            }
        elif opponent_total <= 24:
            points_probs = {
                'points_0': 0.01, 'points_1_6': 0.04, 'points_7_13': 0.15,
                'points_14_20': 0.35, 'points_21_27': 0.30, 'points_28_34': 0.12,
                'points_35_plus': 0.03
            }
        else:  # High scoring
            points_probs = {
                'points_0': 0.005, 'points_1_6': 0.02, 'points_7_13': 0.08,
                'points_14_20': 0.20, 'points_21_27': 0.35, 'points_28_34': 0.25,
                'points_35_plus': 0.095
            }
        
        return opponent_total, points_probs
    
    def project_yards_allowed(self, defending_team: str, opponent_total: float) -> Dict[str, float]:
        """Project yards allowed distribution."""
        # Rough correlation: higher scoring = more yards
        if opponent_total <= 17:
            yards_probs = {
                'yards_under_100': 0.02, 'yards_100_199': 0.08, 'yards_200_299': 0.35,
                'yards_300_399': 0.40, 'yards_400_499': 0.12, 'yards_500_plus': 0.03
            }
        elif opponent_total <= 24:
            yards_probs = {
                'yards_under_100': 0.01, 'yards_100_199': 0.04, 'yards_200_299': 0.20,
                'yards_300_399': 0.45, 'yards_400_499': 0.25, 'yards_500_plus': 0.05
            }
        else:
            yards_probs = {
                'yards_under_100': 0.005, 'yards_100_199': 0.02, 'yards_200_299': 0.10,
                'yards_300_399': 0.35, 'yards_400_499': 0.40, 'yards_500_plus': 0.125
            }
        
        return yards_probs
    
    def calculate_defense_projection(self, team: str, props_df: pd.DataFrame, team_players: Dict[str, List[str]]) -> Dict[str, Any]:
        """Calculate complete defense projection."""
        
        # Individual stats
        sacks = self.project_team_sacks(team, props_df, team_players)
        interceptions = self.project_team_interceptions(team, props_df, team_players)
        
        # Estimate other stats (no props available)
        fumble_recoveries = 0.6  # League average
        defensive_tds = 0.15  # Rare but possible
        safeties = 0.05  # Very rare
        blocked_kicks = 0.1  # Occasional
        
        # Points and yards allowed
        opponent_total, points_probs = self.project_points_allowed(team)
        yards_probs = self.project_yards_allowed(team, opponent_total)
        
        # Calculate expected fantasy points
        fantasy_points = 0.0
        
        # Individual stats
        fantasy_points += sacks * self.dk_scoring['sack']
        fantasy_points += interceptions * self.dk_scoring['interception']
        fantasy_points += fumble_recoveries * self.dk_scoring['fumble_recovery']
        fantasy_points += defensive_tds * self.dk_scoring['defensive_td']
        fantasy_points += safeties * self.dk_scoring['safety']
        fantasy_points += blocked_kicks * self.dk_scoring['blocked_kick']
        
        # Points allowed (expected value)
        for outcome, prob in points_probs.items():
            fantasy_points += prob * self.dk_scoring[outcome]
        
        # Yards allowed (expected value)
        for outcome, prob in yards_probs.items():
            fantasy_points += prob * self.dk_scoring[outcome]
        
        return {
            'team': team,
            'fantasy_points': round(fantasy_points, 2),
            'sacks': round(sacks, 2),
            'interceptions': round(interceptions, 2),
            'fumble_recoveries': round(fumble_recoveries, 2),
            'defensive_tds': round(defensive_tds, 2),
            'opponent_total': opponent_total,
            'points_probs': points_probs,
            'yards_probs': yards_probs,
            'confidence': 0.65  # Medium confidence due to volatility
        }
    
    def create_defense_projections(self) -> List[Dict[str, Any]]:
        """Create projections for both team defenses."""
        print("=== DRAFTKINGS DEFENSE PROJECTIONS ===")
        
        data = self.load_data()
        if not data:
            return []
        
        # Map defensive players to teams
        team_players = self.get_defensive_players_by_team(data['props'])
        
        print(f"📊 Defensive Players Mapped:")
        for team, players in team_players.items():
            print(f"   {team}: {len(players)} players ({', '.join(players[:3])}...)")
        
        # Create projections for both teams
        projections = []
        
        for team in ['WAS', 'GB']:
            projection = self.calculate_defense_projection(team, data['props'], team_players)
            projections.append(projection)
        
        # Sort by projection
        projections.sort(key=lambda x: x['fantasy_points'], reverse=True)
        
        return projections
    
    def save_defense_projections(self, projections: List[Dict[str, Any]]) -> str:
        """Save defense projections."""
        # Create detailed breakdown
        detailed_data = []
        
        for proj in projections:
            team_name = "Packers" if proj['team'] == 'GB' else "Commanders"
            
            detailed_data.append({
                'team': f"{team_name} DST",
                'fantasy_projection': proj['fantasy_points'],
                'sacks': proj['sacks'],
                'interceptions': proj['interceptions'],
                'fumble_recoveries': proj['fumble_recoveries'],
                'defensive_tds': proj['defensive_tds'],
                'opponent_implied_total': proj['opponent_total'],
                'confidence': proj['confidence']
            })
        
        df = pd.DataFrame(detailed_data)
        filename = f"csvs/was_gb_defense_projections_{pd.Timestamp.now().strftime('%Y%m%d_%H%M')}.csv"
        df.to_csv(filename, index=False)
        
        print(f"💾 Defense projections saved to: {filename}")
        return filename


def main():
    """Main execution."""
    projector = DKDefenseProjector()
    
    projections = projector.create_defense_projections()
    
    if not projections:
        print("❌ Could not create defense projections")
        return
    
    # Display results
    print(f"\n🛡️  DEFENSE PROJECTIONS:")
    print(f"{'Team':<15} {'Projection':<10} {'Sacks':<6} {'INTs':<6} {'Opp Total':<10}")
    print("-" * 55)
    
    for proj in projections:
        team_name = "Packers DST" if proj['team'] == 'GB' else "Commanders DST"
        print(f"{team_name:<15} {proj['fantasy_points']:<10.1f} {proj['sacks']:<6.1f} "
              f"{proj['interceptions']:<6.1f} {proj['opponent_total']:<10.1f}")
    
    # Save projections
    projector.save_defense_projections(projections)
    
    return projections


if __name__ == "__main__":
    main()
