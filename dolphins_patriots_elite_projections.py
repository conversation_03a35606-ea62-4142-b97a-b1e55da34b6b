#!/usr/bin/env python3
"""
Elite DraftKings Fantasy Projections: Miami Dolphins vs New England Patriots
- Comprehensive prop-driven methodology
- Team rankings + holes/levers adjustments
- Includes QB passing TDs, all yardage categories, receptions, and team D/ST
Output: CSV with columns: Player,Projection (sorted desc)
"""

import os
import sys
import re
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append('src')

from proj.fetch_odds import (
    get_totals_spreads,
    find_game_event_id,
    get_player_props,
    process_player_props_response,
)
from proj.sleeper import depth_charts_for_teams

load_dotenv()


# ----------------------------- Data classes -----------------------------
@dataclass
class GameContext:
    home_team: str = "Patriots"
    away_team: str = "Dolphins"
    spread: float = 0.0
    total: float = 0.0
    home_implied_total: float = 0.0
    away_implied_total: float = 0.0
    market_strength: float = 0.0
    market_uncertainty: float = 0.0
    event_id: Optional[str] = None


@dataclass
class TeamEdges:
    edge_pass: float = 0.0
    edge_rush: float = 0.0
    edge_rz: float = 0.0
    edge_explosive: float = 0.0
    edge_protection: float = 0.0
    edge_penalty: float = 0.0
    edge_plays: float = 0.0


# ----------------------------- Main class -----------------------------
class DolphinsPatriotsEliteProjections:
    """Elite projection system for Dolphins vs Patriots."""

    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        self.game = GameContext()

        # DraftKings scoring (PPR)
        self.dk = {
            'pass_yards': 0.04,
            'pass_tds': 4.0,
            'pass_interceptions': -1.0,
            'rush_yards': 0.1,
            'rush_tds': 6.0,
            'rec_yards': 0.1,
            'receptions': 1.0,
            'rec_tds': 6.0,
            # D/ST
            'dst_sack': 1.0,
            'dst_int': 2.0,
            'dst_fum_rec': 2.0,
            'dst_td': 6.0,
        }

        # Loadable data
        self.team_ratings = None
        self.holes_levers = None
        self.props_df: Optional[pd.DataFrame] = None

        # Team player name lists for context mapping
        self.dolphins_players = [
            'tua', 'tagovailoa', 'tyreek', 'hill', 'waddle', 'jaylen',
            'devon', 'achane', 'raheem', 'mostert', 'jaylen waddle', 'durham smythe',
            'jonnu smith', 'odell', 'beckham', 'odell beckham', 'malik washington'
        ]
        self.patriots_players = [
            'drake maye', 'maye', 'rhamondre', 'stevenson', 'antonio gibson', 'gibson',
            'demario douglas', 'douglas', 'ja\'lynn polk', 'polk', 'kj osborn', 'osborn',
            'hunter henry', 'henry', 'jaheim bell', 'bell', 'juju smith-schuster', 'juju'
        ]

    # ----------------------------- Data loading -----------------------------
    def load_team_data(self) -> bool:
        try:
            self.team_ratings = pd.read_parquet('models/team_unit_ratings.parquet')
            self.holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
            return True
        except Exception as e:
            print(f"Error loading team models: {e}")
            return False

    # ----------------------------- Market ingestion -----------------------------
    def fetch_market_data(self) -> bool:
        """Read odds for spread/total and fetch 20+ player prop markets for the game."""
        try:
            if not self.api_key:
                print("No ODDS_API_KEY set. Cannot fetch live markets.")
                return False

            # Get event id for Dolphins vs Patriots (order-agnostic)
            event_id = find_game_event_id(self.api_key, home_team="New England Patriots", away_team="Miami Dolphins")
            if not event_id:
                print("Patriots vs Dolphins not found on The Odds API schedule.")
                return False

            # Also pull an overall odds snapshot for implied totals
            odds_games = get_totals_spreads(self.api_key)
            # Pick matching game to derive spread/total if present
            spread, total = None, None
            home_name, away_name = None, None
            for g in odds_games:
                teams = {g.get('home_team'), g.get('away_team')}
                if teams == {"New England Patriots", "Miami Dolphins"}:
                    home_name = g.get('home_team')
                    away_name = g.get('away_team')
                    # Use first bookmaker for simplicity
                    bk = (g.get('bookmakers') or [{}])[0]
                    sp, to = None, None
                    for m in bk.get('markets', []):
                        if m.get('key') == 'spreads':
                            for o in m.get('outcomes', []):
                                if o.get('name') == home_name:
                                    sp = o.get('point')
                        if m.get('key') == 'totals':
                            for o in m.get('outcomes', []):
                                if o.get('name') == 'Over':
                                    to = o.get('point')
                    spread, total = sp, to
                    break

            # Compute implied team totals
            if spread is not None and total is not None:
                home_it = total / 2 + spread / 2
                away_it = total / 2 - spread / 2
            else:
                # Fallback conservative numbers if book snapshot path fails
                # Keep real event teams if we found them
                home_it, away_it, spread, total = 21.5, 24.5, (21.5 - 24.5), (21.5 + 24.5)
                if home_name is None:
                    home_name, away_name = "New England Patriots", "Miami Dolphins"

            # Normalize to short names for labeling
            def short(n):
                return 'Dolphins' if 'dolphins' in n.lower() else ('Patriots' if 'patriots' in n.lower() else n)

            self.game = GameContext(
                home_team=short(home_name or 'Patriots'),
                away_team=short(away_name or 'Dolphins'),
                spread=float(spread),
                total=float(total),
                home_implied_total=float(home_it),
                away_implied_total=float(away_it),
                market_strength=0.0,
                market_uncertainty=0.0,
                event_id=event_id,
            )

            # Comprehensive markets (20+)
            markets = [
                'player_pass_yds', 'player_pass_tds', 'player_pass_interceptions', 'player_pass_attempts',
                'player_rush_yds', 'player_rush_tds', 'player_rush_attempts',
                'player_receptions', 'player_reception_yds', 'player_reception_tds',
                'player_rush_reception_yds', 'player_kicking_points',
                'player_sacks', 'player_tackles_assists', 'player_defensive_interceptions',
                # alternates to strengthen market read
                'player_pass_yds_alternate', 'player_rush_yds_alternate', 'player_reception_yds_alternate',
                'player_receptions_alternate', 'player_pass_tds_alternate', 'player_rush_tds_alternate',
            ]

            raw = get_player_props(self.api_key, event_id=self.game.event_id, markets=markets)
            self.props_df = process_player_props_response(raw)
            return True
        except Exception as e:
            print(f"Failed to fetch market data: {e}")
            return False

    # ----------------------------- Team edges -----------------------------
    def _team_row(self, team: str) -> Tuple[Optional[pd.Series], Optional[pd.Series]]:
        if self.team_ratings is None or self.holes_levers is None:
            return None, None
        # Use latest week available
        tr = self.team_ratings
        hl = self.holes_levers
        # Pick last as_of_week per team
        w_tr = tr.loc[tr['team'] == team]
        w_hl = hl.loc[hl['team'] == team]
        if w_tr.empty or w_hl.empty:
            return None, None
        tr_row = w_tr.sort_values('as_of_week').iloc[-1]
        hl_row = w_hl.sort_values('as_of_week').iloc[-1]
        return tr_row, hl_row

    def compute_team_edges(self) -> Tuple[TeamEdges, TeamEdges]:
        """Build matchup edges Dolphins (away) vs Patriots (home)."""
        d_tr, d_hl = self._team_row('Dolphins')
        p_tr, p_hl = self._team_row('Patriots')
        if d_tr is None or p_tr is None or d_hl is None or p_hl is None:
            return TeamEdges(), TeamEdges()
        dolphins = TeamEdges(
            edge_pass=float(d_hl.get('lever_explosive_pass', 0) - p_hl.get('hole_pass_eff', 0)),
            edge_rush=float(d_tr.get('OFF_rush_ypa_rating', 0) - p_tr.get('DEF_rush_ypa_allowed_rating', 0)),
            edge_rz=float(d_hl.get('lever_rz', 0) - p_hl.get('hole_rz', 0)),
            edge_explosive=float(d_hl.get('lever_explosive_overall', 0) - p_hl.get('hole_explosive_pass', 0)),
            edge_protection=float(d_hl.get('lever_protection', 0) - p_hl.get('hole_pressure', 0)),
            edge_penalty=float(d_hl.get('lever_penalty_discipline', 0) - p_hl.get('hole_penalty_fp', 0)),
            edge_plays=float(d_tr.get('OFF_ppd_rating', 0) - p_tr.get('DEF_ppd_allowed_rating', 0)),
        )
        patriots = TeamEdges(
            edge_pass=float(p_hl.get('lever_explosive_pass', 0) - d_hl.get('hole_pass_eff', 0)),
            edge_rush=float(p_tr.get('OFF_rush_ypa_rating', 0) - d_tr.get('DEF_rush_ypa_allowed_rating', 0)),
            edge_rz=float(p_hl.get('lever_rz', 0) - d_hl.get('hole_rz', 0)),
            edge_explosive=float(p_hl.get('lever_explosive_overall', 0) - d_hl.get('hole_explosive_pass', 0)),
            edge_protection=float(p_hl.get('lever_protection', 0) - d_hl.get('hole_pressure', 0)),
            edge_penalty=float(p_hl.get('lever_penalty_discipline', 0) - d_hl.get('hole_penalty_fp', 0)),
            edge_plays=float(p_tr.get('OFF_ppd_rating', 0) - d_tr.get('DEF_ppd_allowed_rating', 0)),
        )
        return dolphins, patriots

    # ----------------------------- Elite prop reading -----------------------------
    @staticmethod
    def american_to_prob(odds: int) -> float:
        if odds > 0:
            return 100 / (odds + 100)
        return abs(odds) / (abs(odds) + 100)

    def _implied_mean(self, line: float, true_over_prob: float, market: str, sharp_line: float) -> float:
        # Base from line, nudge by market probability and sharp divergence
        x = line
        if any(k in market for k in ['pass_yds', 'rush_yds', 'rec', 'reception_yds']):
            if true_over_prob > 0.55:
                x *= 1.05
            elif true_over_prob < 0.45:
                x *= 0.95
        elif any(k in market for k in ['receptions', 'completions']):
            if true_over_prob > 0.55:
                x *= 1.03
            elif true_over_prob < 0.45:
                x *= 0.97
        elif 'tds' in market:
            if true_over_prob > 0.60:
                x *= 1.10
            elif true_over_prob < 0.40:
                x *= 0.90
        # Blend sharp line if it diverges
        if abs(sharp_line - line) > 0.5:
            x = 0.7 * x + 0.3 * sharp_line
        return x

    def analyze_props(self) -> Dict[str, Any]:
        if self.props_df is None or self.props_df.empty:
            return {}
        props_analysis: Dict[str, Any] = {}
        for _, r in self.props_df.iterrows():
            player = r['player_name']
            market = r['market']
            line = r['line']
            over_odds = r['over_odds']
            under_odds = r['under_odds']
            book = r['bookmaker']
            d = props_analysis.setdefault(player, {}).setdefault(market, {
                'lines': [], 'over_odds': [], 'under_odds': [], 'books': []
            })
            d['lines'].append(line)
            d['over_odds'].append(over_odds)
            d['under_odds'].append(under_odds)
            d['books'].append(book)
        # compute aggregates
        sharp_books = ['pinnacle', 'circa', 'bookmaker']
        for player, markets in props_analysis.items():
            for mkt, d in markets.items():
                lines = d['lines']
                if not lines:
                    continue
                books = d['books']
                sharp_lines = [lines[i] for i, b in enumerate(books) if any(s in (b or '').lower() for s in sharp_books)]
                consensus = float(np.mean(lines))
                sharp_line = float(np.mean(sharp_lines)) if sharp_lines else consensus
                over_probs = [self.american_to_prob(o) for o in d['over_odds']]
                under_probs = [self.american_to_prob(o) for o in d['under_odds']]
                total = (np.mean(over_probs) + np.mean(under_probs)) or 1.0
                true_over = float(np.mean(over_probs) / total)
                implied = self._implied_mean(consensus, true_over, mkt, sharp_line)
                market_strength = min(1.0, len(lines) / 8.0) * (1.0 - min(1.0, np.var(lines)))
                confidence = 0.5 * market_strength + 0.5 * (1.0 - min(1.0, np.var(lines) / max(consensus, 1)))
                d.update({
                    'consensus_line': consensus,
                    'sharp_line': sharp_line,
                    'implied_total': implied,
                    'market_strength': market_strength,
                    'confidence': float(confidence),
                })
        return props_analysis

    # ----------------------------- Build player projections -----------------------------
    def _player_team_multiplier(self, player_name: str) -> float:
        name = player_name.lower()
        team = 'Dolphins' if any(s in name for s in self.dolphins_players) else (
            'Patriots' if any(s in name for s in self.patriots_players) else None
        )
        if team is None:
            return 1.0
        d_edges, p_edges = self.compute_team_edges()
        edges = d_edges if team == 'Dolphins' else p_edges
        # position heuristics by name
        mult = 1.0
        if any(k in name for k in ['tua', 'maye', 'prescott', 'jones']):  # QB
            mult += 0.05 * edges.edge_pass + 0.03 * edges.edge_protection
        if any(k in name for k in ['hill', 'waddle', 'polk', 'osborn', 'douglas']):  # WR
            mult += 0.06 * edges.edge_pass + 0.04 * edges.edge_explosive
        if any(k in name for k in ['achane', 'mostert', 'stevenson', 'gibson']):  # RB
            mult += 0.05 * edges.edge_rush
        if any(k in name for k in ['smythe', 'henry', 'jonnu']):  # TE
            mult += 0.04 * edges.edge_rz
        return float(max(0.8, min(1.3, mult)))

    def projections_from_props(self, props_analysis: Dict[str, Any]) -> pd.DataFrame:
        out = []
        for player, markets in props_analysis.items():
            # Collect implied means per category to avoid double-counting
            pass_yds = pass_tds = pass_int = 0.0
            rush_yds = rec_yds = rush_rec_yds = 0.0
            receptions = rush_tds = rec_tds = 0.0
            kicking_pts = 0.0

            for mkt, d in markets.items():
                x = float(d.get('implied_total', 0.0))
                m = mkt.lower()
                if 'pass_yds' in m:
                    pass_yds = max(pass_yds, x)
                elif 'pass_tds' in m:
                    pass_tds = max(pass_tds, x)
                elif 'pass_interceptions' in m:
                    pass_int = max(pass_int, x)
                elif 'rush_reception_yds' in m or 'pass_rush_reception_yds' in m:
                    rush_rec_yds = max(rush_rec_yds, x)
                elif 'rush_yds' in m:
                    rush_yds = max(rush_yds, x)
                elif 'reception_yds' in m or 'rec_yds' in m or 'receiving_yds' in m:
                    rec_yds = max(rec_yds, x)
                elif 'receptions' in m:
                    receptions = max(receptions, x)
                elif 'rush_tds' in m or 'rushing_touchdowns' in m:
                    rush_tds = max(rush_tds, x)
                elif 'reception_tds' in m or 'rec_tds' in m or 'receiving_touchdowns' in m:
                    rec_tds = max(rec_tds, x)
                elif 'kicking_points' in m:
                    # Kickers: direct DK points (already in DK points units)
                    kicking_pts = max(kicking_pts, x)

            # DK scoring with proper de-duplication of yards
            yard_pts = (rush_rec_yds * self.dk['rec_yards']) if rush_rec_yds > 0 else (
                rush_yds * self.dk['rush_yards'] + rec_yds * self.dk['rec_yards']
            )
            # sum direct points
            pts = (
                pass_yds * self.dk['pass_yards'] +
                pass_tds * self.dk['pass_tds'] +
                pass_int * self.dk['pass_interceptions'] +
                yard_pts +
                receptions * self.dk['receptions'] +
                rush_tds * self.dk['rush_tds'] +
                rec_tds * self.dk['rec_tds'] +
                kicking_pts
            )

            mult = self._player_team_multiplier(player)
            final_pts = pts * mult
            out.append({'Player': player, 'Projection': round(final_pts, 2)})
        df = pd.DataFrame(out)
        return df.sort_values('Projection', ascending=False)

    # ----------------------------- D/ST projections -----------------------------
    def defense_projections(self, props_analysis: Dict[str, Any]) -> pd.DataFrame:
        d_edges, p_edges = self.compute_team_edges()
        # Determine opponent implied totals dynamically based on actual home/away
        def opp_implied_for(team_short: str) -> float:
            if team_short == 'Dolphins':
                # Opponent is Patriots
                if self.game.home_team == 'Patriots':
                    return self.game.home_implied_total
                else:
                    return self.game.away_implied_total
            else:
                # Opponent is Dolphins
                if self.game.home_team == 'Dolphins':
                    return self.game.home_implied_total
                else:
                    return self.game.away_implied_total
        teams = [
            ('Dolphins D/ST', opp_implied_for('Dolphins'), p_edges, 'Patriots'),
            ('Patriots D/ST', opp_implied_for('Patriots'), d_edges, 'Dolphins'),
        ]
        rows = []
        # derive team sack estimate from individual sacks props if available
        def team_sacks_est(team_key: str) -> Optional[float]:
            if self.props_df is None or self.props_df.empty:
                return None
            df = self.props_df
            sacks = df[df['market'].str.contains('sacks', case=False, na=False)]
            if sacks.empty:
                return None
            # crude mapping by last names to teams list
            if team_key == 'Dolphins':
                roster_keys = ['chubb', 'ramsey', 'phillips', 'sieler', 'davis', 'van ginkel']
            else:
                roster_keys = ['judon', 'barmore', 'ujai', 'wise', 'bentley']
            total = 0.0
            for _, r in sacks.iterrows():
                name = str(r['player_name']).lower()
                if any(k in name for k in roster_keys):
                    total += float(r['line'])
            return total if total > 0 else None

        for name, opp_it, edges, opp_team in teams:
            # Points allowed bucket per DK rules
            if opp_it <= 6:
                pa = 10.0
            elif opp_it <= 13:
                pa = 7.0
            elif opp_it <= 20:
                pa = 4.0
            elif opp_it <= 27:
                pa = 1.0
            elif opp_it <= 34:
                pa = 0.0
            elif opp_it <= 41:
                pa = -1.0
            else:
                pa = -4.0

            sacks_base = 2.5
            sacks_prop = team_sacks_est(opp_team)
            if sacks_prop is not None:
                sacks = sacks_prop
            else:
                sacks = sacks_base * (1.0 + 0.10 * edges.edge_protection)
            turnovers = 1.2 * (1.0 + 0.05 * abs(edges.edge_protection))
            def_td = 0.12  # 12% chance

            pts = (
                pa + sacks * self.dk['dst_sack'] + turnovers * self.dk['dst_int'] + def_td * self.dk['dst_td']
            )
            rows.append({'Player': name, 'Projection': round(float(pts), 2)})
        return pd.DataFrame(rows)

    # ----------------------------- Orchestration -----------------------------
    def generate(self) -> pd.DataFrame:
        if not self.load_team_data():
            return pd.DataFrame()
        if not self.fetch_market_data():
            return pd.DataFrame()
        props_analysis = self.analyze_props()
        if not props_analysis:
            return pd.DataFrame()
        players = self.projections_from_props(props_analysis)
        dst = self.defense_projections(props_analysis)
        all_df = pd.concat([players, dst], ignore_index=True)
        return all_df.sort_values('Projection', ascending=False)

    def save(self, df: pd.DataFrame, filename: Optional[str] = None) -> str:
        if filename is None:
            ts = datetime.now().strftime('%Y%m%d_%H%M')
            filename = f'dolphins_patriots_elite_projections_{ts}.csv'
        # Ensure 2-col format
        df[['Player', 'Projection']].to_csv(filename, index=False)
        return filename


def main():
    proj = DolphinsPatriotsEliteProjections()
    df = proj.generate()
    if df.empty:
        print("No projections generated. Ensure ODDS_API_KEY is set and game is listed.")
        return
    # Show a quick preview
    print(df.head(15).to_string(index=False))
    out = proj.save(df)
    print(f"Saved: {out}")


if __name__ == '__main__':
    main()

