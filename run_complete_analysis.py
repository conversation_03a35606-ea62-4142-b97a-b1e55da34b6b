#!/usr/bin/env python3
"""
Complete NFL Analysis Pipeline
Combines market signals + gamebook intelligence for enhanced projections
"""

from enhanced_projection_system import EnhancedProjectionSystem
from gamebook_intelligence import GamebookIntelligence
import j<PERSON>


def run_complete_pipeline(away_team: str, home_team: str, 
                         base_projections: dict = None,
                         odds_api_key: str = None):
    """Run the complete enhanced projection pipeline."""
    
    print("🚀 NFL ENHANCED PROJECTION PIPELINE")
    print("=" * 45)
    print(f"🏈 Game: {away_team} @ {home_team}")
    print("=" * 45)
    
    # Game info
    game_info = {
        'away_team': away_team,
        'home_team': home_team
    }
    
    # Initialize enhanced system
    system = EnhancedProjectionSystem(odds_api_key)
    
    # Run complete analysis
    results = system.run_enhanced_projections(game_info, base_projections)
    
    # Print detailed results
    system.print_enhanced_results(results)
    
    # Additional insights
    print_additional_insights(results)
    
    return results


def print_additional_insights(results):
    """Print additional gamebook insights."""
    
    gamebook_analysis = results.get('gamebook_analysis', {})
    team_rankings = results.get('team_rankings', {})
    
    if 'error' not in gamebook_analysis:
        print(f"\n🧠 DETAILED GAMEBOOK ANALYSIS:")
        print("-" * 40)
        
        # Team profiles
        team_profiles = gamebook_analysis.get('team_profiles', {})
        for team, profile in team_profiles.items():
            if profile:
                print(f"\n{team} Profile:")
                print(f"  Games Played: {profile.get('games_played', 0)}")
                print(f"  Offensive Rating: {profile.get('offensive_rating', 0):.3f}")
                print(f"  Defensive Rating: {profile.get('defensive_rating', 0):.3f}")
                
                strengths = profile.get('key_strengths', [])
                if strengths:
                    print(f"  Strengths: {', '.join(strengths)}")
                
                weaknesses = profile.get('key_weaknesses', [])
                if weaknesses:
                    print(f"  Weaknesses: {', '.join(weaknesses)}")
        
        # Projection adjustments
        proj_adjustments = gamebook_analysis.get('projection_adjustments', {})
        if proj_adjustments:
            print(f"\n📊 PROJECTION ADJUSTMENTS:")
            for adjustment, value in proj_adjustments.items():
                if value != 0:
                    print(f"  {adjustment.replace('_', ' ').title()}: {value:+.3f}")
    
    # League rankings context
    if team_rankings:
        print(f"\n🏆 LEAGUE CONTEXT:")
        print("-" * 25)
        
        game_teams = results['game_info']['away_team'], results['game_info']['home_team']
        
        for category, rankings in team_rankings.items():
            print(f"\n{category.replace('_', ' ').title()}:")
            for i, (team, score) in enumerate(rankings, 1):
                if team in game_teams:
                    print(f"  #{i:2d}. {team}: {score:.3f} ⭐")
                elif i <= 5:
                    print(f"  #{i:2d}. {team}: {score:.3f}")


def main():
    """Interactive pipeline runner."""
    
    print("🎯 NFL ENHANCED PROJECTION SYSTEM")
    print("=" * 40)
    
    # Get game info
    print("\n📝 Enter game information:")
    away_team = input("Away team: ").strip().title()
    home_team = input("Home team: ").strip().title()
    
    # Optional: Get API key
    print("\n🔑 API Configuration (optional):")
    api_key = input("Odds API key (press Enter to skip): ").strip()
    if not api_key:
        api_key = None
        print("   Running without live market data")
    
    # Optional: Get base projections
    print("\n📊 Base Projections (optional):")
    use_sample = input("Use sample projections? (y/n): ").strip().lower()
    
    base_projections = None
    if use_sample == 'y':
        base_projections = {
            f'{away_team} QB1': 20.5,
            f'{home_team} QB1': 18.8,
            f'{away_team} RB1': 14.2,
            f'{home_team} RB1': 12.7,
            f'{away_team} WR1': 13.8,
            f'{home_team} WR1': 11.9,
            f'{away_team} TE1': 8.5,
            f'{home_team} TE1': 7.8,
        }
        print("   Using sample projections")
    else:
        print("   Using position-based estimates")
    
    # Run analysis
    print(f"\n🚀 Running complete analysis...")
    results = run_complete_pipeline(away_team, home_team, base_projections, api_key)
    
    # Save results
    output_file = f"enhanced_projections_{away_team}_{home_team}.json"
    with open(output_file, 'w') as f:
        # Convert results to JSON-serializable format
        json_results = {
            'game_info': results['game_info'],
            'projections': [
                {
                    'player': p.player,
                    'position': p.position,
                    'team': p.team,
                    'projection': p.final_projection,
                    'confidence': p.confidence,
                    'reasoning': p.reasoning
                }
                for p in results['enhanced_projections']
            ],
            'recommendations': results['final_recommendations'],
            'gamebook_insights': results['gamebook_analysis']
        }
        json.dump(json_results, f, indent=2)
    
    print(f"\n💾 Results saved to: {output_file}")


# Pre-configured game examples
def run_example_games():
    """Run analysis for example games."""
    
    example_games = [
        ('Chiefs', 'Bills'),
        ('Ravens', 'Steelers'), 
        ('49ers', 'Rams'),
        ('Cowboys', 'Eagles'),
        ('Lions', 'Packers')
    ]
    
    print("🎯 RUNNING EXAMPLE GAME ANALYSES")
    print("=" * 40)
    
    for away, home in example_games:
        print(f"\n🏈 Analyzing: {away} @ {home}")
        print("-" * 30)
        
        # Sample projections for demonstration
        sample_projections = {
            f'{away} QB1': 21.5,
            f'{home} QB1': 19.2,
            f'{away} RB1': 13.8,
            f'{home} RB1': 12.4,
            f'{away} WR1': 14.1,
            f'{home} WR1': 12.7
        }
        
        try:
            results = run_complete_pipeline(away, home, sample_projections)
            
            # Quick summary
            top_proj = results['enhanced_projections'][0]
            print(f"   Top Projection: {top_proj.player} - {top_proj.final_projection:.1f} pts")
            
            recommendations = results['final_recommendations']
            if recommendations:
                top_rec = recommendations[0]
                print(f"   Top Recommendation: {top_rec['player']} - {top_rec['edge']:+.1%} edge")
            
            overall_edge = results['gamebook_analysis'].get('overall_edge', 0)
            print(f"   Matchup Edge: {overall_edge:+.3f}")
            
        except Exception as e:
            print(f"   Error: {str(e)}")
        
        print()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == 'examples':
        run_example_games()
    else:
        main()
