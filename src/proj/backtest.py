"""
Week 1 backtest module for gentle projection nudges based on historical residuals.

This module analyzes 2024 Week 1 projection vs actual performance to create
small, confidence-weighted adjustments to priors and uncertainty bands.
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path
from typing import Dict, Any, Tuple, Optional
from .io_csv import load_csv


# Default parameters for gentle nudging
DEFAULT_LAMBDA = 0.35      # Shrink mean_error substantially
DEFAULT_MU_CAP = 0.6       # Nudge cap in DK points
DEFAULT_SIGMA_SCALE = 0.03 # 3% sigma increase per 1pt abs bias
DEFAULT_SIGMA_CAP = 1.10   # At most +10% wider uncertainty


def detect_columns(df: pd.DataFrame) -> Dict[str, str]:
    """
    Map canonical columns from common variants.
    
    Args:
        df: DataFrame to analyze
        
    Returns:
        Dict mapping canonical names to actual column names
    """
    columns = df.columns.str.lower().str.strip()
    mapping = {}
    
    # Player name variants
    player_variants = ['player', 'player_name', 'name', 'full_name']
    for variant in player_variants:
        if variant in columns:
            matching_cols = df.columns[columns == variant]
            if len(matching_cols) > 0:
                mapping['player'] = matching_cols[0]
            break

    # Team variants
    team_variants = ['team', 'tm', 'club']
    for variant in team_variants:
        if variant in columns:
            matching_cols = df.columns[columns == variant]
            if len(matching_cols) > 0:
                mapping['team'] = matching_cols[0]
            break

    # Position variants
    pos_variants = ['pos', 'position', 'pos_group']
    for variant in pos_variants:
        if variant in columns:
            matching_cols = df.columns[columns == variant]
            if len(matching_cols) > 0:
                mapping['pos'] = matching_cols[0]
            break

    # Projection variants
    proj_variants = ['projection', 'proj', 'projpoints', 'proj_mean', 'expected', 'fc proj', 'my proj', 'fc', 'my']
    for variant in proj_variants:
        if variant in columns:
            matching_cols = df.columns[columns == variant]
            if len(matching_cols) > 0:
                mapping['proj'] = matching_cols[0]
            break

    # Actual score variants
    actual_variants = ['score', 'fpts', 'fantasypoints', 'actual', 'points']
    for variant in actual_variants:
        if variant in columns:
            matching_cols = df.columns[columns == variant]
            if len(matching_cols) > 0:
                mapping['actual'] = matching_cols[0]
            break
    
    return mapping


def load_fc_week1_2024(path: str) -> pd.DataFrame:
    """
    Load and normalize 2024 Week 1 fantasy data.

    Args:
        path: Path to CSV file

    Returns:
        DataFrame with normalized columns: player_name, team, pos, exp_proj, actual_fp
    """
    # Try loading with pandas directly first
    try:
        df = pd.read_csv(path)

        # Check if first row contains headers (common issue with exported CSVs)
        if df.iloc[0, 0] == 'Player' or 'Player' in str(df.iloc[0, 0]):
            # Use first row as headers
            df.columns = df.iloc[0]
            df = df.iloc[1:].reset_index(drop=True)

        # If still no proper columns, try skipping first row
        if df.columns[0].startswith('Unnamed'):
            df = pd.read_csv(path, skiprows=1)
    except Exception:
        # Fallback to load_csv
        df, metadata = load_csv(path)

    if df.empty:
        raise ValueError(f"No data loaded from {path}")

    # Detect column mappings
    col_mapping = detect_columns(df)
    
    # Check required columns
    required = ['player', 'proj', 'actual']
    missing = [col for col in required if col not in col_mapping]
    if missing:
        available_cols = list(df.columns)
        raise ValueError(f"Missing required columns: {missing}. Available: {available_cols}")
    
    # Create normalized DataFrame
    result = pd.DataFrame()
    result['player_name'] = df[col_mapping['player']].astype(str).str.strip()
    result['team'] = df[col_mapping.get('team', 'team')].astype(str).str.strip() if 'team' in col_mapping else ''
    result['pos'] = df[col_mapping.get('pos', 'pos')].astype(str).str.strip() if 'pos' in col_mapping else ''
    result['exp_proj'] = pd.to_numeric(df[col_mapping['proj']], errors='coerce')
    result['actual_fp'] = pd.to_numeric(df[col_mapping['actual']], errors='coerce')
    
    # Remove rows with missing critical data
    result = result.dropna(subset=['exp_proj', 'actual_fp'])
    
    # Normalize team names to uppercase
    result['team'] = result['team'].str.upper()
    
    # Normalize position names
    result['pos'] = result['pos'].str.upper()
    
    return result


def compute_residuals(df: pd.DataFrame) -> pd.DataFrame:
    """
    Compute projection residuals with position groups and archetypes.
    
    Args:
        df: DataFrame with exp_proj and actual_fp columns
        
    Returns:
        DataFrame with added error metrics and archetypes
    """
    result = df.copy()
    
    # Basic residuals
    result['error'] = result['actual_fp'] - result['exp_proj']
    result['abs_error'] = result['error'].abs()
    
    # Winsorize errors at 5th/95th percentiles to limit outliers
    error_5th = result['error'].quantile(0.05)
    error_95th = result['error'].quantile(0.95)
    result['error_winsorized'] = result['error'].clip(error_5th, error_95th)
    
    # Add position groups (normalize common variants)
    pos_mapping = {
        'QB': 'QB', 'QUARTERBACK': 'QB',
        'RB': 'RB', 'RUNNINGBACK': 'RB', 'RUNNING BACK': 'RB',
        'WR': 'WR', 'WIDE RECEIVER': 'WR', 'WIDERECEIVER': 'WR',
        'TE': 'TE', 'TIGHT END': 'TE', 'TIGHTEND': 'TE',
        'DST': 'DST', 'D/ST': 'DST', 'DEF': 'DST', 'DEFENSE': 'DST'
    }
    result['pos_group'] = result['pos'].map(pos_mapping).fillna(result['pos'])
    
    # Simple archetype assignment (placeholder - could be enhanced)
    result['archetype'] = result['pos_group']  # Default to position
    
    # For WRs, could add more sophisticated archetype detection based on projection ranges
    wr_mask = result['pos_group'] == 'WR'
    if wr_mask.sum() > 0:
        wr_high_proj = result.loc[wr_mask, 'exp_proj'] >= result.loc[wr_mask, 'exp_proj'].quantile(0.7)
        result.loc[wr_mask & wr_high_proj, 'archetype'] = 'WR_alpha'
        result.loc[wr_mask & ~wr_high_proj, 'archetype'] = 'WR_depth'
    
    # For RBs, similar logic
    rb_mask = result['pos_group'] == 'RB'
    if rb_mask.sum() > 0:
        rb_high_proj = result.loc[rb_mask, 'exp_proj'] >= result.loc[rb_mask, 'exp_proj'].quantile(0.6)
        result.loc[rb_mask & rb_high_proj, 'archetype'] = 'RB_feature'
        result.loc[rb_mask & ~rb_high_proj, 'archetype'] = 'RB_committee'
    
    return result


def summarize_residuals(df: pd.DataFrame) -> Dict[str, Any]:
    """
    Compute residual statistics by position and archetype.
    
    Args:
        df: DataFrame with residuals
        
    Returns:
        Dict with global, by_pos, and by_archetype statistics
    """
    def compute_stats(group_df: pd.DataFrame) -> Dict[str, float]:
        """Compute statistics for a group."""
        if len(group_df) == 0:
            return {'mean_error': 0.0, 'mae': 0.0, 'rmse': 0.0, 'pearson_r': 0.0, 'count': 0}
        
        error = group_df['error_winsorized']
        actual = group_df['actual_fp']
        proj = group_df['exp_proj']
        
        # Correlation (handle edge cases)
        try:
            corr = np.corrcoef(proj, actual)[0, 1]
            if np.isnan(corr):
                corr = 0.0
        except:
            corr = 0.0
        
        return {
            'mean_error': float(error.mean()),
            'mae': float(error.abs().mean()),
            'rmse': float(np.sqrt((error ** 2).mean())),
            'pearson_r': float(corr),
            'count': len(group_df)
        }
    
    summary = {}
    
    # Global statistics
    summary['global'] = compute_stats(df)
    
    # By position
    summary['by_pos'] = {}
    for pos in df['pos_group'].unique():
        if pd.notna(pos):
            pos_df = df[df['pos_group'] == pos]
            summary['by_pos'][pos] = compute_stats(pos_df)
    
    # By archetype
    summary['by_archetype'] = {}
    for arch in df['archetype'].unique():
        if pd.notna(arch):
            arch_df = df[df['archetype'] == arch]
            summary['by_archetype'][arch] = compute_stats(arch_df)
    
    return summary


def build_residual_adjusters(summary: Dict[str, Any], 
                           lambda_shrink: float = DEFAULT_LAMBDA,
                           mu_cap: float = DEFAULT_MU_CAP,
                           sigma_scale: float = DEFAULT_SIGMA_SCALE,
                           sigma_cap: float = DEFAULT_SIGMA_CAP) -> Dict[str, Dict[str, float]]:
    """
    Convert residual summary to gentle nudge parameters.
    
    Args:
        summary: Output from summarize_residuals
        lambda_shrink: Shrinkage factor for mean error
        mu_cap: Maximum absolute nudge in points
        sigma_scale: Sigma multiplier per point of absolute bias
        sigma_cap: Maximum sigma multiplier
        
    Returns:
        Dict with adjustment parameters by group
    """
    adjusters = {}
    
    # Process all groups (positions and archetypes)
    all_groups = {}
    all_groups.update(summary.get('by_pos', {}))
    all_groups.update(summary.get('by_archetype', {}))
    all_groups['global'] = summary.get('global', {})
    
    for group_name, stats in all_groups.items():
        if stats.get('count', 0) < 3:  # Skip groups with too few observations
            continue
        
        mean_error = stats.get('mean_error', 0.0)
        
        # Gentle mu adjustment with shrinkage and cap
        mu_delta = np.clip(mean_error * lambda_shrink, -mu_cap, mu_cap)
        
        # Sigma multiplier based on absolute bias
        abs_bias = abs(mean_error)
        sigma_mult = np.clip(1.0 + abs_bias * sigma_scale, 1.0, sigma_cap)
        
        adjusters[group_name] = {
            'mu_delta': float(mu_delta),
            'sigma_mult': float(sigma_mult),
            'source_mean_error': float(mean_error),
            'source_count': int(stats.get('count', 0))
        }
    
    return adjusters


def write_backtest_report(residuals_df: pd.DataFrame,
                         summary: Dict[str, Any],
                         out_csv: str,
                         out_txt: str) -> None:
    """
    Write backtest results to CSV and text report.

    Args:
        residuals_df: DataFrame with residuals
        summary: Summary statistics
        out_csv: Path for CSV output
        out_txt: Path for text report
    """
    # Save detailed residuals to CSV
    residuals_df.to_csv(out_csv, index=False)

    # Create text report
    with open(out_txt, 'w') as f:
        f.write("# 2024 Week 1 Backtest Report\n\n")

        # Global statistics
        global_stats = summary.get('global', {})
        f.write("## Global Performance\n")
        f.write(f"- Count: {global_stats.get('count', 0)}\n")
        f.write(f"- Mean Error (Bias): {global_stats.get('mean_error', 0):.3f}\n")
        f.write(f"- MAE: {global_stats.get('mae', 0):.3f}\n")
        f.write(f"- RMSE: {global_stats.get('rmse', 0):.3f}\n")
        f.write(f"- Correlation: {global_stats.get('pearson_r', 0):.3f}\n\n")

        # By position
        f.write("## By Position\n")
        for pos, stats in summary.get('by_pos', {}).items():
            f.write(f"### {pos}\n")
            f.write(f"- Count: {stats.get('count', 0)}\n")
            f.write(f"- Mean Error: {stats.get('mean_error', 0):.3f}\n")
            f.write(f"- MAE: {stats.get('mae', 0):.3f}\n")
            f.write(f"- RMSE: {stats.get('rmse', 0):.3f}\n")
            f.write(f"- Correlation: {stats.get('pearson_r', 0):.3f}\n\n")

        # Top smashes (under-projected)
        f.write("## Top 15 Under-Projected (Smashes)\n")
        smashes = residuals_df.nlargest(15, 'error')[['player_name', 'pos_group', 'team', 'exp_proj', 'actual_fp', 'error']]
        for _, row in smashes.iterrows():
            f.write(f"- {row['player_name']} ({row['pos_group']}, {row['team']}): "
                   f"Proj {row['exp_proj']:.1f} → Actual {row['actual_fp']:.1f} "
                   f"(+{row['error']:.1f})\n")

        f.write("\n## Top 15 Over-Projected (Busts)\n")
        busts = residuals_df.nsmallest(15, 'error')[['player_name', 'pos_group', 'team', 'exp_proj', 'actual_fp', 'error']]
        for _, row in busts.iterrows():
            f.write(f"- {row['player_name']} ({row['pos_group']}, {row['team']}): "
                   f"Proj {row['exp_proj']:.1f} → Actual {row['actual_fp']:.1f} "
                   f"({row['error']:.1f})\n")


def apply_week1_nudges(player_row: pd.Series,
                      adjusters: Dict[str, Dict[str, float]],
                      confidence: float = 1.0) -> Tuple[float, float]:
    """
    Apply gentle Week 1 nudges to a player's projection.

    Args:
        player_row: Player data with pos_group and archetype if available
        adjusters: Adjustment parameters from build_residual_adjusters
        confidence: Confidence factor (0-1) to scale nudges

    Returns:
        Tuple of (mu_adjustment, sigma_multiplier)
    """
    # Determine group keys in order of specificity
    archetype = player_row.get('archetype', '')
    pos_group = player_row.get('pos_group', player_row.get('position', ''))

    # Try archetype first, then position, then global
    group_keys = [archetype, pos_group, 'global']

    mu_delta = 0.0
    sigma_mult = 1.0

    for key in group_keys:
        if key and key in adjusters:
            adjuster = adjusters[key]
            mu_delta = adjuster.get('mu_delta', 0.0)
            sigma_mult = adjuster.get('sigma_mult', 1.0)
            break

    # Scale by confidence
    mu_delta_scaled = mu_delta * confidence
    sigma_mult_scaled = 1.0 + (sigma_mult - 1.0) * confidence

    return mu_delta_scaled, sigma_mult_scaled
