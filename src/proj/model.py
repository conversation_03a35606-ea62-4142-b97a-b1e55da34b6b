"""NFL projection models with context-aware Bayesian blending."""

import pandas as pd
import numpy as np
from typing import Dict, Any, Optional, Tuple
from scipy import stats
from scipy.optimize import minimize_scalar


def american_to_prob(odds: float) -> float:
    """Convert American odds to implied probability."""
    if odds > 0:
        return 100 / (odds + 100)
    else:
        return abs(odds) / (abs(odds) + 100)


def devig(p_over_raw: float, p_under_raw: float, method: str = "multiplicative") -> Tuple[float, float]:
    """
    Remove vig from over/under probabilities.

    Args:
        p_over_raw: Raw implied probability for over
        p_under_raw: Raw implied probability for under
        method: Devig method ("multiplicative", "additive", "power")

    Returns:
        Tuple of (p_over_fair, p_under_fair)
    """
    total_prob = p_over_raw + p_under_raw

    if method == "multiplicative":
        # Scale probabilities to sum to 1
        p_over_fair = p_over_raw / total_prob
        p_under_fair = p_under_raw / total_prob
    elif method == "additive":
        # Remove equal vig from both sides
        vig = (total_prob - 1) / 2
        p_over_fair = p_over_raw - vig
        p_under_fair = p_under_raw - vig
    elif method == "power":
        # Power method (Shin's method approximation)
        if total_prob <= 1.0:
            # No vig case
            p_over_fair = p_over_raw
            p_under_fair = p_under_raw
        else:
            # Calculate margin
            margin = total_prob - 1.0
            # Simple power adjustment
            p_over_fair = p_over_raw ** (1 / (1 + margin))
            p_under_fair = p_under_raw ** (1 / (1 + margin))
            # Normalize to sum to 1
            total_fair = p_over_fair + p_under_fair
            p_over_fair = p_over_fair / total_fair
            p_under_fair = p_under_fair / total_fair
    else:
        raise ValueError(f"Unknown devig method: {method}")

    return p_over_fair, p_under_fair


def line_to_implied_mean(line: float, p_over: float, sigma_guess: float = 1.0) -> float:
    """
    Convert betting line to implied mean using Normal assumption.

    Args:
        line: Betting line (over/under threshold)
        p_over: Fair probability of going over the line
        sigma_guess: Initial guess for standard deviation

    Returns:
        Implied mean (mu_mkt)
    """
    # mu_mkt = line + sigma * Phi^-1(p_over)
    # where Phi^-1 is the inverse normal CDF
    z_score = stats.norm.ppf(p_over)
    mu_mkt = line + sigma_guess * z_score
    return mu_mkt


def bayes_blend(mu_prior: float, sigma_prior: float,
                mu_mkt: float, sigma_mkt: float) -> Tuple[float, float]:
    """
    Bayesian blend of prior and market information.

    Args:
        mu_prior: Prior mean
        sigma_prior: Prior standard deviation
        mu_mkt: Market-implied mean
        sigma_mkt: Market-implied standard deviation

    Returns:
        Tuple of (mu_posterior, sigma_posterior)
    """
    # Precision-weighted average
    precision_prior = 1 / (sigma_prior ** 2)
    precision_mkt = 1 / (sigma_mkt ** 2)

    precision_post = precision_prior + precision_mkt
    sigma_post = np.sqrt(1 / precision_post)

    mu_post = (precision_prior * mu_prior + precision_mkt * mu_mkt) / precision_post

    return mu_post, sigma_post


class NFLProjectionModel:
    """Base class for NFL projection models."""
    
    def __init__(self):
        self.is_fitted = False
        self.features = []
    
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'NFLProjectionModel':
        """Fit the model to training data."""
        raise NotImplementedError
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions on new data."""
        raise NotImplementedError
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get feature importance scores."""
        raise NotImplementedError


class SimpleLinearModel(NFLProjectionModel):
    """Simple linear regression model for NFL projections."""
    
    def __init__(self):
        super().__init__()
        self.coefficients = {}
        self.intercept = 0.0
    
    def fit(self, X: pd.DataFrame, y: pd.Series) -> 'SimpleLinearModel':
        """Fit linear model using normal equation."""
        from scipy.linalg import lstsq
        
        self.features = list(X.columns)
        
        # Add intercept column
        X_with_intercept = np.column_stack([np.ones(len(X)), X.values])
        
        # Solve normal equation
        coeffs, _, _, _ = lstsq(X_with_intercept, y.values)
        
        self.intercept = coeffs[0]
        self.coefficients = dict(zip(self.features, coeffs[1:]))
        self.is_fitted = True
        
        return self
    
    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make predictions."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted before making predictions")
        
        predictions = np.full(len(X), self.intercept)
        for feature in self.features:
            if feature in X.columns:
                predictions += X[feature].values * self.coefficients[feature]
        
        return predictions
    
    def get_feature_importance(self) -> Dict[str, float]:
        """Get coefficient magnitudes as feature importance."""
        if not self.is_fitted:
            raise ValueError("Model must be fitted first")

        return {feature: abs(coeff) for feature, coeff in self.coefficients.items()}


class ContextAwareModel(NFLProjectionModel):
    """Context-aware NFL projection model with Bayesian blending."""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        super().__init__()
        self.config = config or {
            'sigma_market_factor_week1': 1.3,
            'devig_method': 'multiplicative',
            'default_sigma_guess': 7.0
        }
        self.psychology_bias_module = None

    def apply_psychology_bias(self, features: Dict[str, Any]) -> float:
        """
        Apply psychology bias correction to market-implied mean.

        Args:
            features: Game/player features (prime_time, star_player, recency_bias, etc.)

        Returns:
            Bias adjustment to add to mu_mkt
        """
        if self.psychology_bias_module is None:
            # Placeholder for psychology bias module
            # In practice, this would call psych_bias.bias_correction
            bias = 0.0

            # Simple heuristic biases
            if features.get('prime_time', False):
                bias += 0.5  # Slight upward bias for prime time games

            if features.get('star_player', False):
                bias += 0.3  # Slight upward bias for star players

            if features.get('recency_bias', 0) > 0:
                bias += features['recency_bias'] * 0.2  # Recent performance bias

            return bias
        else:
            return self.psychology_bias_module.bias_correction(features)

    def process_market_line(self, line: float, over_odds: float, under_odds: float,
                          week: int = 1, features: Optional[Dict[str, Any]] = None) -> Tuple[float, float]:
        """
        Process market line to get implied mean and sigma.

        Args:
            line: Betting line (over/under threshold)
            over_odds: American odds for over
            under_odds: American odds for under
            week: NFL week number
            features: Additional features for bias correction

        Returns:
            Tuple of (mu_mkt_adjusted, sigma_mkt)
        """
        # Convert odds to probabilities
        p_over_raw = american_to_prob(over_odds)
        p_under_raw = american_to_prob(under_odds)

        # Remove vig
        p_over_fair, p_under_fair = devig(p_over_raw, p_under_raw, self.config['devig_method'])

        # Get implied mean
        sigma_guess = self.config['default_sigma_guess']
        mu_mkt = line_to_implied_mean(line, p_over_fair, sigma_guess)

        # Apply psychology bias
        if features:
            bias_adjustment = self.apply_psychology_bias(features)
            mu_mkt += bias_adjustment

        # Adjust sigma for week 1
        sigma_mkt = sigma_guess
        if week == 1:
            sigma_mkt *= self.config['sigma_market_factor_week1']

        return mu_mkt, sigma_mkt

    def blend_with_prior(self, mu_prior: float, sigma_prior: float,
                        line: float, over_odds: float, under_odds: float,
                        week: int = 1, features: Optional[Dict[str, Any]] = None) -> Tuple[float, float]:
        """
        Blend prior with market information using Bayesian updating.

        Returns:
            Tuple of (mu_posterior, sigma_posterior)
        """
        mu_mkt, sigma_mkt = self.process_market_line(line, over_odds, under_odds, week, features)
        return bayes_blend(mu_prior, sigma_prior, mu_mkt, sigma_mkt)


class DefensivePlayerModel(NFLProjectionModel):
    """Model for defensive player projections (tackles, sacks, etc.)."""

    def __init__(self):
        super().__init__()
        self.position_factors = {
            'LB': {'tackles': 1.2, 'assists': 1.1, 'sacks': 0.8},
            'S': {'tackles': 1.0, 'assists': 1.0, 'sacks': 0.3},
            'CB': {'tackles': 0.6, 'assists': 0.8, 'sacks': 0.2},  # CBs have fewer tackles
            'DE': {'tackles': 0.8, 'assists': 0.7, 'sacks': 1.5},  # DEs have more tackles than CBs
            'DT': {'tackles': 0.9, 'assists': 0.9, 'sacks': 1.0}
        }

    def project_defensive_stats(self, player_info: Dict[str, Any],
                              game_context: Dict[str, Any]) -> Dict[str, float]:
        """
        Project defensive player statistics.

        Args:
            player_info: Player information (position, historical stats, etc.)
            game_context: Game context (opponent pace, play volume, etc.)

        Returns:
            Dictionary with projected defensive stats
        """
        position = player_info.get('position', 'LB')
        snap_share = player_info.get('snap_share', 0.6)

        # Base rates per snap
        base_rates = {
            'tackles': 0.08,
            'assists': 0.06,
            'sacks': 0.015,
            'interceptions': 0.005,
            'fumble_recoveries': 0.003
        }

        # Adjust by position
        position_adj = self.position_factors.get(position, {'tackles': 1.0, 'assists': 1.0, 'sacks': 1.0})

        # Estimate opponent plays
        opp_pace = game_context.get('opponent_pace', 65)  # plays per game
        opp_pass_rate = game_context.get('opponent_pass_rate', 0.6)

        # Adjust rates based on opponent tendencies
        if opp_pass_rate > 0.65:  # Pass-heavy opponent
            base_rates['sacks'] *= 1.2
            base_rates['tackles'] *= 0.9
        elif opp_pass_rate < 0.55:  # Run-heavy opponent
            base_rates['tackles'] *= 1.1
            base_rates['sacks'] *= 0.8

        projections = {}

        for stat, base_rate in base_rates.items():
            # Apply position adjustment
            adj_rate = base_rate * position_adj.get(stat, 1.0)

            # Project based on expected snaps
            expected_snaps = opp_pace * snap_share
            projection = adj_rate * expected_snaps

            projections[stat] = max(0, projection)

        return projections

    def apply_matchup_adjustments(self, base_projections: Dict[str, float],
                                matchup_info: Dict[str, Any]) -> Dict[str, float]:
        """Apply matchup-specific adjustments to defensive projections."""
        adjusted = base_projections.copy()

        # Opponent offensive line quality
        oline_quality = matchup_info.get('opponent_oline_rating', 0.5)  # 0-1 scale

        # Better OLine = fewer sacks/pressures
        sack_adjustment = 1.0 - (oline_quality - 0.5) * 0.4
        adjusted['sacks'] *= max(0.5, sack_adjustment)

        # Opponent QB mobility
        qb_mobility = matchup_info.get('opponent_qb_mobility', 0.5)  # 0-1 scale

        # Mobile QBs are harder to sack but may scramble into more tackles
        if qb_mobility > 0.7:
            adjusted['sacks'] *= 0.85
            adjusted['tackles'] *= 1.05

        # Weather conditions
        weather = matchup_info.get('weather_conditions', 'clear')
        if weather in ['rain', 'snow', 'wind']:
            # Bad weather typically means more running, more tackles
            adjusted['tackles'] *= 1.1
            adjusted['sacks'] *= 0.9

        return adjusted
