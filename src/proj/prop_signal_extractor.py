"""Advanced prop odds signal extraction for NFL projections."""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from scipy import stats
import re


@dataclass
class PropSignal:
    """Container for extracted prop market signals."""
    player_name: str
    market: str
    line: float
    implied_mean: float
    implied_prob_over: float
    market_confidence: float  # Based on vig and line movement
    volume_signal: float  # Derived volume expectation
    efficiency_signal: float  # Derived efficiency expectation
    scoring_signal: float  # TD probability signal
    game_script_bias: float  # How much game script affects this prop
    sharp_money_indicator: float  # 0-1 scale of sharp vs public money


class PropSignalExtractor:
    """Extract actionable signals from prop betting markets."""
    
    def __init__(self):
        self.position_sigma_defaults = {
            'QB': {'pass_yards': 28, 'pass_tds': 0.8, 'rush_yards': 12, 'completions': 3.2},
            'RB': {'rush_yards': 18, 'rush_att': 4.5, 'rec_yards': 12, 'receptions': 1.2},
            'WR': {'rec_yards': 15, 'receptions': 1.8, 'rec_longest': 8},
            'TE': {'rec_yards': 12, 'receptions': 1.5, 'rec_longest': 6},
            'K': {'kicking_points': 3.2, 'fg_made': 0.8}
        }
        
        # Market efficiency indicators (lower = sharper market)
        self.market_sharpness = {
            'pass_yards': 0.9, 'rush_yards': 0.85, 'rec_yards': 0.8,
            'pass_tds': 0.75, 'rush_tds': 0.7, 'rec_tds': 0.7,
            'td_anytime': 0.6, 'td_first': 0.4, 'td_2+': 0.3
        }
    
    def american_to_prob(self, odds: float) -> float:
        """Convert American odds to implied probability."""
        if odds > 0:
            return 100 / (odds + 100)
        else:
            return abs(odds) / (abs(odds) + 100)
    
    def devig_multiplicative(self, p_over: float, p_under: float) -> Tuple[float, float]:
        """Remove vig using multiplicative method."""
        total_prob = p_over + p_under
        if total_prob <= 1.0:
            return p_over, p_under
        
        fair_over = p_over / total_prob
        fair_under = p_under / total_prob
        return fair_over, fair_under
    
    def extract_volume_signal(self, player: str, market: str, line: float, 
                            implied_mean: float, position: str) -> float:
        """Extract volume expectation from prop lines."""
        volume_signals = {
            'pass_att': implied_mean,
            'completions': implied_mean,
            'rush_att': implied_mean,
            'receptions': implied_mean,
            'targets': implied_mean * 1.4,  # Assume 70% catch rate
        }
        
        if market in volume_signals:
            return volume_signals[market]
        
        # Derive volume from yardage props
        if market == 'pass_yards' and position == 'QB':
            return implied_mean / 7.2  # ~7.2 YPA average
        elif market == 'rush_yards' and position in ['RB', 'QB']:
            ypc = 4.3 if position == 'RB' else 5.8  # QB scrambles higher YPC
            return implied_mean / ypc
        elif market == 'rec_yards' and position in ['WR', 'TE', 'RB']:
            # Derive targets from receiving yards
            ypr_by_pos = {'WR': 11.2, 'TE': 9.8, 'RB': 7.4}
            catch_rate_by_pos = {'WR': 0.65, 'TE': 0.72, 'RB': 0.78}
            
            ypr = ypr_by_pos.get(position, 10.0)
            catch_rate = catch_rate_by_pos.get(position, 0.68)
            
            receptions = implied_mean / ypr
            targets = receptions / catch_rate
            return targets
        
        return 0.0
    
    def extract_efficiency_signal(self, player: str, market: str, line: float,
                                implied_mean: float, position: str, volume_signal: float) -> float:
        """Extract efficiency expectation from prop lines."""
        if volume_signal == 0:
            return 0.0
            
        efficiency_signals = {}
        
        if market == 'pass_yards' and position == 'QB':
            efficiency_signals['ypa'] = implied_mean / max(volume_signal, 1)
        elif market == 'rush_yards':
            efficiency_signals['ypc'] = implied_mean / max(volume_signal, 1)
        elif market == 'rec_yards':
            efficiency_signals['ypr'] = implied_mean / max(volume_signal * 0.68, 1)  # Assume catch rate
        elif market in ['rec_longest', 'rush_longest']:
            # Longer props indicate big play potential
            efficiency_signals['explosive_rate'] = min(line / 20.0, 1.0)
        
        return list(efficiency_signals.values())[0] if efficiency_signals else 0.0
    
    def extract_scoring_signal(self, player: str, market: str, line: float,
                             over_odds: float, under_odds: float, position: str) -> float:
        """Extract TD scoring probability from various TD markets."""
        if 'td' not in market:
            return 0.0
        
        p_over = self.american_to_prob(over_odds)
        p_under = self.american_to_prob(under_odds)
        fair_over, _ = self.devig_multiplicative(p_over, p_under)
        
        # Adjust for market type
        if market == 'td_anytime':
            return fair_over
        elif market == 'td_first':
            # First TD is ~15-20% of anytime TD probability
            return fair_over / 0.18
        elif market == 'td_last':
            # Last TD is ~12-15% of anytime TD probability  
            return fair_over / 0.14
        elif market == 'td_2+':
            # 2+ TDs requires much higher scoring rate
            return fair_over / 0.25
        elif market in ['pass_tds', 'rush_tds']:
            # Direct TD line
            return fair_over if line == 0.5 else implied_mean
        
        return fair_over
    
    def calculate_game_script_bias(self, market: str, position: str) -> float:
        """Calculate how much game script affects this prop market."""
        script_sensitivity = {
            # High sensitivity to game script
            ('QB', 'pass_att'): 0.9,
            ('QB', 'pass_yards'): 0.85,
            ('RB', 'rush_att'): 0.8,
            ('RB', 'rush_yards'): 0.75,
            
            # Medium sensitivity
            ('WR', 'receptions'): 0.6,
            ('WR', 'rec_yards'): 0.65,
            ('TE', 'receptions'): 0.5,
            
            # Low sensitivity
            ('QB', 'rush_yards'): 0.3,  # Scrambles happen regardless
            ('K', 'kicking_points'): 0.4,  # Depends on drives, not script
        }
        
        return script_sensitivity.get((position, market), 0.5)
    
    def detect_sharp_money(self, over_odds: float, under_odds: float, 
                          market: str, line: float) -> float:
        """Detect sharp money indicators in prop markets."""
        # Calculate vig
        p_over = self.american_to_prob(over_odds)
        p_under = self.american_to_prob(under_odds)
        total_prob = p_over + p_under
        vig = total_prob - 1.0
        
        sharp_indicators = 0.0
        
        # Low vig indicates sharp market
        if vig < 0.05:
            sharp_indicators += 0.4
        elif vig < 0.08:
            sharp_indicators += 0.2
        
        # Balanced odds (close to -110/-110) often indicate sharp action
        odds_balance = abs(abs(over_odds) - abs(under_odds))
        if odds_balance < 20:
            sharp_indicators += 0.3
        elif odds_balance < 40:
            sharp_indicators += 0.1
        
        # Round numbers often indicate public money
        if line % 0.5 == 0 and line % 1.0 != 0:  # Half-point lines
            sharp_indicators += 0.2
        elif line % 5 == 0:  # Round numbers like 50, 100
            sharp_indicators -= 0.2
        
        # Market type sharpness
        market_sharp = self.market_sharpness.get(market, 0.5)
        sharp_indicators += (market_sharp - 0.5)
        
        return max(0.0, min(1.0, sharp_indicators))
    
    def line_to_implied_mean(self, line: float, prob_over: float, sigma: float) -> float:
        """Convert betting line to implied mean using normal distribution."""
        if prob_over <= 0 or prob_over >= 1:
            return line
        
        # Use inverse normal CDF to find implied mean
        z_score = stats.norm.ppf(1 - prob_over)
        implied_mean = line - (z_score * sigma)
        
        return max(0, implied_mean)  # Ensure non-negative
    
    def process_prop(self, player_name: str, market: str, line: float,
                    over_odds: float, under_odds: float, position: str) -> PropSignal:
        """Process a single prop into actionable signals."""
        # Convert odds to probabilities
        p_over_raw = self.american_to_prob(over_odds)
        p_under_raw = self.american_to_prob(under_odds)

        # Remove vig
        p_over_fair, p_under_fair = self.devig_multiplicative(p_over_raw, p_under_raw)

        # Get sigma estimate
        sigma = self.position_sigma_defaults.get(position, {}).get(market, 5.0)

        # Calculate implied mean
        implied_mean_calc = self.line_to_implied_mean(line, p_over_fair, sigma)
        
        # Extract signals
        volume_signal = self.extract_volume_signal(player_name, market, line, implied_mean_calc, position)
        efficiency_signal = self.extract_efficiency_signal(player_name, market, line, implied_mean_calc, position, volume_signal)
        scoring_signal = self.extract_scoring_signal(player_name, market, line, over_odds, under_odds, position)
        
        # Calculate market confidence (inverse of vig)
        total_prob = p_over_raw + p_under_raw
        vig = total_prob - 1.0
        market_confidence = max(0.1, 1.0 - (vig * 5))  # Scale vig to confidence
        
        # Game script and sharp money indicators
        game_script_bias = self.calculate_game_script_bias(market, position)
        sharp_money_indicator = self.detect_sharp_money(over_odds, under_odds, market, line)
        
        return PropSignal(
            player_name=player_name,
            market=market,
            line=line,
            implied_mean=implied_mean_calc,
            implied_prob_over=p_over_fair,
            market_confidence=market_confidence,
            volume_signal=volume_signal,
            efficiency_signal=efficiency_signal,
            scoring_signal=scoring_signal,
            game_script_bias=game_script_bias,
            sharp_money_indicator=sharp_money_indicator
        )
    
    def extract_all_signals(self, props_data: List[Dict]) -> Dict[str, List[PropSignal]]:
        """Extract signals from all props, grouped by player."""
        player_signals = {}
        
        for prop in props_data:
            player = prop['player_name']
            market = prop['market']
            line = float(prop['line'])
            over_odds = int(prop['over_odds'])
            under_odds = int(prop['under_odds'])
            
            # Infer position from market types (simplified)
            position = self.infer_position(market)
            
            signal = self.process_prop(player, market, line, over_odds, under_odds, position)
            
            if player not in player_signals:
                player_signals[player] = []
            player_signals[player].append(signal)
        
        return player_signals
    
    def infer_position(self, market: str) -> str:
        """Infer player position from market type."""
        if market in ['pass_yards', 'pass_tds', 'completions', 'pass_att', 'interceptions']:
            return 'QB'
        elif market in ['rush_yards', 'rush_att', 'rush_tds'] and 'pass' not in market:
            return 'RB'  # Could be QB but default to RB
        elif market in ['rec_yards', 'receptions', 'rec_longest']:
            return 'WR'  # Could be TE/RB but default to WR
        elif market in ['kicking_points', 'fg_made', 'pat_made']:
            return 'K'
        else:
            return 'UNKNOWN'
