"""Advanced game context analysis from betting markets and external signals."""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from .prop_signal_extractor import PropSignalExtractor, PropSignal


@dataclass
class GameContext:
    """Comprehensive game context derived from multiple sources."""
    total: float
    spread: float
    home_team: str
    away_team: str
    
    # Derived metrics
    implied_pace: float  # Plays per game
    pass_rate_home: float  # Expected pass rate
    pass_rate_away: float
    script_probabilities: Dict[str, float]  # competitive, blowout_home, blowout_away
    
    # Market-derived insights
    sharp_money_lean: str  # Which side sharp money favors
    public_money_lean: str  # Which side public money favors
    line_movement: Dict[str, float]  # How lines have moved
    
    # Weather and external factors
    weather_impact: float  # 0-1 scale of weather impact
    venue_factors: Dict[str, float]  # Dome, altitude, etc.
    
    # Correlation insights
    qb_rushing_boost: Dict[str, float]  # QB rushing expectation boost by team
    rz_efficiency: Dict[str, float]  # Red zone TD rate by team
    pace_factors: Dict[str, float]  # Pace multipliers by team


class AdvancedGameContextAnalyzer:
    """Extract deep game context signals from betting markets."""
    
    def __init__(self):
        self.prop_extractor = PropSignalExtractor()
        
        # NFL team pace baselines (plays per game)
        self.team_pace_baselines = {
            'BUF': 66.2, 'BAL': 64.8, 'PHI': 67.1, 'DAL': 65.3,
            'KC': 66.8, 'SF': 65.9, 'MIA': 68.2, 'CIN': 64.1
        }
        
        # Red zone efficiency baselines
        self.team_rz_td_rates = {
            'BUF': 0.61, 'BAL': 0.64, 'PHI': 0.58, 'DAL': 0.55,
            'KC': 0.63, 'SF': 0.59, 'MIA': 0.52, 'CIN': 0.57
        }
        
        # QB rushing propensity by team
        self.qb_rush_factors = {
            'BUF': 1.4,  # Josh Allen
            'BAL': 1.6,  # Lamar Jackson
            'PHI': 1.3,  # Jalen Hurts
            'KC': 0.8,   # Mahomes less rushing
            'DAL': 0.6,  # Dak limited rushing
        }
    
    def analyze_total_and_pace(self, total: float, home_team: str, away_team: str) -> Tuple[float, float, float]:
        """Analyze total to derive pace and pass rates."""
        # Base pace from team tendencies
        home_pace = self.team_pace_baselines.get(home_team, 65.0)
        away_pace = self.team_pace_baselines.get(away_team, 65.0)
        
        # Implied total pace (both teams combined)
        avg_pace = (home_pace + away_pace) / 2
        
        # Adjust pace based on total
        if total > 50:
            pace_multiplier = 1.0 + ((total - 47) * 0.02)  # Higher totals = more pace
        else:
            pace_multiplier = 1.0 - ((47 - total) * 0.015)  # Lower totals = less pace
        
        implied_pace = avg_pace * pace_multiplier
        
        # Derive pass rates from total
        # Higher totals typically mean more passing
        base_pass_rate = 0.62  # NFL average
        
        if total > 48:
            pass_rate_boost = (total - 48) * 0.01
        else:
            pass_rate_boost = (total - 48) * 0.008
        
        home_pass_rate = min(0.75, base_pass_rate + pass_rate_boost)
        away_pass_rate = min(0.75, base_pass_rate + pass_rate_boost)
        
        return implied_pace, home_pass_rate, away_pass_rate
    
    def analyze_spread_for_script(self, spread: float, total: float) -> Dict[str, float]:
        """Analyze spread to determine game script probabilities."""
        abs_spread = abs(spread)
        
        if abs_spread <= 2.5:
            # Close game - competitive throughout
            script_probs = {
                'competitive': 0.70,
                'blowout_favorite': 0.15,
                'blowout_underdog': 0.15
            }
        elif abs_spread <= 6.5:
            # Moderate favorite
            script_probs = {
                'competitive': 0.55,
                'blowout_favorite': 0.30,
                'blowout_underdog': 0.15
            }
        else:
            # Large favorite
            script_probs = {
                'competitive': 0.35,
                'blowout_favorite': 0.50,
                'blowout_underdog': 0.15
            }
        
        # Adjust for total - high totals less likely to be blowouts
        if total > 50:
            competitive_boost = (total - 50) * 0.02
            script_probs['competitive'] += competitive_boost
            script_probs['blowout_favorite'] -= competitive_boost * 0.6
            script_probs['blowout_underdog'] -= competitive_boost * 0.4
        
        return script_probs
    
    def detect_sharp_vs_public_money(self, player_signals: Dict[str, List[PropSignal]]) -> Tuple[str, str]:
        """Detect which side sharp vs public money is on."""
        sharp_indicators = []
        public_indicators = []
        
        for player, signals in player_signals.items():
            for signal in signals:
                if signal.sharp_money_indicator > 0.7:
                    # High sharp money confidence
                    if signal.implied_prob_over > 0.55:
                        sharp_indicators.append('over')
                    else:
                        sharp_indicators.append('under')
                elif signal.sharp_money_indicator < 0.3:
                    # Likely public money
                    if signal.implied_prob_over > 0.55:
                        public_indicators.append('over')
                    else:
                        public_indicators.append('under')
        
        # Determine consensus
        sharp_lean = 'neutral'
        if sharp_indicators:
            sharp_over_pct = sharp_indicators.count('over') / len(sharp_indicators)
            if sharp_over_pct > 0.6:
                sharp_lean = 'over'
            elif sharp_over_pct < 0.4:
                sharp_lean = 'under'
        
        public_lean = 'neutral'
        if public_indicators:
            public_over_pct = public_indicators.count('over') / len(public_indicators)
            if public_over_pct > 0.6:
                public_lean = 'over'
            elif public_over_pct < 0.4:
                public_lean = 'under'
        
        return sharp_lean, public_lean
    
    def analyze_qb_rushing_context(self, home_team: str, away_team: str, 
                                 player_signals: Dict[str, List[PropSignal]]) -> Dict[str, float]:
        """Analyze QB rushing context from props and team tendencies."""
        qb_rush_boosts = {}
        
        for team in [home_team, away_team]:
            base_factor = self.qb_rush_factors.get(team, 1.0)
            
            # Look for QB rushing props to gauge market expectation
            market_boost = 0.0
            for player, signals in player_signals.items():
                for signal in signals:
                    if signal.market == 'rush_yards' and 'QB' in signal.player_name:
                        # High rushing yards line indicates mobile QB
                        if signal.line > 35:
                            market_boost += 0.3
                        elif signal.line > 25:
                            market_boost += 0.2
                        elif signal.line > 15:
                            market_boost += 0.1
            
            qb_rush_boosts[team] = base_factor + market_boost
        
        return qb_rush_boosts
    
    def analyze_red_zone_context(self, home_team: str, away_team: str,
                                player_signals: Dict[str, List[PropSignal]]) -> Dict[str, float]:
        """Analyze red zone efficiency from TD props."""
        rz_efficiency = {}
        
        for team in [home_team, away_team]:
            base_rate = self.team_rz_td_rates.get(team, 0.58)
            
            # Analyze TD prop concentrations
            td_signal_strength = 0.0
            td_prop_count = 0
            
            for player, signals in player_signals.items():
                for signal in signals:
                    if 'td' in signal.market and signal.scoring_signal > 0:
                        td_signal_strength += signal.scoring_signal
                        td_prop_count += 1
            
            if td_prop_count > 0:
                avg_td_prob = td_signal_strength / td_prop_count
                # Adjust base rate based on market TD expectations
                if avg_td_prob > 0.4:
                    rz_efficiency[team] = min(0.70, base_rate + 0.05)
                elif avg_td_prob < 0.25:
                    rz_efficiency[team] = max(0.45, base_rate - 0.05)
                else:
                    rz_efficiency[team] = base_rate
            else:
                rz_efficiency[team] = base_rate
        
        return rz_efficiency
    
    def estimate_weather_impact(self, venue: str, temperature: Optional[float] = None,
                              wind_speed: Optional[float] = None) -> float:
        """Estimate weather impact on game (0 = no impact, 1 = severe impact)."""
        impact = 0.0
        
        # Temperature impact
        if temperature is not None:
            if temperature < 20:
                impact += 0.3
            elif temperature < 32:
                impact += 0.15
            elif temperature > 85:
                impact += 0.1
        
        # Wind impact
        if wind_speed is not None:
            if wind_speed > 20:
                impact += 0.4
            elif wind_speed > 15:
                impact += 0.2
            elif wind_speed > 10:
                impact += 0.1
        
        # Venue factors
        dome_venues = ['ATL', 'DET', 'HOU', 'IND', 'LV', 'LAR', 'MIN', 'NO']
        if venue in dome_venues:
            impact = 0.0  # No weather impact in domes
        
        return min(1.0, impact)
    
    def build_comprehensive_context(self, total: float, spread: float, 
                                  home_team: str, away_team: str,
                                  player_signals: Dict[str, List[PropSignal]],
                                  weather_data: Optional[Dict] = None) -> GameContext:
        """Build comprehensive game context from all available signals."""
        
        # Analyze pace and pass rates
        implied_pace, home_pass_rate, away_pass_rate = self.analyze_total_and_pace(total, home_team, away_team)
        
        # Analyze game script probabilities
        script_probs = self.analyze_spread_for_script(spread, total)
        
        # Detect sharp vs public money
        sharp_lean, public_lean = self.detect_sharp_vs_public_money(player_signals)
        
        # Analyze QB rushing context
        qb_rush_boosts = self.analyze_qb_rushing_context(home_team, away_team, player_signals)
        
        # Analyze red zone efficiency
        rz_efficiency = self.analyze_red_zone_context(home_team, away_team, player_signals)
        
        # Weather impact
        weather_impact = 0.0
        if weather_data:
            weather_impact = self.estimate_weather_impact(
                home_team, 
                weather_data.get('temperature'),
                weather_data.get('wind_speed')
            )
        
        # Pace factors by team
        pace_factors = {
            home_team: implied_pace / self.team_pace_baselines.get(home_team, 65.0),
            away_team: implied_pace / self.team_pace_baselines.get(away_team, 65.0)
        }
        
        return GameContext(
            total=total,
            spread=spread,
            home_team=home_team,
            away_team=away_team,
            implied_pace=implied_pace,
            pass_rate_home=home_pass_rate,
            pass_rate_away=away_pass_rate,
            script_probabilities=script_probs,
            sharp_money_lean=sharp_lean,
            public_money_lean=public_lean,
            line_movement={},  # Would need historical data
            weather_impact=weather_impact,
            venue_factors={},  # Could add altitude, dome, etc.
            qb_rushing_boost=qb_rush_boosts,
            rz_efficiency=rz_efficiency,
            pace_factors=pace_factors
        )
