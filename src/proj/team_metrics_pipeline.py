#!/usr/bin/env python3
"""
Team metrics pipeline for curated team-game rows, opponent-adjusted unit ratings,
z-scores/ranks, levers/holes, and findings from JSON gamebook MD files.

Input files: csvs/Gamebook Results/*.md (each contains a single JSON object per game)
Outputs (upsert-safe):
- curated/team_game.parquet
- models/team_unit_ratings.parquet
- models/team_ranks.parquet
- models/holes_and_levers.parquet
- findings/asof_week_<W>/team_<TEAM>.json
- qc/qc_issues.parquet, qc/meta.json

Note: Writing Parquet requires pyarrow or fastparquet. If unavailable, CSV fallbacks are written
with the same base name and a .csv extension.
"""
from __future__ import annotations

import json
import os
import re
import math
import hashlib
from dataclasses import dataclass
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import numpy as np
import pandas as pd

LAMBDA = 0.7  # EWMA recency weight
CURATED_DIR = Path("curated")
MODELS_DIR = Path("models")
FINDINGS_DIR = Path("findings")
QC_DIR = Path("qc")
GAMEBOOK_DIR = Path("csvs/Gamebook Results")


# ---------------------------- Utilities ----------------------------

def mmss_to_seconds(s: Optional[str]) -> Optional[int]:
    if s is None:
        return None
    # Strip approximation symbols and spaces
    s = s.strip()
    s = s.replace('≈', '').strip()
    # Keep only digits and colon
    s = re.sub(r"[^0-9:]", "", s)
    if not s:
        return None
    parts = s.split(":")
    try:
        if len(parts) == 2:
            return 60 * int(parts[0]) + int(parts[1])
        elif len(parts) == 3:  # HH:MM:SS -> convert to seconds; use minutes:seconds portion
            return 3600 * int(parts[0]) + 60 * int(parts[1]) + int(parts[2])
    except Exception:
        return None
    return None


def parse_yardline(avg_start: Optional[str]) -> Optional[int]:
    if avg_start is None:
        return None
    # Expect like "SF 25"; robustly extract trailing integer
    m = re.search(r"(\d{1,2})$", avg_start.strip())
    if not m:
        return None
    try:
        yd = int(m.group(1))
        if 0 <= yd <= 50:
            return yd
    except Exception:
        return None
    return None


def safe_div(n: Optional[float], d: Optional[float]) -> Optional[float]:
    if n is None or d in (None, 0, 0.0):
        return None
    try:
        return n / d
    except Exception:
        return None


def write_parquet_or_csv(df: pd.DataFrame, path: Path, index: bool = False, key_cols: Optional[List[str]] = None):
    path.parent.mkdir(parents=True, exist_ok=True)
    try:
        df.to_parquet(path, index=index)
        return str(path)
    except Exception as e:
        # Fallback to CSV
        csv_path = path.with_suffix('.csv')
        # If upsert desired via key_cols, drop duplicates first
        if key_cols:
            df = df.sort_values(key_cols).drop_duplicates(key_cols, keep='last')
        df.to_csv(csv_path, index=index)
        return str(csv_path)


# ---------------------------- Validation ----------------------------

def validate_and_normalize_game(game_json: Dict[str, Any]) -> Tuple[Optional[Dict[str, Any]], Dict[str, Any]]:
    """Validate numeric presence, coercions, and produce QC info. Returns (normalized_game, qc_record).
    If hard validation fails, normalized_game is None but qc_record is populated.
    """
    qc = {
        "missing_fields": [],
        "violations": [],
        "notes": [],
    }

    # Required structure
    for top in ["game", "away", "home"]:
        if top not in game_json:
            qc["missing_fields"].append(top)
    if qc["missing_fields"]:
        return None, qc

    # Coerce numeric fields to proper types or None
    def coerce_fields(side: Dict[str, Any]) -> Dict[str, Any]:
        numeric_int_fields = [
            "score","total_plays","total_yards","third_down_made","third_down_att",
            "red_zone_td","red_zone_att","rushing_yards","rushing_attempts","passing_yards_net",
            "passing_yards_gross","pass_attempts","pass_completions","pass_interceptions","sacks_allowed",
            "sack_yards_lost","punts","kickoffs","kickoff_touchbacks","penalties","penalty_yards",
            "fumbles","fumbles_lost","drives_total","largest_lead","largest_deficit","drives_leading",
            "drives_trailing","explosive_plays_15plus"
        ]
        numeric_float_fields = ["yards_per_play","yards_per_rush","yards_per_pass","punt_average"]
        out = side.copy()
        for f in numeric_int_fields:
            if f not in out or out[f] is None:
                qc["missing_fields"].append(f"{side.get('team','?')}.{f}")
                out[f] = None
            else:
                try:
                    out[f] = int(out[f])
                except Exception:
                    try:
                        out[f] = int(float(out[f]))
                    except Exception:
                        qc["violations"].append(f"Non-integer for {side.get('team','?')}.{f}")
                        out[f] = None
        for f in numeric_float_fields:
            if f not in out or out[f] is None:
                qc["missing_fields"].append(f"{side.get('team','?')}.{f}")
                out[f] = None
            else:
                try:
                    out[f] = float(out[f])
                except Exception:
                    qc["violations"].append(f"Non-float for {side.get('team','?')}.{f}")
                    out[f] = None
        # Time fields
        out["time_of_possession_s"] = mmss_to_seconds(out.get("time_of_possession"))
        if out.get("time_of_possession") and out["time_of_possession_s"] is None:
            qc["violations"].append(f"Unparseable TOP for {side.get('team','?')}: {out.get('time_of_possession')}")
        out["time_leading_s"] = mmss_to_seconds(out.get("time_leading"))
        out["time_trailing_s"] = mmss_to_seconds(out.get("time_trailing"))
        # Yardline parse
        out["avg_start_own_ydln"] = parse_yardline(out.get("average_drive_start"))
        if out.get("average_drive_start") and out["avg_start_own_ydln"] is None:
            qc["violations"].append(f"Unparseable avg drive start for {side.get('team','?')}: {out.get('average_drive_start')}")
        return out

    away = coerce_fields(game_json["away"])
    home = coerce_fields(game_json["home"])

    # Logical constraints
    def ensure_ge(a: Optional[int], b: Optional[int], label: str):
        if a is not None and b is not None and a < b:
            qc["violations"].append(label)

    ensure_ge(away.get("red_zone_att"), away.get("red_zone_td"), "away.red_zone_att < away.red_zone_td")
    ensure_ge(home.get("red_zone_att"), home.get("red_zone_td"), "home.red_zone_att < home.red_zone_td")
    ensure_ge(away.get("third_down_att"), away.get("third_down_made"), "away.third_down_att < away.third_down_made")
    ensure_ge(home.get("third_down_att"), home.get("third_down_made"), "home.third_down_att < home.third_down_made")

    normalized = {
        "game": game_json["game"],
        "away": away,
        "home": home,
    }

    # Hard validation failures? If scores or plays missing entirely, skip
    hard_fail = (
        away.get("total_plays") is None or home.get("total_plays") is None or
        away.get("score") is None or home.get("score") is None
    )

    return (None if hard_fail else normalized), qc


# ---------------------------- Normalization to team-game rows ----------------------------

def stable_game_id(season: int, week: int, away_team: str, home_team: str) -> str:
    raw = f"{season}|{week}|{away_team}@{home_team}"
    return hashlib.sha1(raw.encode()).hexdigest()[:16]


def game_to_team_rows(game: Dict[str, Any], season: int, week: int) -> List[Dict[str, Any]]:
    g = game["game"]
    away = game["away"]
    home = game["home"]

    # Team identifiers
    away_team = g.get("away_team") or away.get("team")
    home_team = g.get("home_team") or home.get("team")
    # Prefer short names in away/home.team if present
    away_label = away.get("team") or away_team
    home_label = home.get("team") or home_team

    gid = stable_game_id(season, week, str(away_team), str(home_team))

    # Helper to build a row from side/opposite
    def build_row(side: Dict[str, Any], opp: Dict[str, Any], team: str, opp_team: str, home_away: str) -> Dict[str, Any]:
        pass_att = side.get("pass_attempts") or 0
        sacks = side.get("sacks_allowed") or 0
        dropbacks = pass_att + sacks
        pass_yds_net = side.get("passing_yards_net")
        pass_yds_gross = side.get("passing_yards_gross")
        rush_att = side.get("rushing_attempts") or 0
        rush_yds = side.get("rushing_yards")
        plays = side.get("total_plays") or 0
        yards = side.get("total_yards")
        drives = side.get("drives_total") or 0
        rz_td = side.get("red_zone_td")
        rz_att = side.get("red_zone_att")

        ypa = safe_div(pass_yds_gross, pass_att) if pass_att else None
        nyd = safe_div(pass_yds_net, dropbacks) if dropbacks else None
        sack_rate = safe_div(sacks, dropbacks) if dropbacks else None
        int_rate = safe_div(side.get("pass_interceptions"), pass_att) if pass_att else None
        third_pct = safe_div(side.get("third_down_made"), side.get("third_down_att")) if side.get("third_down_att") else None
        fourth_pct = None  # Not present in JSONs provided
        rz_td_pct = safe_div(rz_td, rz_att) if rz_att else None
        turnovers = (side.get("pass_interceptions") or 0) + (side.get("fumbles_lost") or 0)
        pen_yds_per_drive = safe_div(side.get("penalty_yards"), drives) if drives else None
        pts_per_drive = safe_div(side.get("score"), drives) if drives else None
        yds_per_drive = safe_div(yards, drives) if drives else None
        explosive_rate = safe_div(side.get("explosive_plays_15plus"), plays) if plays else None

        # Opponent mirrors
        opp_dropbacks = (opp.get("pass_attempts") or 0) + (opp.get("sacks_allowed") or 0)

        row = {
            # Keys
            "season": season,
            "week": week,
            "game_id": gid,
            "team": team,
            "opp": opp_team,
            "home_away": home_away,
            # Score
            "points_for": side.get("score"),
            "points_against": opp.get("score"),
            "point_diff": (None if side.get("score") is None or opp.get("score") is None else side.get("score") - opp.get("score")),
            # Volume
            "plays": side.get("total_plays"),
            "yards": yards,
            "ypp": side.get("yards_per_play"),
            # Rushing
            "rush_att": rush_att,
            "rush_yds": rush_yds,
            "rush_ypa": side.get("yards_per_rush"),
            # Passing
            "pass_att": pass_att,
            "pass_cmp": side.get("pass_completions"),
            "pass_int": side.get("pass_interceptions"),
            "sacks": sacks,
            "sack_yds": side.get("sack_yards_lost"),
            "pass_yds_gross": pass_yds_gross,
            "pass_yds_net": pass_yds_net,
            "dropbacks": dropbacks,
            "ypa": ypa,
            "ny_d": nyd,
            "sack_rate": sack_rate,
            "int_rate": int_rate,
            # Situational
            "third_made": side.get("third_down_made"),
            "third_att": side.get("third_down_att"),
            "third_pct": third_pct,
            "fourth_pct": fourth_pct,
            "rz_td": rz_td,
            "rz_att": rz_att,
            "rz_td_pct": rz_td_pct,
            # Ball security
            "fumbles": side.get("fumbles"),
            "fumbles_lost": side.get("fumbles_lost"),
            "turnovers": turnovers,
            # Discipline
            "pen": side.get("penalties"),
            "pen_yds": side.get("penalty_yards"),
            "pen_yds_per_drive": pen_yds_per_drive,
            # ST/Field
            "punts": side.get("punts"),
            "punt_avg": side.get("punt_average"),
            "kickoffs": side.get("kickoffs"),
            "kick_tb": side.get("kickoff_touchbacks"),
            # Possession & drives
            "top_s": side.get("time_of_possession_s"),
            "drives": drives,
            "avg_start_own_ydln": side.get("avg_start_own_ydln"),
            "pts_per_drive": pts_per_drive,
            "yds_per_drive": yds_per_drive,
            # Game flow
            "largest_lead": side.get("largest_lead"),
            "largest_deficit": side.get("largest_deficit"),
            "time_leading_s": side.get("time_leading_s"),
            "time_trailing_s": side.get("time_trailing_s"),
            "drives_leading": side.get("drives_leading"),
            "drives_trailing": side.get("drives_trailing"),
            # Explosives
            "explosive_15p": side.get("explosive_plays_15plus"),
            "explosive_rate": explosive_rate,
            # Opponent mirrors
            "opp_plays": opp.get("total_plays"),
            "opp_yards": opp.get("total_yards"),
            "opp_rush_att": opp.get("rushing_attempts"),
            "opp_rush_yds": opp.get("rushing_yards"),
            "opp_pass_att": opp.get("pass_attempts"),
            "opp_pass_yds_net": opp.get("passing_yards_net"),
            "opp_dropbacks": opp_dropbacks,
            "opp_sacks_allowed": opp.get("sacks_allowed"),
            "opp_pass_int": opp.get("pass_interceptions"),
            "opp_rz_td": opp.get("red_zone_td"),
            "opp_rz_att": opp.get("red_zone_att"),
            "opp_pen_yds": opp.get("penalty_yards"),
            "opp_drives": opp.get("drives_total"),
            "opp_explosive_15p": opp.get("explosive_plays_15plus"),
        }
        return row

    rows = [
        build_row(away, home, away_label, home_label, "A"),
        build_row(home, away, home_label, away_label, "H"),
    ]
    return rows


# ---------------------------- Ratings and ranks ----------------------------

def compute_unit_axes(df: pd.DataFrame) -> pd.DataFrame:
    """Add per-game raw axes for OFF/DEF allowed as specified."""
    out = df.copy()
    # Offense axes (raw)
    out["OFF_ppd"] = df["points_for"] / df["drives"].replace({0: np.nan})
    out["OFF_ypp"] = df["ypp"]
    out["OFF_rush_ypa"] = df["rush_yds"] / df["rush_att"].replace({0: np.nan})
    out["OFF_nyd"] = df["pass_yds_net"] / df["dropbacks"].replace({0: np.nan})
    out["OFF_sack_rate_inv"] = 1 - df["sack_rate"]
    out["OFF_int_rate_inv"] = 1 - df["int_rate"]
    out["OFF_rz_td_pct"] = df["rz_td_pct"]
    out["OFF_explosive_rate"] = df["explosive_rate"]
    out["OFF_pen_yds_pdrv"] = df["pen_yds_per_drive"]

    # Defense allowed axes (use opponent mirrors)
    out["DEF_ppd_allowed"] = df["points_against"] / df["opp_drives"].replace({0: np.nan})
    out["DEF_ypp_allowed"] = df["opp_yards"] / df["opp_plays"].replace({0: np.nan})
    out["DEF_rush_ypa_allowed"] = df["opp_rush_yds"] / df["opp_rush_att"].replace({0: np.nan})
    out["DEF_nyd_allowed"] = df["opp_pass_yds_net"] / df["opp_dropbacks"].replace({0: np.nan})
    # Pressure proxy from opponent's sack rate
    opp_sack_rate = df["opp_sacks_allowed"] / df["opp_dropbacks"].replace({0: np.nan})
    out["DEF_pressure_proxy"] = opp_sack_rate
    out["DEF_int_rate"] = df["opp_pass_int"] / df["opp_pass_att"].replace({0: np.nan})
    out["DEF_rz_td_pct_allowed"] = df["opp_rz_td"] / df["opp_rz_att"].replace({0: np.nan})
    out["DEF_explosive_rate_all"] = df["opp_explosive_15p"] / df["opp_plays"].replace({0: np.nan})
    out["DEF_pen_yds_pdrv_all"] = df["opp_pen_yds"] / df["opp_drives"].replace({0: np.nan})
    return out


def opponent_adjust(df_axes: pd.DataFrame) -> pd.DataFrame:
    """Compute opponent-adjusted values per game using leak-proof prior weeks only.
    Assumes df_axes has season, week, team, opp, and the raw axes columns.
    """
    df = df_axes.copy()
    metrics_off = [
        "OFF_ppd","OFF_ypp","OFF_rush_ypa","OFF_nyd","OFF_sack_rate_inv","OFF_int_rate_inv",
        "OFF_rz_td_pct","OFF_explosive_rate","OFF_pen_yds_pdrv",
    ]
    metrics_def_allowed = [
        "DEF_ppd_allowed","DEF_ypp_allowed","DEF_rush_ypa_allowed","DEF_nyd_allowed",
        "DEF_pressure_proxy","DEF_int_rate","DEF_rz_td_pct_allowed","DEF_explosive_rate_all","DEF_pen_yds_pdrv_all",
    ]

    df.sort_values(["season","week","game_id","team"], inplace=True)

    # Precompute league offensive means by week cutoff for defense adjustment
    def league_off_means_up_to(week: int) -> Dict[str, float]:
        prev = df[df["week"] < week]
        return {m: prev[m].mean(skipna=True) for m in metrics_off}

    adj_records: List[Dict[str, Any]] = []
    weeks = sorted(df["week"].dropna().unique().tolist())
    for w in weeks:
        # Data up to previous weeks only
        prev = df[df["week"] < w]
        league_off_means = league_off_means_up_to(w)
        week_df = df[df["week"] == w]
        for _, row in week_df.iterrows():
            rec = row.to_dict()
            opp = row["opp"]
            # Opponent means allowed to others before this game
            opp_prev = prev[prev["team"] == opp]
            # For offense adj, for each metric, compute mean of what other teams produced vs opp previously
            for m in metrics_off:
                raw = row.get(m)
                mean_allow = opp_prev[m].mean(skipna=True)
                if np.isnan(mean_allow):
                    # Fallback to league mean of that offensive metric vs all opponents prior to week
                    mean_allow = prev[m].mean(skipna=True)
                rec[m + "_adj"] = (None if pd.isna(raw) or pd.isna(mean_allow) else raw - mean_allow)
            # Defense allowed adj: league mean offense - raw allowed
            for m in metrics_def_allowed:
                raw_allowed = row.get(m)
                league_mean_off = league_off_means.get(_map_def_to_off_equiv(m), np.nan)
                # For pressure proxy, use inverse sense later; keep as proxy here
                if pd.isna(league_mean_off):
                    league_mean_off = prev[_map_def_to_off_equiv(m)].mean(skipna=True) if not prev.empty else np.nan
                rec[m + "_adj"] = (None if pd.isna(raw_allowed) or pd.isna(league_mean_off) else league_mean_off - raw_allowed)
            adj_records.append(rec)
    return pd.DataFrame(adj_records)


def _map_def_to_off_equiv(def_metric: str) -> str:
    mapping = {
        "DEF_ppd_allowed": "OFF_ppd",
        "DEF_ypp_allowed": "OFF_ypp",
        "DEF_rush_ypa_allowed": "OFF_rush_ypa",
        "DEF_nyd_allowed": "OFF_nyd",
        "DEF_pressure_proxy": "OFF_sack_rate_inv",  # inverse relationship proxy
        "DEF_int_rate": "OFF_int_rate_inv",
        "DEF_rz_td_pct_allowed": "OFF_rz_td_pct",
        "DEF_explosive_rate_all": "OFF_explosive_rate",
        "DEF_pen_yds_pdrv_all": "OFF_pen_yds_pdrv",
    }
    return mapping.get(def_metric, "OFF_ypp")


def compute_ewmas(df_adj: pd.DataFrame) -> pd.DataFrame:
    """Roll per-team EWMA for adjusted and raw axes."""
    df = df_adj.copy().sort_values(["season","team","week"])  
    cols_adj = [c for c in df.columns if c.endswith("_adj")]
    cols_raw = [c for c in df.columns if re.match(r"^(OFF|DEF)_", c) and not c.endswith("_adj")]

    out_records: List[Dict[str, Any]] = []
    for team, tdf in df.groupby("team"):
        prev_adj = {c: 0.0 for c in cols_adj}
        prev_raw = {c: 0.0 for c in cols_raw}
        games_played = 0
        for _, row in tdf.iterrows():
            games_played += 1
            rec = {"season": row["season"], "as_of_week": row["week"], "team": team, "games_played": games_played, "recency_weight": LAMBDA}
            # Adjusted EWMA
            for c in cols_adj:
                val = row.get(c)
                if pd.isna(val):
                    val = 0.0
                prev_adj[c] = LAMBDA * val + (1 - LAMBDA) * prev_adj[c]
                rec[c.replace("_adj", "_rating")] = prev_adj[c]
            # Raw EWMA (sanity)
            for c in cols_raw:
                val = row.get(c)
                if pd.isna(val):
                    val = 0.0
                prev_raw[c] = LAMBDA * val + (1 - LAMBDA) * prev_raw[c]
                rec[c + "_raw_ewma"] = prev_raw[c]
            out_records.append(rec)
    return pd.DataFrame(out_records)


def zscores_and_ranks(df_ratings: pd.DataFrame, as_of_week: Optional[int] = None) -> pd.DataFrame:
    """Compute z-scores and ranks at given as_of_week (or latest if None) with sign conventions."""
    if as_of_week is None:
        as_of_week = int(df_ratings["as_of_week"].max())
    latest = df_ratings[df_ratings["as_of_week"] == as_of_week].copy()

    # Choose rating columns
    rating_cols = [c for c in latest.columns if c.endswith("_rating")]

    # Z-scores
    for c in rating_cols:
        mu = latest[c].mean()
        sd = latest[c].std()
        if sd == 0 or pd.isna(sd):
            latest[c + "_z"] = 0.0
        else:
            latest[c + "_z"] = (latest[c] - mu) / sd

    # Derived strengths (defense inverted for allowed metrics)
    def_strengths = {
        "DEF_strength_pass": -latest.get("DEF_nyd_allowed_rating_z", pd.Series(0, index=latest.index)),
        "DEF_strength_rush": -latest.get("DEF_rush_ypa_allowed_rating_z", pd.Series(0, index=latest.index)),
        "DEF_strength_ppd": -latest.get("DEF_ppd_allowed_rating_z", pd.Series(0, index=latest.index)),
        "DEF_explosive_prevent": -latest.get("DEF_explosive_rate_all_rating_z", pd.Series(0, index=latest.index)),
        "DEF_rz_prevent": -latest.get("DEF_rz_td_pct_allowed_rating_z", pd.Series(0, index=latest.index)),
    }
    for k, v in def_strengths.items():
        latest[k] = v

    # Pressure: if only proxy exists
    if "DEF_pressure_proxy_rating_z" in latest.columns:
        latest["DEF_pressure"] = (-(latest["DEF_pressure_proxy_rating_z"]))

    # Ranks (1 = best) for key axes
    latest["rank_OFF_pass"] = latest["OFF_nyd_rating"].rank(ascending=False, method="min").astype(int)
    latest["rank_OFF_rush"] = latest["OFF_rush_ypa_rating"].rank(ascending=False, method="min").astype(int)
    latest["rank_DEF_pass"] = latest["DEF_strength_pass"].rank(ascending=False, method="min").astype(int)
    latest["rank_DEF_rush"] = latest["DEF_strength_rush"].rank(ascending=False, method="min").astype(int)

    latest["as_of_week"] = as_of_week
    return latest


def build_holes_and_levers(df_ranks: pd.DataFrame, df_ratings_latest: pd.DataFrame, team_game: pd.DataFrame) -> pd.DataFrame:
    # Plays per game EWM (from team_game)
    plays_ewm = team_game.groupby("team")["plays"].apply(lambda s: s.ewm(alpha=LAMBDA, adjust=False).mean().iloc[-1] if len(s) else np.nan)
    pg = plays_ewm.reindex(df_ranks["team"]).values

    out = df_ranks[["season","as_of_week","team","rank_OFF_pass","rank_OFF_rush","rank_DEF_pass","rank_DEF_rush"]].copy()

    # Helper to get z cols safely
    z = df_ratings_latest.set_index("team")
    def zcol(c: str):
        return z[c] if c in z.columns else pd.Series(0, index=z.index)

    out["lever_explosive_pass"] = zcol("OFF_explosive_rate_rating_z").values
    out["lever_explosive_overall"] = (0.6*zcol("OFF_explosive_rate_rating") + 0.4*zcol("OFF_nyd_rating")).values
    out["lever_rz"] = zcol("OFF_rz_td_pct_rating_z").values
    out["lever_protection"] = zcol("OFF_sack_rate_inv_rating_z").values
    out["lever_ppd"] = zcol("OFF_ppd_rating_z").values
    out["lever_play_volume"] = pd.Series(pg, index=out.index)

    out["hole_explosive_pass"] = zcol("DEF_explosive_rate_all_rating_z").values
    out["hole_pass_eff"] = zcol("DEF_nyd_allowed_rating_z").values
    out["hole_rush_eff"] = zcol("DEF_rush_ypa_allowed_rating_z").values
    out["hole_rz"] = zcol("DEF_rz_td_pct_allowed_rating_z").values
    out["hole_ppd"] = zcol("DEF_ppd_allowed_rating_z").values
    out["hole_pressure"] = (-zcol("DEF_pressure")).values if "DEF_pressure" in z.columns else (-zcol("DEF_pressure_proxy_rating_z")).values
    out["hole_penalty_fp"] = zcol("DEF_pen_yds_pdrv_all_rating_z").values

    return out


# ---------------------------- QC ----------------------------

def qc_checks(team_game: pd.DataFrame) -> pd.DataFrame:
    issues: List[Dict[str, Any]] = []
    for _, r in team_game.iterrows():
        # Recompute ypp, rz_td_pct, third_pct
        if not pd.isna(r.get("yards")) and not pd.isna(r.get("plays")) and r.get("plays"):
            ypp = r["yards"] / r["plays"]
            if not np.isclose(ypp, r.get("ypp", np.nan), atol=1e-6, equal_nan=True):
                issues.append({"game_id": r["game_id"], "team": r["team"], "field": "ypp", "reported": r.get("ypp"), "recomputed": ypp})
        if not pd.isna(r.get("rz_att")) and r.get("rz_att"):
            rz = r["rz_td"] / r["rz_att"]
            if not np.isclose(rz, r.get("rz_td_pct", np.nan), atol=1e-6, equal_nan=True):
                issues.append({"game_id": r["game_id"], "team": r["team"], "field": "rz_td_pct", "reported": r.get("rz_td_pct"), "recomputed": rz})
        if not pd.isna(r.get("third_att")) and r.get("third_att"):
            tp = r["third_made"] / r["third_att"]
            if not np.isclose(tp, r.get("third_pct", np.nan), atol=1e-6, equal_nan=True):
                issues.append({"game_id": r["game_id"], "team": r["team"], "field": "third_pct", "reported": r.get("third_pct"), "recomputed": tp})
        # Time leading/trailing sanity: optional, since game length unknown here
    return pd.DataFrame(issues)


# ---------------------------- Main entry ----------------------------

def _infer_week_for_game(path: Path, data: Dict[str, Any], default_week: int) -> int:
    """Infer week number from filename or teams. Defaults to provided week if unknown.
    Week 2 if matchup is Vikings-Bears or Commanders-Packers, else default_week.
    """
    fname = path.name.lower()
    away_raw = (data.get("away", {}) or {}).get("team") or (data.get("game", {}) or {}).get("away_team", "")
    home_raw = (data.get("home", {}) or {}).get("team") or (data.get("game", {}) or {}).get("home_team", "")
    away = str(away_raw).lower().strip()
    home = str(home_raw).lower().strip()
    pair = (away, home)
    week2_pairs = {
        ("vikings", "bears"),
        ("commanders", "packers"),
        ("washington commanders", "green bay packers"),
    }
    # Also handle filename hints
    if "vikings" in fname and "bears" in fname:
        return 2
    if "commanders" in fname and "packers" in fname:
        return 2
    if pair in week2_pairs:
        return 2
    return default_week


def build_pipeline(season: int = 2025, week: int = 1) -> Dict[str, str]:
    # Load MD JSON files (include all .md, including generic gamebook.md)
    files = sorted([p for p in GAMEBOOK_DIR.glob("*.md")])
    games_with_week: List[Tuple[Dict[str, Any], int]] = []
    qc_rows: List[Dict[str, Any]] = []

    for p in files:
        try:
            with open(p, "r", encoding="utf-8") as f:
                data = json.load(f)
            norm, qc = validate_and_normalize_game(data)
            qc_rows.append({"file": p.name, **qc})
            if norm is not None:
                wk = _infer_week_for_game(p, data, week)
                games_with_week.append((norm, wk))
        except Exception as e:
            qc_rows.append({"file": p.name, "missing_fields": ["file_read_or_json"], "violations": [str(e)], "notes": []})

    # Normalize to team-game rows
    rows: List[Dict[str, Any]] = []
    for g, wk in games_with_week:
        rows.extend(game_to_team_rows(g, season=season, week=wk))
    team_game = pd.DataFrame(rows)

    # Write curated/team_game.parquet fresh from current files to avoid stale weeks
    curated_path = CURATED_DIR / "team_game.parquet"
    combined = team_game.sort_values(["season","week","game_id","team"]).drop_duplicates(["season","week","game_id","team"], keep="last")
    out_curated = write_parquet_or_csv(combined, curated_path, index=False, key_cols=["season","week","game_id","team"])

    # Compute axes and adjustments
    axes = compute_unit_axes(combined)
    adj = opponent_adjust(axes)
    ratings = compute_ewmas(adj)

    # Upsert ratings
    ratings_path = MODELS_DIR / "team_unit_ratings.parquet"
    out_ratings = write_parquet_or_csv(ratings, ratings_path, index=False, key_cols=["season","as_of_week","team"])

    # Z-scores and ranks at selected week (default latest)
    ranks_latest = zscores_and_ranks(ratings, as_of_week=week)
    # Also compute latest snapshot to keep default outputs fresh
    ranks_latest_actual = zscores_and_ranks(ratings, as_of_week=int(ratings["as_of_week"].max())) if not ratings.empty else ranks_latest

    # Write generic (latest) and week-specific snapshots
    ranks_path = MODELS_DIR / "team_ranks.parquet"
    out_ranks = write_parquet_or_csv(ranks_latest_actual, ranks_path, index=False, key_cols=["season","as_of_week","team"])
    ranks_w_path = MODELS_DIR / f"team_ranks_w{week}.parquet"
    _ = write_parquet_or_csv(ranks_latest, ranks_w_path, index=False, key_cols=["season","as_of_week","team"])

    # Holes and levers
    holes_levers = build_holes_and_levers(ranks_latest, ratings[ratings["as_of_week"] == week], combined)
    hl_path = MODELS_DIR / "holes_and_levers.parquet"
    # generic latest
    holes_levers_actual = build_holes_and_levers(ranks_latest_actual, ratings[ratings["as_of_week"] == ranks_latest_actual["as_of_week"].max()], combined)
    out_hl = write_parquet_or_csv(holes_levers_actual, hl_path, index=False, key_cols=["season","as_of_week","team"])
    hl_w_path = MODELS_DIR / f"holes_and_levers_w{week}.parquet"
    _ = write_parquet_or_csv(holes_levers, hl_w_path, index=False, key_cols=["season","as_of_week","team"])

    # Findings JSONs for requested week
    asof_week = int(ranks_latest["as_of_week"].max()) if not ranks_latest.empty else week
    out_findings_dir = FINDINGS_DIR / f"asof_week_{asof_week}"
    out_findings_dir.mkdir(parents=True, exist_ok=True)
    for _, r in ranks_latest.iterrows():
        team = r["team"]
        summary = {
            "off_rank_pass": int(r.get("rank_OFF_pass", np.nan)) if not pd.isna(r.get("rank_OFF_pass")) else None,
            "off_rank_rush": int(r.get("rank_OFF_rush", np.nan)) if not pd.isna(r.get("rank_OFF_rush")) else None,
            "def_rank_pass": int(r.get("rank_DEF_pass", np.nan)) if not pd.isna(r.get("rank_DEF_pass")) else None,
            "def_rank_rush": int(r.get("rank_DEF_rush", np.nan)) if not pd.isna(r.get("rank_DEF_rush")) else None,
        }
        findings = {
            "as_of_week": asof_week,
            "team": team,
            "summary": summary,
            "levers": [],
            "holes": [],
            "stability": {"games": int(ratings[ratings["team"] == team]["as_of_week"].count()), "recency_lambda": LAMBDA},
            "qc_flags": []
        }
        with open(out_findings_dir / f"team_{team}.json", "w", encoding="utf-8") as f:
            json.dump(findings, f, indent=2)

    # QC
    qc_df = pd.DataFrame(qc_rows)
    qc_issues = qc_checks(combined)
    qc_out_path = QC_DIR / "qc_issues.parquet"
    _ = write_parquet_or_csv(qc_issues, qc_out_path, index=False)
    QC_DIR.mkdir(parents=True, exist_ok=True)
    meta = {"built_at": pd.Timestamp.utcnow().isoformat(), "games_through_week": asof_week, "lambda": LAMBDA}
    with open(QC_DIR / "meta.json", "w", encoding="utf-8") as f:
        json.dump(meta, f, indent=2)

    return {
        "curated": out_curated,
        "ratings": out_ratings,
        "ranks": out_ranks,
        "holes_levers": out_hl,
        "findings_dir": str(out_findings_dir),
        "qc_issues": str(qc_out_path),
    }


if __name__ == "__main__":
    import argparse
    ap = argparse.ArgumentParser(description="Build team metrics pipeline from JSON gamebook MD files")
    ap.add_argument("--season", type=int, default=2025)
    ap.add_argument("--week", type=int, default=1)
    args = ap.parse_args()
    outputs = build_pipeline(season=args.season, week=args.week)
    print("Pipeline complete. Outputs:")
    for k, v in outputs.items():
        print(f"- {k}: {v}")

