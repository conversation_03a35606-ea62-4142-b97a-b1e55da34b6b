#!/usr/bin/env python3
"""
Enhanced projection engine with improved logic from DET vs GB analysis.
Integrates player props, depth charts, and game context for more accurate projections.
"""

import pandas as pd
import numpy as np
import json
from pathlib import Path
from typing import Dict, List, Tuple, Any, Optional

from .game_context import GameContextEngine


class EnhancedProjectionEngine:
    """Enhanced projection engine with improved player props and context logic."""
    
    def __init__(self):
        self.game_context_engine = GameContextEngine()
        self.team_name_mappings = {}
        
    def load_data(self, depth_file: str, props_file: str, odds_file: str):
        """Load all necessary data files."""
        print("Loading projection data...")
        
        # Load depth charts
        self.depth_df = pd.read_parquet(depth_file)
        print(f"Loaded {len(self.depth_df)} depth chart entries")
        
        # Load player props
        self.props_df = pd.read_parquet(props_file)
        print(f"Loaded {len(self.props_df)} player props")
        
        # Load odds data
        self.game_context_engine.load_odds_data(odds_file)
        
        # Create team name mappings
        self._create_team_mappings()
        
    def _create_team_mappings(self):
        """Create mappings between full team names and abbreviations."""
        # Standard NFL team mappings
        self.team_name_mappings = {
            'Arizona Cardinals': 'ARI',
            'Atlanta Falcons': 'ATL', 
            'Baltimore Ravens': 'BAL',
            'Buffalo Bills': 'BUF',
            'Carolina Panthers': 'CAR',
            'Chicago Bears': 'CHI',
            'Cincinnati Bengals': 'CIN',
            'Cleveland Browns': 'CLE',
            'Dallas Cowboys': 'DAL',
            'Denver Broncos': 'DEN',
            'Detroit Lions': 'DET',
            'Green Bay Packers': 'GB',
            'Houston Texans': 'HOU',
            'Indianapolis Colts': 'IND',
            'Jacksonville Jaguars': 'JAX',
            'Kansas City Chiefs': 'KC',
            'Las Vegas Raiders': 'LV',
            'Los Angeles Chargers': 'LAC',
            'Los Angeles Rams': 'LAR',
            'Miami Dolphins': 'MIA',
            'Minnesota Vikings': 'MIN',
            'New England Patriots': 'NE',
            'New Orleans Saints': 'NO',
            'New York Giants': 'NYG',
            'New York Jets': 'NYJ',
            'Philadelphia Eagles': 'PHI',
            'Pittsburgh Steelers': 'PIT',
            'San Francisco 49ers': 'SF',
            'Seattle Seahawks': 'SEA',
            'Tampa Bay Buccaneers': 'TB',
            'Tennessee Titans': 'TEN',
            'Washington Commanders': 'WAS'
        }
        
    def get_base_projection(self, position: str, depth_order: int, is_starter: bool) -> float:
        """Get enhanced base projection by position and depth."""
        # Improved base projections based on DET vs GB analysis
        if position == 'QB':
            if depth_order == 1:
                return 19.5  # Starting QB
            else:
                return 4.0   # Backup QB
        elif position == 'RB':
            if depth_order == 1:
                return 14.2  # RB1
            elif depth_order == 2:
                return 8.5   # RB2
            else:
                return 3.5   # Deep backup
        elif position == 'WR':
            if depth_order == 1:
                return 13.8  # WR1
            elif depth_order == 2:
                return 10.2  # WR2
            elif depth_order == 3:
                return 6.8   # WR3
            else:
                return 4.2   # WR4+
        elif position == 'TE':
            if depth_order == 1:
                return 9.5   # TE1
            elif depth_order == 2:
                return 4.8   # TE2
            else:
                return 2.5   # Deep TE
        elif position == 'DEF':
            return 8.0  # Team defense base
        
        return 2.0  # Default
        
    def adjust_for_props(self, base_proj: float, player_props: pd.DataFrame, position: str) -> float:
        """Enhanced player props adjustment logic."""
        if len(player_props) == 0:
            return base_proj
        
        adjustment = 1.0
        
        # Position-specific props logic
        if position == 'QB':
            pass_yards = player_props[player_props['market'] == 'pass_yards']
            if len(pass_yards) > 0:
                line = pass_yards['line'].iloc[0]
                if line > 260:
                    adjustment *= 1.25  # High volume passer
                elif line > 230:
                    adjustment *= 1.1
                elif line < 200:
                    adjustment *= 0.85  # Low volume
        
        elif position == 'RB':
            rush_yards = player_props[player_props['market'] == 'rush_yards']
            rec_yards = player_props[player_props['market'] == 'rec_yards']
            
            total_yards_line = 0
            if len(rush_yards) > 0:
                total_yards_line += rush_yards['line'].iloc[0]
            if len(rec_yards) > 0:
                total_yards_line += rec_yards['line'].iloc[0]
                
            if total_yards_line > 90:
                adjustment *= 1.3   # High usage RB
            elif total_yards_line > 60:
                adjustment *= 1.15
            elif total_yards_line < 30:
                adjustment *= 0.8
        
        elif position in ['WR', 'TE']:
            rec_yards = player_props[player_props['market'] == 'rec_yards']
            
            if len(rec_yards) > 0:
                yards_line = rec_yards['line'].iloc[0]
                if yards_line > 70:
                    adjustment *= 1.35  # High target share
                elif yards_line > 50:
                    adjustment *= 1.2
                elif yards_line < 25:
                    adjustment *= 0.75
        
        # TD props boost
        td_props = player_props[player_props['market'] == 'td_anytime']
        if len(td_props) > 0:
            adjustment *= 1.1
        
        return base_proj * adjustment
        
    def apply_enhanced_game_context(self, base_proj: float, team_abbr: str, position: str,
                                   game_context: Dict[str, Any]) -> float:
        """Apply enhanced game context adjustments including weather."""
        adjustment = 1.0

        # Get team's implied total
        is_home = game_context.get('home_team', '').endswith(team_abbr) or team_abbr in game_context.get('home_team', '')
        team_total = game_context.get('home_total' if is_home else 'away_total', 22.5)
        spread = game_context.get('home_spread' if is_home else 'away_spread', 0.0)
        game_script = game_context.get('game_script', 'competitive')

        # Team total adjustments (more nuanced than before)
        if team_total > 25:
            adjustment *= 1.2   # Increased for high-scoring games
        elif team_total > 23:
            adjustment *= 1.1
        elif team_total < 20:
            adjustment *= 0.85
        elif team_total < 22:
            adjustment *= 0.9

        # Weather adjustments
        weather = game_context.get('weather', {})
        if weather:
            adjustment *= self._apply_weather_adjustments(position, weather)
        
        # Position-specific game script adjustments
        if game_script == 'competitive':
            if position == 'QB':
                adjustment *= 1.05  # More passing in competitive games
            elif position == 'RB':
                adjustment *= 1.02  # Balanced attack
        elif game_script == 'high_scoring':
            if position in ['QB', 'WR', 'TE']:
                adjustment *= 1.1   # More passing in shootouts
            elif position == 'RB':
                adjustment *= 0.95  # Less running
        elif game_script == 'low_scoring':
            if position == 'RB':
                adjustment *= 1.08  # More running in low-scoring games
            elif position in ['WR', 'TE']:
                adjustment *= 0.9   # Less passing volume

        # Spread adjustments (favored teams get slight boost)
        if abs(spread) > 0.5:  # Meaningful spread
            if spread > 0:  # Team is favored
                adjustment *= 1.03
            else:  # Team is underdog
                if position == 'QB':
                    adjustment *= 1.08  # Underdogs throw more (increased)
                elif position in ['WR', 'TE']:
                    adjustment *= 1.05  # More targets for receivers

        return base_proj * adjustment

    def _apply_weather_adjustments(self, position: str, weather: Dict[str, Any]) -> float:
        """Apply weather-based adjustments to projections."""
        adjustment = 1.0

        conditions = weather.get('conditions', '')
        temperature = weather.get('temperature', 70)
        wind_speed = weather.get('wind_speed', 0)
        precipitation = weather.get('precipitation', '')

        # Cold weather adjustments (below 32°F)
        if temperature < 32:
            if position == 'QB':
                adjustment *= 0.95  # Harder to throw accurately
            elif position == 'RB':
                adjustment *= 1.05  # More rushing in cold weather
            elif position in ['WR', 'TE']:
                adjustment *= 0.92  # Harder to catch in cold

        # Wind adjustments (above 15 mph)
        if wind_speed > 15:
            if position == 'QB':
                adjustment *= 0.9   # Wind affects passing
            elif position in ['WR', 'TE']:
                adjustment *= 0.88  # Wind affects receiving

        # Precipitation adjustments
        if precipitation in ['rain', 'snow', 'light_snow']:
            if position == 'QB':
                adjustment *= 0.93  # Wet ball harder to throw
            elif position == 'RB':
                adjustment *= 1.08  # More running in bad weather
            elif position in ['WR', 'TE']:
                adjustment *= 0.9   # Harder to catch wet ball

        # Combined severe weather conditions
        if conditions in ['cold_windy', 'stormy', 'blizzard']:
            if position in ['QB', 'WR', 'TE']:
                adjustment *= 0.85  # Significant passing game impact
            elif position == 'RB':
                adjustment *= 1.15  # Heavy reliance on running game

        return adjustment
        
    def create_projections_for_teams(self, team_list: List[str]) -> pd.DataFrame:
        """Create projections for specified teams using enhanced logic."""
        print(f"Creating enhanced projections for teams: {team_list}")
        
        projections = []
        
        # Convert team abbreviations to full names for depth chart lookup
        full_team_names = []
        for abbr in team_list:
            full_name = next((k for k, v in self.team_name_mappings.items() if v == abbr), abbr)
            full_team_names.append(full_name)
        
        # Filter depth chart for target teams
        team_depth = self.depth_df[self.depth_df['team'].isin(full_team_names)].copy()
        team_props = self.props_df[self.props_df['team'].isin(team_list)].copy()
        
        print(f"Found {len(team_depth)} players in depth charts")
        print(f"Found {len(team_props)} player props")
        
        # Process each player
        for _, player in team_depth.iterrows():
            team_full = player['team']
            team_abbr = self.team_name_mappings.get(team_full, team_full)
            player_name = player['player_name']
            position = player['position']
            depth_order = player['depth_chart_order']
            is_starter = player['is_starter']
            
            # Skip non-fantasy relevant positions
            if position not in ['QB', 'RB', 'WR', 'TE']:
                continue
            
            # Get base projection
            base_proj = self.get_base_projection(position, depth_order, is_starter)
            
            # Apply player props adjustments
            player_props = team_props[team_props['player_name'] == player_name]
            if len(player_props) > 0:
                base_proj = self.adjust_for_props(base_proj, player_props, position)
            
            # Find opponent and get game context
            opponent = self._find_opponent_for_team(team_abbr, team_list)
            if opponent:
                game_context = self.game_context_engine.extract_game_context(team_abbr, opponent)
                if game_context:
                    final_proj = self.apply_enhanced_game_context(base_proj, team_abbr, position, game_context)
                else:
                    final_proj = base_proj
            else:
                final_proj = base_proj
            
            projections.append({
                'player_name': player_name,
                'team': team_abbr,
                'position': position,
                'depth_order': depth_order,
                'is_starter': is_starter,
                'proj_mean': round(final_proj, 2),
                'p50': round(final_proj, 2),
                'p75': round(final_proj * 1.2, 2),
                'p90': round(final_proj * 1.4, 2),
                'confidence': 0.8 if len(player_props) > 0 else 0.6
            })
        
        # Add team defense projections
        defense_projections = self._create_defense_projections(team_list)
        if len(defense_projections) > 0:
            projections.extend(defense_projections)

        return pd.DataFrame(projections)

    def _create_defense_projections(self, team_list: List[str]) -> List[Dict[str, Any]]:
        """Create team defense projections."""
        defense_projections = []

        # Base defense projections by team (can be enhanced with historical data)
        defense_bases = {
            'DET': 8.5, 'GB': 7.2, 'SF': 9.1, 'SEA': 7.8,
            'TEN': 6.9, 'DEN': 8.3, 'HOU': 7.5, 'LAR': 7.0,
            # Add more teams as needed
        }

        for team in team_list:
            base_def = defense_bases.get(team, 7.5)  # Default defense projection

            # Apply game context to defense
            opponent = self._find_opponent_for_team(team, team_list)
            if opponent:
                game_context = self.game_context_engine.extract_game_context(team, opponent)
                if game_context:
                    # Defense benefits from opponent having lower total
                    is_home = game_context.get('home_team', '').endswith(team) or team in game_context.get('home_team', '')
                    opp_total = game_context.get('away_total' if is_home else 'home_total', 22.5)

                    # Lower opponent total = better defense projection
                    if opp_total < 20:
                        final_def = base_def * 1.2
                    elif opp_total < 22:
                        final_def = base_def * 1.1
                    elif opp_total > 26:
                        final_def = base_def * 0.85
                    elif opp_total > 24:
                        final_def = base_def * 0.92
                    else:
                        final_def = base_def
                else:
                    final_def = base_def
            else:
                final_def = base_def

            defense_projections.append({
                'player_name': f'{team} Defense',
                'team': team,
                'position': 'DEF',
                'depth_order': 1,
                'is_starter': True,
                'proj_mean': round(final_def, 2),
                'p50': round(final_def, 2),
                'p75': round(final_def * 1.15, 2),
                'p90': round(final_def * 1.3, 2),
                'confidence': 0.7
            })

        return defense_projections

    def _find_opponent_for_team(self, team: str, team_list: List[str]) -> Optional[str]:
        """Find opponent for a team within the provided team list."""
        # This is a simplified approach - in practice you'd use schedule data
        # For now, assume teams in pairs are opponents
        team_pairs = [
            ('DET', 'GB'), ('GB', 'DET'),
            ('TEN', 'DEN'), ('DEN', 'TEN'),
            ('SF', 'SEA'), ('SEA', 'SF'),
            ('HOU', 'LAR'), ('LAR', 'HOU')
        ]

        for t1, t2 in team_pairs:
            if team == t1 and t2 in team_list:
                return t2
            elif team == t2 and t1 in team_list:
                return t1

        return None

    def generate_projections(self, team_list: List[str], output_file: Optional[str] = None) -> pd.DataFrame:
        """Main method to generate enhanced projections for specified teams."""
        print("=== ENHANCED PROJECTION ENGINE ===")

        # Create projections
        projections_df = self.create_projections_for_teams(team_list)

        # Sort by projection descending
        projections_df = projections_df.sort_values('proj_mean', ascending=False)

        print(f"\nGenerated {len(projections_df)} projections")
        print("\nTop 10 projections:")
        print(projections_df[['player_name', 'team', 'position', 'proj_mean']].head(10).to_string(index=False))

        # Save if output file specified
        if output_file:
            projections_df.to_csv(output_file, index=False)
            print(f"\nProjections saved to {output_file}")

        return projections_df
