"""Prop-driven projection system that maximizes signal extraction from betting markets."""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from .prop_signal_extractor import PropSignalExtractor, PropSignal
from .advanced_game_context import AdvancedGameContextAnalyzer, GameContext


@dataclass
class PlayerProjection:
    """Enhanced player projection with prop-derived insights."""
    player_name: str
    position: str
    team: str
    
    # Core projections
    fantasy_points: float
    floor: float
    ceiling: float
    
    # Component projections
    volume_projection: Dict[str, float]  # targets, carries, attempts
    efficiency_projection: Dict[str, float]  # ypr, ypc, ypa
    scoring_projection: Dict[str, float]  # td_prob, rz_share
    
    # Market-derived confidence
    projection_confidence: float  # 0-1 based on prop market depth
    sharp_money_alignment: float  # How aligned with sharp money
    
    # Game script sensitivity
    script_sensitivity: Dict[str, float]  # How much each script affects projection


class PropDrivenProjector:
    """Create projections primarily driven by prop market signals."""
    
    def __init__(self):
        self.prop_extractor = PropSignalExtractor()
        self.context_analyzer = AdvancedGameContextAnalyzer()
        
        # DraftKings scoring
        self.scoring_system = {
            'pass_yards': 0.04, 'pass_tds': 4, 'interceptions': -1,
            'rush_yards': 0.1, 'rush_tds': 6,
            'rec_yards': 0.1, 'receptions': 1, 'rec_tds': 6,
            'fumbles': -1
        }
    
    def parse_props_from_text(self, props_text: str) -> List[Dict]:
        """Parse props from the text format in your file."""
        props = []
        
        # Split the first line by spaces and parse prop data
        lines = props_text.strip().split('\n')
        if not lines:
            return props
            
        first_line = lines[0]
        
        # This is a simplified parser - you may need to adjust based on exact format
        parts = first_line.split(' ')
        
        i = 0
        while i < len(parts):
            part = parts[i]
            if ',' in part:
                # Try to parse as prop: player_name,market,line,over_odds,under_odds
                prop_parts = part.split(',')
                if len(prop_parts) >= 5:
                    try:
                        props.append({
                            'player_name': prop_parts[0],
                            'market': prop_parts[1],
                            'line': float(prop_parts[2]),
                            'over_odds': int(prop_parts[3]),
                            'under_odds': int(prop_parts[4])
                        })
                    except (ValueError, IndexError):
                        pass
            i += 1
        
        return props
    
    def aggregate_player_signals(self, player_signals: List[PropSignal]) -> Dict[str, float]:
        """Aggregate multiple prop signals for a player into core projections."""
        volume_signals = {}
        efficiency_signals = {}
        scoring_signals = {}
        
        # Aggregate by signal type
        for signal in player_signals:
            if signal.volume_signal > 0:
                if signal.market in ['pass_att', 'completions']:
                    volume_signals['pass_attempts'] = signal.volume_signal
                elif signal.market in ['rush_att']:
                    volume_signals['rush_attempts'] = signal.volume_signal
                elif signal.market in ['receptions']:
                    volume_signals['targets'] = signal.volume_signal / 0.68  # Assume catch rate
                elif 'rec_yards' in signal.market:
                    volume_signals['targets'] = signal.volume_signal
            
            if signal.efficiency_signal > 0:
                if 'pass' in signal.market:
                    efficiency_signals['ypa'] = signal.efficiency_signal
                elif 'rush' in signal.market:
                    efficiency_signals['ypc'] = signal.efficiency_signal
                elif 'rec' in signal.market:
                    efficiency_signals['ypr'] = signal.efficiency_signal
            
            if signal.scoring_signal > 0:
                if 'td' in signal.market:
                    scoring_signals['td_prob'] = max(scoring_signals.get('td_prob', 0), signal.scoring_signal)
        
        return {
            'volume': volume_signals,
            'efficiency': efficiency_signals,
            'scoring': scoring_signals
        }
    
    def project_qb(self, player_name: str, signals: Dict[str, float], 
                   game_context: GameContext, team: str) -> PlayerProjection:
        """Project QB fantasy points from prop signals."""
        volume = signals.get('volume', {})
        efficiency = signals.get('efficiency', {})
        scoring = signals.get('scoring', {})
        
        # Passing projection
        pass_attempts = volume.get('pass_attempts', 32)
        ypa = efficiency.get('ypa', 7.2)
        pass_yards = pass_attempts * ypa
        
        # Adjust for game context
        if team == game_context.home_team:
            pass_rate = game_context.pass_rate_home
        else:
            pass_rate = game_context.pass_rate_away
        
        # Apply pace and script adjustments
        pace_factor = game_context.pace_factors.get(team, 1.0)
        pass_attempts *= pace_factor
        
        # Passing TDs (typically 1 TD per 13-15 attempts)
        pass_tds = pass_attempts / 14.0
        
        # Rushing projection
        qb_rush_boost = game_context.qb_rushing_boost.get(team, 1.0)
        rush_attempts = 6 * qb_rush_boost
        rush_yards = rush_attempts * 5.8  # QB YPC typically higher
        
        # Rushing TDs - boosted in competitive games
        competitive_prob = game_context.script_probabilities.get('competitive', 0.5)
        rush_td_base = 0.3
        rush_tds = rush_td_base * (1 + competitive_prob * 0.5) * qb_rush_boost
        
        # Calculate fantasy points
        fantasy_points = (
            pass_yards * self.scoring_system['pass_yards'] +
            pass_tds * self.scoring_system['pass_tds'] +
            rush_yards * self.scoring_system['rush_yards'] +
            rush_tds * self.scoring_system['rush_tds']
        )
        
        # Floor and ceiling based on variance
        floor = fantasy_points * 0.6
        ceiling = fantasy_points * 1.8
        
        return PlayerProjection(
            player_name=player_name,
            position='QB',
            team=team,
            fantasy_points=fantasy_points,
            floor=floor,
            ceiling=ceiling,
            volume_projection={'pass_att': pass_attempts, 'rush_att': rush_attempts},
            efficiency_projection={'ypa': ypa, 'ypc': 5.8},
            scoring_projection={'pass_tds': pass_tds, 'rush_tds': rush_tds},
            projection_confidence=0.8,
            sharp_money_alignment=0.5,
            script_sensitivity={'competitive': 0.3, 'blowout': -0.2}
        )
    
    def project_rb(self, player_name: str, signals: Dict[str, float],
                   game_context: GameContext, team: str) -> PlayerProjection:
        """Project RB fantasy points from prop signals."""
        volume = signals.get('volume', {})
        efficiency = signals.get('efficiency', {})
        scoring = signals.get('scoring', {})
        
        # Rushing projection
        rush_attempts = volume.get('rush_attempts', 15)
        ypc = efficiency.get('ypc', 4.3)
        rush_yards = rush_attempts * ypc
        
        # Receiving projection
        targets = volume.get('targets', 3)
        receptions = targets * 0.78  # RB catch rate
        rec_yards = receptions * 7.4  # RB YPR
        
        # Apply game context
        pace_factor = game_context.pace_factors.get(team, 1.0)
        rush_attempts *= pace_factor
        
        # Script adjustments
        competitive_prob = game_context.script_probabilities.get('competitive', 0.5)
        if team == game_context.home_team and game_context.spread > 0:
            # Home favorite - more rushing in blowouts
            rush_attempts *= (1 + (1 - competitive_prob) * 0.2)
        
        # TD projection
        td_prob = scoring.get('td_prob', 0.6)
        rz_efficiency = game_context.rz_efficiency.get(team, 0.58)
        total_tds = td_prob * rz_efficiency
        
        # Calculate fantasy points
        fantasy_points = (
            rush_yards * self.scoring_system['rush_yards'] +
            total_tds * self.scoring_system['rush_tds'] +
            rec_yards * self.scoring_system['rec_yards'] +
            receptions * self.scoring_system['receptions']
        )
        
        floor = fantasy_points * 0.5
        ceiling = fantasy_points * 2.2
        
        return PlayerProjection(
            player_name=player_name,
            position='RB',
            team=team,
            fantasy_points=fantasy_points,
            floor=floor,
            ceiling=ceiling,
            volume_projection={'rush_att': rush_attempts, 'targets': targets},
            efficiency_projection={'ypc': ypc, 'ypr': 7.4},
            scoring_projection={'td_prob': td_prob},
            projection_confidence=0.75,
            sharp_money_alignment=0.5,
            script_sensitivity={'competitive': -0.1, 'blowout': 0.3}
        )
    
    def project_wr(self, player_name: str, signals: Dict[str, float],
                   game_context: GameContext, team: str) -> PlayerProjection:
        """Project WR fantasy points from prop signals."""
        volume = signals.get('volume', {})
        efficiency = signals.get('efficiency', {})
        scoring = signals.get('scoring', {})
        
        # Receiving projection
        targets = volume.get('targets', 6)
        catch_rate = 0.65  # WR catch rate
        receptions = targets * catch_rate
        ypr = efficiency.get('ypr', 11.2)
        rec_yards = receptions * ypr
        
        # Apply game context
        if team == game_context.home_team:
            pass_rate = game_context.pass_rate_home
        else:
            pass_rate = game_context.pass_rate_away
        
        pace_factor = game_context.pace_factors.get(team, 1.0)
        targets *= pace_factor * (pass_rate / 0.62)  # Adjust for pass rate vs average
        
        # TD projection
        td_prob = scoring.get('td_prob', 0.4)
        rz_efficiency = game_context.rz_efficiency.get(team, 0.58)
        rec_tds = td_prob * rz_efficiency
        
        # Calculate fantasy points
        fantasy_points = (
            rec_yards * self.scoring_system['rec_yards'] +
            receptions * self.scoring_system['receptions'] +
            rec_tds * self.scoring_system['rec_tds']
        )
        
        floor = fantasy_points * 0.4
        ceiling = fantasy_points * 2.5
        
        return PlayerProjection(
            player_name=player_name,
            position='WR',
            team=team,
            fantasy_points=fantasy_points,
            floor=floor,
            ceiling=ceiling,
            volume_projection={'targets': targets},
            efficiency_projection={'ypr': ypr, 'catch_rate': catch_rate},
            scoring_projection={'td_prob': td_prob},
            projection_confidence=0.7,
            sharp_money_alignment=0.5,
            script_sensitivity={'competitive': 0.2, 'blowout': 0.1}
        )
    
    def create_projections_from_props(self, props_text: str, total: float, spread: float,
                                    home_team: str, away_team: str,
                                    weather_data: Optional[Dict] = None) -> List[PlayerProjection]:
        """Create comprehensive projections from prop data."""
        
        # Parse props
        props_data = self.parse_props_from_text(props_text)
        if not props_data:
            print("Warning: No props data parsed")
            return []
        
        # Extract signals
        player_signals = self.prop_extractor.extract_all_signals(props_data)
        
        # Build game context
        game_context = self.context_analyzer.build_comprehensive_context(
            total, spread, home_team, away_team, player_signals, weather_data
        )
        
        # Create projections
        projections = []
        
        for player_name, signals in player_signals.items():
            # Aggregate signals
            aggregated = self.aggregate_player_signals(signals)
            
            # Determine position and team (simplified - you may need better logic)
            position = self.infer_position_from_signals(signals)
            team = self.infer_team_from_name(player_name, home_team, away_team)
            
            # Create projection based on position
            if position == 'QB':
                projection = self.project_qb(player_name, aggregated, game_context, team)
            elif position == 'RB':
                projection = self.project_rb(player_name, aggregated, game_context, team)
            elif position == 'WR':
                projection = self.project_wr(player_name, aggregated, game_context, team)
            else:
                continue  # Skip unknown positions for now
            
            projections.append(projection)
        
        return sorted(projections, key=lambda x: x.fantasy_points, reverse=True)
    
    def infer_position_from_signals(self, signals: List[PropSignal]) -> str:
        """Infer position from the types of props available."""
        markets = [s.market for s in signals]
        
        if any('pass' in m for m in markets):
            return 'QB'
        elif any(m in ['rush_att', 'rush_yards'] for m in markets) and not any('pass' in m for m in markets):
            return 'RB'
        elif any(m in ['rec_yards', 'receptions'] for m in markets):
            return 'WR'  # Could be TE but default to WR
        else:
            return 'UNKNOWN'
    
    def infer_team_from_name(self, player_name: str, home_team: str, away_team: str) -> str:
        """Infer team from player name (simplified - would need roster data)."""
        # This is a placeholder - you'd need actual roster mapping
        known_players = {
            'Josh Allen': 'BUF', 'Lamar Jackson': 'BAL',
            'Derrick Henry': 'BAL', 'James Cook': 'BUF',
            'Zay Flowers': 'BAL', 'Khalil Shakir': 'BUF',
            'Keon Coleman': 'BUF', 'Mark Andrews': 'BAL'
        }
        
        return known_players.get(player_name, home_team)  # Default to home team
