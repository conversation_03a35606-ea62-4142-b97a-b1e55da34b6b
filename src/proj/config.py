"""
Configuration settings for NFL projections.

This module handles configuration from environment variables and provides defaults.
"""

import os
from typing import Any


def get_bool_env(key: str, default: bool = False) -> bool:
    """Get boolean value from environment variable."""
    value = os.getenv(key, '').lower()
    if value in ('true', '1', 'yes', 'on'):
        return True
    elif value in ('false', '0', 'no', 'off'):
        return False
    else:
        return default


def get_float_env(key: str, default: float) -> float:
    """Get float value from environment variable."""
    try:
        return float(os.getenv(key, str(default)))
    except (ValueError, TypeError):
        return default


# Week 1 backtest nudge configuration
APPLY_2024_W1_NUDGES = get_bool_env('APPLY_2024_W1_NUDGES', False)
NUDGE_LAMBDA = get_float_env('NUDGE_LAMBDA', 0.35)
NUDGE_MU_CAP = get_float_env('NUDGE_MU_CAP', 0.6)
NUDGE_SIGMA_SCALE = get_float_env('NUDGE_SIGMA_SCALE', 0.03)
NUDGE_SIGMA_CAP = get_float_env('NUDGE_SIGMA_CAP', 1.10)

# Logging configuration
LOG_NUDGES = get_bool_env('LOG_NUDGES', True)
LOG_DIR = os.getenv('LOG_DIR', 'data/logs')

# Model configuration
DEFAULT_CONFIDENCE_THRESHOLD = get_float_env('DEFAULT_CONFIDENCE_THRESHOLD', 0.6)

# API configuration
SLEEPER_CACHE_HOURS = get_float_env('SLEEPER_CACHE_HOURS', 24.0)
WEATHER_CACHE_HOURS = get_float_env('WEATHER_CACHE_HOURS', 6.0)


class Config:
    """Configuration class for easy access to settings."""
    
    def __init__(self):
        self.apply_2024_w1_nudges = APPLY_2024_W1_NUDGES
        self.nudge_lambda = NUDGE_LAMBDA
        self.nudge_mu_cap = NUDGE_MU_CAP
        self.nudge_sigma_scale = NUDGE_SIGMA_SCALE
        self.nudge_sigma_cap = NUDGE_SIGMA_CAP
        self.log_nudges = LOG_NUDGES
        self.log_dir = LOG_DIR
        self.default_confidence_threshold = DEFAULT_CONFIDENCE_THRESHOLD
        self.sleeper_cache_hours = SLEEPER_CACHE_HOURS
        self.weather_cache_hours = WEATHER_CACHE_HOURS
    
    def update_from_dict(self, config_dict: dict) -> None:
        """Update configuration from dictionary."""
        for key, value in config_dict.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def to_dict(self) -> dict:
        """Convert configuration to dictionary."""
        return {
            'apply_2024_w1_nudges': self.apply_2024_w1_nudges,
            'nudge_lambda': self.nudge_lambda,
            'nudge_mu_cap': self.nudge_mu_cap,
            'nudge_sigma_scale': self.nudge_sigma_scale,
            'nudge_sigma_cap': self.nudge_sigma_cap,
            'log_nudges': self.log_nudges,
            'log_dir': self.log_dir,
            'default_confidence_threshold': self.default_confidence_threshold,
            'sleeper_cache_hours': self.sleeper_cache_hours,
            'weather_cache_hours': self.weather_cache_hours
        }


# Global config instance
config = Config()
