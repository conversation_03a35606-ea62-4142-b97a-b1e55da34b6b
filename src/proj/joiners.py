"""Team and player data reconciliation utilities."""

import pandas as pd
import numpy as np
from typing import Dict, Any, List, Tuple, Optional
from scipy.optimize import minimize
from difflib import SequenceMatcher
from .model import american_to_prob, devig, line_to_implied_mean
from .sleeper import load_cached_players, normalize_sleeper_team_name


def map_team_props_to_games(team_props_df: pd.DataFrame, 
                           games_df: pd.DataFrame) -> pd.DataFrame:
    """
    Map team props to specific games and compute implied team stats.
    
    Args:
        team_props_df: DataFrame with team prop lines
        games_df: DataFrame with game information
        
    Returns:
        DataFrame with team-level implied stats per game
    """
    team_stats = []
    
    for _, game in games_df.iterrows():
        home_team = game['home_team']
        away_team = game['away_team']
        
        for team in [home_team, away_team]:
            opponent = away_team if team == home_team else home_team
            
            # Find team props for this team in this game
            team_props = team_props_df[
                (team_props_df['team'] == team) & 
                (team_props_df['opponent'] == opponent)
            ]
            
            game_stats = {
                'game_id': f"{home_team}@{away_team}",
                'team': team,
                'opponent': opponent,
                'is_home': team == home_team
            }
            
            # Process each market
            for _, prop in team_props.iterrows():
                market = prop['market']
                line = prop['line']
                over_odds = prop['over_odds']
                under_odds = prop['under_odds']
                
                # Convert to implied mean
                p_over_raw = american_to_prob(over_odds)
                p_under_raw = american_to_prob(under_odds)
                p_over_fair, _ = devig(p_over_raw, p_under_raw)
                
                # Estimate sigma based on market type
                sigma_estimates = {
                    'team_total': 7.0,
                    'pass_yds': 25.0,
                    'rush_yds': 20.0,
                    'team_sacks': 1.2,
                    'turnovers': 0.8,
                    'first_downs': 2.5
                }
                
                sigma = sigma_estimates.get(market, 5.0)
                implied_mean = line_to_implied_mean(line, p_over_fair, sigma)
                
                game_stats[f'{market}_implied'] = implied_mean
                game_stats[f'{market}_line'] = line
                game_stats[f'{market}_sigma'] = sigma
            
            team_stats.append(game_stats)
    
    return pd.DataFrame(team_stats)


def reconcile_player_team_totals(player_projections: pd.DataFrame,
                                team_stats: pd.DataFrame,
                                tolerance: float = 0.1) -> pd.DataFrame:
    """
    Reconcile player projections with team-level constraints.
    
    Args:
        player_projections: DataFrame with player projections
        team_stats: DataFrame with team-level implied stats
        tolerance: Allowed deviation from team totals (as fraction)
        
    Returns:
        Adjusted player projections DataFrame
    """
    adjusted_projections = player_projections.copy()
    
    # Group by team and game
    for (team, game_id), team_group in player_projections.groupby(['team', 'game_id']):
        # Get team constraints for this game
        team_constraint = team_stats[
            (team_stats['team'] == team) & 
            (team_stats['game_id'] == game_id)
        ]
        
        if team_constraint.empty:
            continue
            
        team_row = team_constraint.iloc[0]
        
        # Reconcile passing yards
        if 'pass_yds_implied' in team_row:
            qb_players = team_group[team_group['position'] == 'QB']
            if not qb_players.empty:
                team_pass_target = team_row['pass_yds_implied']
                current_qb_total = qb_players['pass_yds_proj'].sum()
                
                if current_qb_total > 0:
                    adjustment_factor = team_pass_target / current_qb_total
                    # Apply soft constraint (don't force exact match)
                    soft_factor = 0.7 * adjustment_factor + 0.3 * 1.0
                    
                    for idx in qb_players.index:
                        adjusted_projections.loc[idx, 'pass_yds_proj'] *= soft_factor
        
        # Reconcile rushing yards
        if 'rush_yds_implied' in team_row:
            rb_players = team_group[team_group['position'].isin(['RB', 'QB'])]
            if not rb_players.empty:
                team_rush_target = team_row['rush_yds_implied']
                current_rush_total = rb_players['rush_yds_proj'].sum()
                
                if current_rush_total > 0:
                    adjustment_factor = team_rush_target / current_rush_total
                    soft_factor = 0.6 * adjustment_factor + 0.4 * 1.0
                    
                    for idx in rb_players.index:
                        adjusted_projections.loc[idx, 'rush_yds_proj'] *= soft_factor
        
        # Reconcile receiving yards (distribute among pass catchers)
        rec_players = team_group[team_group['position'].isin(['WR', 'TE', 'RB'])]
        if not rec_players.empty and 'pass_yds_implied' in team_row:
            # Estimate team receiving yards (typically ~85% of passing yards)
            team_rec_target = team_row['pass_yds_implied'] * 0.85
            current_rec_total = rec_players['rec_yds_proj'].sum()
            
            if current_rec_total > 0:
                adjustment_factor = team_rec_target / current_rec_total
                soft_factor = 0.5 * adjustment_factor + 0.5 * 1.0
                
                for idx in rec_players.index:
                    adjusted_projections.loc[idx, 'rec_yds_proj'] *= soft_factor
    
    return adjusted_projections


def apply_defensive_pressure_adjustments(player_projections: pd.DataFrame,
                                       team_stats: pd.DataFrame) -> pd.DataFrame:
    """
    Adjust QB projections based on defensive pressure metrics.
    
    Args:
        player_projections: DataFrame with player projections
        team_stats: DataFrame with team defensive stats
        
    Returns:
        Adjusted projections with pressure considerations
    """
    adjusted_projections = player_projections.copy()
    
    for idx, player in player_projections.iterrows():
        if player['position'] != 'QB':
            continue
            
        team = player['team']
        opponent = player['opponent']
        
        # Find opponent's defensive pressure stats
        opp_defense = team_stats[
            (team_stats['team'] == opponent) & 
            (team_stats['game_id'] == player['game_id'])
        ]
        
        # Find team's QB pressure allowed stats
        team_oline = team_stats[
            (team_stats['team'] == team) & 
            (team_stats['game_id'] == player['game_id'])
        ]
        
        pressure_adjustment = 1.0
        
        # Adjust based on opponent sacks
        if not opp_defense.empty and 'team_sacks_implied' in opp_defense.iloc[0]:
            opp_sacks = opp_defense.iloc[0]['team_sacks_implied']
            if opp_sacks > 3.0:  # High pressure defense
                pressure_adjustment *= 0.95  # Slight downward adjustment
            elif opp_sacks < 1.5:  # Low pressure defense
                pressure_adjustment *= 1.05  # Slight upward adjustment
        
        # Adjust based on team's sacks allowed (if available)
        if not team_oline.empty and 'qb_sacks_taken_implied' in team_oline.iloc[0]:
            sacks_allowed = team_oline.iloc[0]['qb_sacks_taken_implied']
            if sacks_allowed > 3.0:  # Poor pass protection
                pressure_adjustment *= 0.93
            elif sacks_allowed < 1.5:  # Good pass protection
                pressure_adjustment *= 1.07
        
        # Apply pressure adjustments to QB stats
        qb_stats = ['pass_yds_proj', 'pass_tds_proj', 'completions_proj']
        for stat in qb_stats:
            if stat in adjusted_projections.columns:
                adjusted_projections.loc[idx, stat] *= pressure_adjustment
        
        # Inverse adjustment for scrambling/rushing (more pressure = more scrambles)
        if 'rush_yds_proj' in adjusted_projections.columns:
            scramble_factor = 2.0 - pressure_adjustment  # Inverse relationship
            adjusted_projections.loc[idx, 'rush_yds_proj'] *= scramble_factor
        
        # Adjust target distribution (more checkdowns under pressure)
        if pressure_adjustment < 1.0:  # Under pressure
            # This would adjust TE/RB target shares upward
            # Implementation depends on how target shares are stored
            pass
    
    return adjusted_projections


def compute_target_share_constraints(team_stats: pd.DataFrame,
                                   player_roles: pd.DataFrame) -> Dict[str, Dict[str, float]]:
    """
    Compute target share constraints based on team passing volume and player roles.
    
    Args:
        team_stats: Team-level passing statistics
        player_roles: Player role information
        
    Returns:
        Dictionary with target share constraints by team/game
    """
    constraints = {}
    
    for _, team_row in team_stats.iterrows():
        if 'pass_yds_implied' not in team_row:
            continue
            
        team = team_row['team']
        game_id = team_row['game_id']
        
        # Estimate total targets from passing yards
        # Rule of thumb: ~6.5 yards per target in NFL
        estimated_targets = team_row['pass_yds_implied'] / 6.5
        
        # Get players for this team
        team_players = player_roles[player_roles['team'] == team]
        
        # Distribute targets based on roles and snap counts
        target_distribution = {}
        
        for _, player in team_players.iterrows():
            if player['position'] not in ['WR', 'TE', 'RB']:
                continue
                
            player_name = player['player_name']
            snap_pct = player.get('expected_snap_pct', 0.5)
            
            # Base target share by position and role
            if player['position'] == 'WR':
                if 'WR1' in player.get('role_notes', ''):
                    base_share = 0.25
                elif 'WR2' in player.get('role_notes', ''):
                    base_share = 0.18
                else:
                    base_share = 0.12
            elif player['position'] == 'TE':
                base_share = 0.15
            elif player['position'] == 'RB':
                base_share = 0.08
            else:
                base_share = 0.05
            
            # Adjust by snap percentage
            adjusted_share = base_share * snap_pct
            target_distribution[player_name] = adjusted_share
        
        # Normalize to ensure shares sum to reasonable total
        total_share = sum(target_distribution.values())
        if total_share > 0:
            for player in target_distribution:
                target_distribution[player] /= total_share
                target_distribution[player] *= 0.85  # Account for other targets
        
        constraints[f"{team}_{game_id}"] = {
            'estimated_targets': estimated_targets,
            'target_distribution': target_distribution
        }
    
    return constraints


def normalize_player_name(name: str) -> str:
    """
    Normalize player name for matching.

    Args:
        name: Player name to normalize

    Returns:
        Normalized name
    """
    if not name:
        return ''

    # Remove common suffixes and prefixes
    name = name.strip()
    suffixes = [' Jr.', ' Jr', ' Sr.', ' Sr', ' III', ' II', ' IV']
    for suffix in suffixes:
        if name.endswith(suffix):
            name = name[:-len(suffix)].strip()

    # Handle common nickname patterns
    if '(' in name and ')' in name:
        # Remove nicknames in parentheses
        name = name.split('(')[0].strip()

    return name.lower()


def find_sleeper_match(player_name: str, sleeper_df: pd.DataFrame,
                      threshold: float = 0.8) -> Optional[pd.Series]:
    """
    Find best matching player in Sleeper data.

    Args:
        player_name: Name to search for
        sleeper_df: Sleeper players DataFrame
        threshold: Minimum similarity score for match

    Returns:
        Best matching player row or None
    """
    if sleeper_df.empty:
        return None

    normalized_name = normalize_player_name(player_name)
    best_match = None
    best_score = 0

    for _, sleeper_player in sleeper_df.iterrows():
        sleeper_name = normalize_player_name(sleeper_player['full_name'])

        # Try exact match first
        if normalized_name == sleeper_name:
            return sleeper_player

        # Try similarity matching
        score = SequenceMatcher(None, normalized_name, sleeper_name).ratio()

        # Also try matching with first initial + last name
        if '.' in normalized_name or len(normalized_name.split()) == 2:
            parts = normalized_name.split()
            if len(parts) >= 2:
                # Handle "C. McCaffrey" -> "christian mccaffrey"
                first_initial = parts[0].replace('.', '').lower()
                last_name = ' '.join(parts[1:])

                sleeper_parts = sleeper_name.split()
                if len(sleeper_parts) >= 2:
                    sleeper_first = sleeper_parts[0]
                    sleeper_last = ' '.join(sleeper_parts[1:])

                    if (sleeper_first.startswith(first_initial) and
                        SequenceMatcher(None, last_name, sleeper_last).ratio() > 0.8):
                        score = max(score, 0.85)  # High score for initial + last name match

        if score > best_score and score >= threshold:
            best_score = score
            best_match = sleeper_player

    return best_match


def backfill_from_sleeper(players_df: pd.DataFrame) -> pd.DataFrame:
    """
    Backfill missing team/position data from Sleeper API.

    Args:
        players_df: DataFrame with player data

    Returns:
        DataFrame with backfilled data
    """
    # Load cached Sleeper data
    sleeper_df = load_cached_players()
    if sleeper_df is None or sleeper_df.empty:
        print("Warning: No Sleeper data available for backfilling")
        return players_df

    updated_df = players_df.copy()
    backfill_count = 0

    for idx, player in updated_df.iterrows():
        player_name = player.get('player_name', '') or player.get('full_name', '')
        if not player_name:
            continue

        # Check if team or position is missing
        needs_team = pd.isna(player.get('team')) or player.get('team') == ''
        needs_position = pd.isna(player.get('position')) or player.get('position') == ''

        if not (needs_team or needs_position):
            continue

        # Find match in Sleeper data
        sleeper_match = find_sleeper_match(player_name, sleeper_df)
        if sleeper_match is None:
            continue

        # Backfill missing data
        if needs_team and sleeper_match['team']:
            normalized_team = normalize_sleeper_team_name(sleeper_match['team'])
            updated_df.loc[idx, 'team'] = normalized_team
            backfill_count += 1

        if needs_position and sleeper_match['position']:
            updated_df.loc[idx, 'position'] = sleeper_match['position']
            backfill_count += 1

    if backfill_count > 0:
        print(f"Backfilled {backfill_count} missing team/position values from Sleeper data")

    return updated_df


def get_sleeper_suggestions(player_name: str, threshold: float = 0.6) -> List[Dict[str, Any]]:
    """
    Get Sleeper player suggestions for name mapping.

    Args:
        player_name: Name to find suggestions for
        threshold: Minimum similarity score for suggestions

    Returns:
        List of suggestion dictionaries
    """
    sleeper_df = load_cached_players()
    if sleeper_df is None or sleeper_df.empty:
        return []

    normalized_name = normalize_player_name(player_name)
    suggestions = []

    for _, sleeper_player in sleeper_df.iterrows():
        sleeper_name = normalize_player_name(sleeper_player['full_name'])
        score = SequenceMatcher(None, normalized_name, sleeper_name).ratio()

        if score >= threshold:
            suggestions.append({
                'sleeper_name': sleeper_player['full_name'],
                'team': sleeper_player['team'],
                'position': sleeper_player['position'],
                'similarity_score': score,
                'active': sleeper_player['active']
            })

    # Sort by similarity score descending
    suggestions.sort(key=lambda x: x['similarity_score'], reverse=True)

    return suggestions[:5]  # Return top 5 suggestions


def infer_role_order(df: pd.DataFrame, helpers: Dict[str, Any] = None) -> pd.DataFrame:
    """
    Infer role order within position groups using heuristic scoring.

    Args:
        df: DataFrame with player data including team, position, player_name
        helpers: Optional dict with additional data (snap_share_2024, contract_apy, etc.)

    Returns:
        DataFrame with added columns: inferred_order, inferred_reason, inferred_confidence
    """
    if helpers is None:
        helpers = {}

    result_df = df.copy()
    result_df['inferred_order'] = None
    result_df['inferred_reason'] = 'no_data'
    result_df['inferred_confidence'] = 0.2

    # Group by team and position
    for (team, position), group in df.groupby(['team', 'position']):
        if position not in ['RB', 'WR', 'TE']:
            continue  # Only infer for skill positions with depth

        players_in_group = group.copy()
        scores = []

        for _, player in players_in_group.iterrows():
            player_name = player['player_name']
            score = 0.0
            reasons = []

            # Factor 1: Last season snap share (highest weight)
            snap_2024 = helpers.get('snap_share_2024', {}).get(player_name, 0)
            if snap_2024 > 0:
                score += snap_2024 * 0.4  # 40% weight
                reasons.append(f"2024_snaps_{snap_2024:.1%}")

            # Factor 2: Contract APY (if available)
            contract_apy = helpers.get('contract_apy', {}).get(player_name, 0)
            if contract_apy > 0:
                # Normalize by position (rough estimates)
                pos_max_apy = {'RB': 15_000_000, 'WR': 30_000_000, 'TE': 15_000_000}
                normalized_apy = min(contract_apy / pos_max_apy.get(position, 15_000_000), 1.0)
                score += normalized_apy * 0.25  # 25% weight
                reasons.append(f"apy_{contract_apy/1_000_000:.1f}M")

            # Factor 3: Age/Experience (younger = higher upside, but veterans = proven)
            age = helpers.get('age', {}).get(player_name, 26)  # Default to 26
            experience = helpers.get('experience', {}).get(player_name, 3)  # Default to 3 years

            # Sweet spot around 24-28 years old, 2-6 years experience
            age_score = max(0, 1 - abs(age - 26) / 10)  # Peak at 26, decline with distance
            exp_score = max(0, 1 - abs(experience - 4) / 8)  # Peak at 4 years
            score += (age_score + exp_score) * 0.15  # 15% weight combined
            reasons.append(f"age_{age}_exp_{experience}")

            # Factor 4: Recent transactions/draft status
            draft_round = helpers.get('draft_round', {}).get(player_name, 7)  # Default to UDFA
            if draft_round <= 3:
                score += (4 - draft_round) * 0.1  # 10% weight for early draft picks
                reasons.append(f"rd{draft_round}")

            # Factor 5: Preseason usage (if available)
            preseason_snaps = helpers.get('preseason_snaps', {}).get(player_name, 0)
            if preseason_snaps > 0:
                score += min(preseason_snaps, 0.8) * 0.1  # 10% weight, cap at 80%
                reasons.append(f"pre_{preseason_snaps:.1%}")

            scores.append({
                'player_name': player_name,
                'score': score,
                'reasons': reasons
            })

        # Sort by score and assign order
        scores.sort(key=lambda x: x['score'], reverse=True)

        for i, player_score in enumerate(scores):
            player_name = player_score['player_name']
            order = i + 1
            confidence = min(0.75, 0.4 + player_score['score'] * 0.35)  # Cap at 0.75
            reason = ', '.join(player_score['reasons'][:2])  # Top 2 reasons

            # Update the result DataFrame
            mask = (result_df['team'] == team) & (result_df['position'] == position) & (result_df['player_name'] == player_name)
            result_df.loc[mask, 'inferred_order'] = order
            result_df.loc[mask, 'inferred_reason'] = reason if reason else 'heuristic'
            result_df.loc[mask, 'inferred_confidence'] = confidence

    return result_df


def validate_reconciliation(original_projections: pd.DataFrame,
                          adjusted_projections: pd.DataFrame,
                          team_constraints: pd.DataFrame,
                          tolerance: float = 0.15) -> Dict[str, Any]:
    """
    Validate that reconciliation stayed within acceptable bounds.
    
    Args:
        original_projections: Original player projections
        adjusted_projections: Reconciled projections
        team_constraints: Team-level constraints
        tolerance: Maximum allowed deviation
        
    Returns:
        Validation report dictionary
    """
    report = {
        'passed': True,
        'warnings': [],
        'errors': [],
        'adjustments_summary': {}
    }
    
    # Check individual player adjustments
    for idx in original_projections.index:
        player_name = original_projections.loc[idx, 'player_name']
        
        for col in ['pass_yds_proj', 'rush_yds_proj', 'rec_yds_proj']:
            if col in original_projections.columns:
                orig_val = original_projections.loc[idx, col]
                adj_val = adjusted_projections.loc[idx, col]
                
                if orig_val > 0:
                    pct_change = abs(adj_val - orig_val) / orig_val
                    
                    if pct_change > tolerance:
                        report['warnings'].append(
                            f"{player_name} {col}: {pct_change:.1%} change (>{tolerance:.1%})"
                        )
                        
                    if pct_change > tolerance * 2:
                        report['errors'].append(
                            f"{player_name} {col}: {pct_change:.1%} change (>{tolerance*2:.1%})"
                        )
                        report['passed'] = False
    
    # Check team-level constraint satisfaction
    for (team, game_id), team_group in adjusted_projections.groupby(['team', 'game_id']):
        constraint = team_constraints[
            (team_constraints['team'] == team) & 
            (team_constraints['game_id'] == game_id)
        ]
        
        if constraint.empty:
            continue
            
        constraint_row = constraint.iloc[0]
        
        # Check passing yards constraint
        if 'pass_yds_implied' in constraint_row:
            qb_total = team_group[team_group['position'] == 'QB']['pass_yds_proj'].sum()
            target = constraint_row['pass_yds_implied']
            
            if target > 0:
                deviation = abs(qb_total - target) / target
                if deviation > tolerance:
                    report['warnings'].append(
                        f"{team} passing yards: {deviation:.1%} from constraint"
                    )
    
    return report
