"""Sleeper API integration for NFL player data."""

import os
import json
import requests
import pandas as pd
from datetime import datetime, timezone
from pathlib import Path
from typing import Dict, Any, Optional, List


def get_players() -> pd.DataFrame:
    """
    Fetch NFL players from Sleeper API and normalize to standard format.
    
    Returns:
        DataFrame with columns: player_id, full_name, first_name, last_name, team, position, active
    """
    url = "https://api.sleeper.app/v1/players/nfl"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        raw_data = response.json()
        
        # Cache raw JSON with timestamp
        cache_dir = Path("data/cache")
        cache_dir.mkdir(parents=True, exist_ok=True)
        
        cache_file = cache_dir / "sleeper_players.json"
        cache_data = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": raw_data
        }
        
        with open(cache_file, 'w') as f:
            json.dump(cache_data, f, indent=2)
        
        # Normalize to DataFrame
        players = []
        for player_id, player_data in raw_data.items():
            if not isinstance(player_data, dict):
                continue
                
            # Extract basic info
            first_name = player_data.get('first_name', '')
            last_name = player_data.get('last_name', '')
            full_name = player_data.get('full_name', '')
            
            # If full_name is missing, construct it
            if not full_name and (first_name or last_name):
                full_name = f"{first_name} {last_name}".strip()
            
            # Get team and position
            team = player_data.get('team')
            position = player_data.get('position')
            
            # Active status - default to True if not specified
            active = player_data.get('active', True)
            
            # Only include players with meaningful data
            if full_name and position:
                players.append({
                    'player_id': player_id,
                    'full_name': full_name,
                    'first_name': first_name or '',
                    'last_name': last_name or '',
                    'team': team,
                    'position': position,
                    'active': active
                })
        
        df = pd.DataFrame(players)
        
        # Clean up team names (handle None values)
        df['team'] = df['team'].fillna('')
        
        # Sort by position and name for consistency
        df = df.sort_values(['position', 'full_name']).reset_index(drop=True)
        
        return df
        
    except requests.RequestException as e:
        raise Exception(f"Failed to fetch Sleeper players data: {e}")
    except Exception as e:
        raise Exception(f"Error processing Sleeper players data: {e}")


def get_state() -> Dict[str, Any]:
    """
    Fetch NFL state information from Sleeper API.
    
    Returns:
        Dictionary with season and week fields
    """
    url = "https://api.sleeper.app/v1/state/nfl"
    
    try:
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        data = response.json()
        
        return {
            'season': data.get('season'),
            'week': data.get('week'),
            'season_type': data.get('season_type'),
            'display_week': data.get('display_week')
        }
        
    except requests.RequestException as e:
        raise Exception(f"Failed to fetch Sleeper state data: {e}")
    except Exception as e:
        raise Exception(f"Error processing Sleeper state data: {e}")


def get_trending(kind: str = "add", lookback_hours: int = 24, limit: int = 50) -> pd.DataFrame:
    """
    Fetch trending players from Sleeper API.

    Args:
        kind: Type of trending data ("add" or "drop")
        lookback_hours: Hours to look back for trending data
        limit: Maximum number of players to return

    Returns:
        DataFrame with trending player information
    """
    url = f"https://api.sleeper.app/v1/players/nfl/trending/{kind}"
    params = {'lookback_hours': lookback_hours, 'limit': limit}
    
    try:
        response = requests.get(url, params=params, timeout=30)
        response.raise_for_status()
        raw_data = response.json()
        
        if not raw_data:
            return pd.DataFrame()
        
        # Convert to DataFrame
        trending_players = []
        for item in raw_data:
            player_id = item.get('player_id')
            count = item.get('count', 0)
            
            trending_players.append({
                'player_id': player_id,
                'trend_type': kind,
                'count': count,
                'lookback_hours': lookback_hours
            })
        
        df = pd.DataFrame(trending_players)
        
        # Sort by count descending
        df = df.sort_values('count', ascending=False).reset_index(drop=True)
        
        return df
        
    except requests.RequestException as e:
        raise Exception(f"Failed to fetch Sleeper trending data: {e}")
    except Exception as e:
        raise Exception(f"Error processing Sleeper trending data: {e}")


def load_cached_players() -> Optional[pd.DataFrame]:
    """
    Load cached Sleeper players data if available and recent.
    
    Returns:
        DataFrame if cache exists and is recent, None otherwise
    """
    cache_file = Path("data/cache/sleeper_players.json")
    
    if not cache_file.exists():
        return None
    
    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)
        
        # Check if cache is recent (within 24 hours)
        timestamp_str = cache_data.get('timestamp')
        if timestamp_str:
            cache_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            hours_old = (now - cache_time).total_seconds() / 3600
            
            if hours_old > 24:
                return None
        
        # Convert cached data to DataFrame
        raw_data = cache_data.get('data', {})
        players = []
        
        for player_id, player_data in raw_data.items():
            if not isinstance(player_data, dict):
                continue
                
            first_name = player_data.get('first_name', '')
            last_name = player_data.get('last_name', '')
            full_name = player_data.get('full_name', '')
            
            if not full_name and (first_name or last_name):
                full_name = f"{first_name} {last_name}".strip()
            
            team = player_data.get('team')
            position = player_data.get('position')
            active = player_data.get('active', True)
            
            if full_name and position:
                players.append({
                    'player_id': player_id,
                    'full_name': full_name,
                    'first_name': first_name or '',
                    'last_name': last_name or '',
                    'team': team,
                    'position': position,
                    'active': active
                })
        
        df = pd.DataFrame(players)
        df['team'] = df['team'].fillna('')
        df = df.sort_values(['position', 'full_name']).reset_index(drop=True)
        
        return df
        
    except Exception:
        return None


def normalize_sleeper_team_name(team: str) -> str:
    """
    Normalize Sleeper team abbreviations to standard format.

    Args:
        team: Sleeper team abbreviation

    Returns:
        Normalized team abbreviation
    """
    if not team:
        return ''

    # Sleeper uses standard NFL abbreviations, but let's handle any edge cases
    team_mapping = {
        'JAX': 'JAC',  # Jacksonville sometimes varies
        'WSH': 'WAS',  # Washington name changes
    }

    return team_mapping.get(team.upper(), team.upper())


def depth_charts_for_teams(teams: List[str]) -> pd.DataFrame:
    """
    Generate roster information for specified teams using Sleeper player data.

    NOTE: This does NOT provide actual depth charts - only roster composition.
    Use roles_notes.csv for authoritative depth chart information.

    Args:
        teams: List of team abbreviations

    Returns:
        DataFrame with columns: team, player_name, position, source, depth_confidence
    """
    # Load cached players data or fetch fresh
    players_df = load_cached_players()
    if players_df is None:
        print("No cached Sleeper data found, fetching fresh data...")
        players_df = get_players()

    # Cache roster data under data/cache/sleeper/
    cache_dir = Path("data/cache/sleeper")
    cache_dir.mkdir(parents=True, exist_ok=True)

    roster_data = []

    for team in teams:
        normalized_team = normalize_sleeper_team_name(team)

        # Get players for this team
        team_players = players_df[
            (players_df['team'] == normalized_team) &
            (players_df['active'] == True)
        ].copy()

        if team_players.empty:
            print(f"Warning: No active players found for team {normalized_team}")
            continue

        # Add each player to roster data (no depth ordering)
        for _, player in team_players.iterrows():
            if player['position'] in ['QB', 'RB', 'WR', 'TE']:  # Only skill positions
                roster_data.append({
                    'team': normalized_team,
                    'player_name': player['full_name'],
                    'position': player['position'],
                    'source': 'sleeper_roster',
                    'depth_confidence': 0.2,  # Low confidence - roster only
                    'inferred_order': None,  # No ordering from Sleeper
                    'inferred_reason': 'roster_only',
                    'inferred_confidence': 0.2
                })

    df = pd.DataFrame(roster_data)

    # Cache the roster data
    cache_file = cache_dir / "roster_data.json"
    cache_data = {
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "teams": teams,
        "data": df.to_dict('records')
    }

    with open(cache_file, 'w') as f:
        json.dump(cache_data, f, indent=2)

    return df


def load_cached_roster_data(teams: List[str]) -> Optional[pd.DataFrame]:
    """
    Load cached roster data if available and recent.

    Args:
        teams: List of team abbreviations to check

    Returns:
        DataFrame if cache exists and is recent, None otherwise
    """
    cache_file = Path("data/cache/sleeper/roster_data.json")

    if not cache_file.exists():
        return None

    try:
        with open(cache_file, 'r') as f:
            cache_data = json.load(f)

        # Check if cache is recent (within 24 hours)
        timestamp_str = cache_data.get('timestamp')
        if timestamp_str:
            cache_time = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            now = datetime.now(timezone.utc)
            hours_old = (now - cache_time).total_seconds() / 3600

            if hours_old > 24:
                return None

        # Check if cached teams match requested teams
        cached_teams = set(cache_data.get('teams', []))
        requested_teams = set(teams)

        if not requested_teams.issubset(cached_teams):
            return None  # Need to fetch more teams

        # Convert cached data to DataFrame
        df = pd.DataFrame(cache_data.get('data', []))

        # Filter to only requested teams
        if not df.empty:
            normalized_teams = [normalize_sleeper_team_name(team) for team in teams]
            df = df[df['team'].isin(normalized_teams)]

        return df

    except Exception:
        return None
