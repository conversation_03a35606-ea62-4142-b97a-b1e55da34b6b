"""NFL game simulation utilities with game-script awareness."""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional
from scipy import stats


def simulate_game(home_total: float, away_total: float, 
                 home_std: float = 7.0, away_std: float = 7.0,
                 n_simulations: int = 10000) -> Dict[str, float]:
    """
    Simulate NFL game outcomes.
    
    Args:
        home_total: Expected points for home team
        away_total: Expected points for away team  
        home_std: Standard deviation for home team scoring
        away_std: Standard deviation for away team scoring
        n_simulations: Number of simulations to run
        
    Returns:
        Dictionary with simulation results
    """
    # Generate random scores
    home_scores = np.random.normal(home_total, home_std, n_simulations)
    away_scores = np.random.normal(away_total, away_std, n_simulations)
    
    # Ensure non-negative scores
    home_scores = np.maximum(home_scores, 0)
    away_scores = np.maximum(away_scores, 0)
    
    # Calculate outcomes
    home_wins = np.sum(home_scores > away_scores)
    away_wins = np.sum(away_scores > home_scores)
    ties = n_simulations - home_wins - away_wins
    
    total_points = home_scores + away_scores
    
    results = {
        'home_win_prob': home_wins / n_simulations,
        'away_win_prob': away_wins / n_simulations,
        'tie_prob': ties / n_simulations,
        'avg_home_score': np.mean(home_scores),
        'avg_away_score': np.mean(away_scores),
        'avg_total_points': np.mean(total_points),
        'over_under_line': home_total + away_total,
        'over_prob': np.sum(total_points > (home_total + away_total)) / n_simulations
    }
    
    return results


def simulate_season(games_df: pd.DataFrame, n_simulations: int = 1000) -> pd.DataFrame:
    """
    Simulate multiple games for season projections.
    
    Args:
        games_df: DataFrame with columns home_team, away_team, home_implied_total, away_implied_total
        n_simulations: Number of simulations per game
        
    Returns:
        DataFrame with simulation results for each game
    """
    results = []
    
    for _, game in games_df.iterrows():
        sim_result = simulate_game(
            game['home_implied_total'],
            game['away_implied_total'],
            n_simulations=n_simulations
        )
        
        game_result = {
            'home_team': game['home_team'],
            'away_team': game['away_team'],
            **sim_result
        }
        
        results.append(game_result)
    
    return pd.DataFrame(results)


def compute_game_script_weights(spread: float, total: float) -> Dict[str, float]:
    """
    Compute game script weights based on spread and total.

    Args:
        spread: Point spread (positive = home favored)
        total: Game total points

    Returns:
        Dictionary with trail/neutral/lead weights for each team
    """
    # Base weights centered around {trail: 0.35, neutral: 0.45, lead: 0.20}
    base_weights = np.array([0.35, 0.45, 0.20])  # trail, neutral, lead

    # Adjust based on spread magnitude
    spread_magnitude = abs(spread)

    # For large spreads, shift weights toward expected game script
    if spread_magnitude > 7:  # Large spread
        if spread > 0:  # Home favored
            # Home more likely to lead, away more likely to trail
            home_weights = np.array([0.25, 0.40, 0.35])  # Less trail, more lead
            away_weights = np.array([0.45, 0.40, 0.15])  # More trail, less lead
        else:  # Away favored
            home_weights = np.array([0.45, 0.40, 0.15])
            away_weights = np.array([0.25, 0.40, 0.35])
    elif spread_magnitude > 3:  # Medium spread
        adjustment = spread_magnitude * 0.02
        if spread > 0:
            home_weights = base_weights + np.array([-adjustment, 0, adjustment])
            away_weights = base_weights + np.array([adjustment, 0, -adjustment])
        else:
            home_weights = base_weights + np.array([adjustment, 0, -adjustment])
            away_weights = base_weights + np.array([-adjustment, 0, adjustment])
    else:  # Small spread or pick'em
        home_weights = base_weights
        away_weights = base_weights

    # Ensure weights sum to 1
    home_weights = home_weights / home_weights.sum()
    away_weights = away_weights / away_weights.sum()

    return {
        'home_trail': home_weights[0],
        'home_neutral': home_weights[1],
        'home_lead': home_weights[2],
        'away_trail': away_weights[0],
        'away_neutral': away_weights[1],
        'away_lead': away_weights[2]
    }


def adjust_player_shares_by_script(base_shares: Dict[str, float],
                                 game_script: str,
                                 position: str) -> Dict[str, float]:
    """
    Adjust player target/carry shares based on game script.

    Args:
        base_shares: Base target/carry shares by player
        game_script: 'trail', 'neutral', or 'lead'
        position: Player position

    Returns:
        Adjusted shares dictionary
    """
    adjusted_shares = base_shares.copy()

    # Position-specific elasticities
    elasticities = {
        'WR1': 0.1,    # Stable
        'WR2': 0.3,    # Elastic
        'WR3': 0.4,    # Very elastic
        'RB1': 0.2,    # Moderately stable
        'RB2': 0.5,    # Very elastic (pass-catching)
        'TE1': 0.25,   # Moderately elastic
        'QB': 0.0      # Not applicable
    }

    base_elasticity = elasticities.get(position, 0.3)

    # Script adjustments
    if game_script == 'trail':
        # Trailing teams pass more, favor pass-catchers
        if position in ['WR2', 'WR3', 'RB2', 'TE1']:
            adjustment = 1 + (base_elasticity * 0.5)  # Increase share
        else:
            adjustment = 1 - (base_elasticity * 0.2)  # Slight decrease
    elif game_script == 'lead':
        # Leading teams run more, favor RB1
        if position == 'RB1':
            adjustment = 1 + (base_elasticity * 0.8)
        elif position in ['WR2', 'WR3']:
            adjustment = 1 - (base_elasticity * 0.4)  # Decrease share
        else:
            adjustment = 1 - (base_elasticity * 0.1)
    else:  # neutral
        adjustment = 1.0

    # Apply adjustment
    for player in adjusted_shares:
        adjusted_shares[player] *= adjustment

    # Renormalize to ensure shares sum to 1
    total_share = sum(adjusted_shares.values())
    if total_share > 0:
        for player in adjusted_shares:
            adjusted_shares[player] /= total_share

    return adjusted_shares


def simulate_game_with_script(home_total: float, away_total: float, spread: float,
                            player_data: Optional[Dict] = None,
                            n_simulations: int = 10000) -> Dict[str, Any]:
    """
    Simulate NFL game with game script considerations.

    Args:
        home_total: Expected points for home team
        away_total: Expected points for away team
        spread: Point spread (positive = home favored)
        player_data: Optional player projection data
        n_simulations: Number of simulations

    Returns:
        Dictionary with game and player simulation results
    """
    # Get game script weights
    script_weights = compute_game_script_weights(spread, home_total + away_total)

    # Sample game scripts for each simulation
    home_scripts = np.random.choice(
        ['trail', 'neutral', 'lead'],
        size=n_simulations,
        p=[script_weights['home_trail'], script_weights['home_neutral'], script_weights['home_lead']]
    )

    away_scripts = np.random.choice(
        ['trail', 'neutral', 'lead'],
        size=n_simulations,
        p=[script_weights['away_trail'], script_weights['away_neutral'], script_weights['away_lead']]
    )

    # Base game simulation
    home_scores = np.random.normal(home_total, 7.0, n_simulations)
    away_scores = np.random.normal(away_total, 7.0, n_simulations)

    # Ensure non-negative scores
    home_scores = np.maximum(home_scores, 0)
    away_scores = np.maximum(away_scores, 0)

    results = {
        'home_win_prob': np.sum(home_scores > away_scores) / n_simulations,
        'away_win_prob': np.sum(away_scores > home_scores) / n_simulations,
        'tie_prob': np.sum(home_scores == away_scores) / n_simulations,
        'avg_home_score': np.mean(home_scores),
        'avg_away_score': np.mean(away_scores),
        'script_weights': script_weights
    }

    # Add player projections if data provided
    if player_data:
        player_results = {}

        for player_id, player_info in player_data.items():
            position = player_info.get('position', 'UNKNOWN')
            team = player_info.get('team', 'UNKNOWN')
            base_projection = player_info.get('base_projection', 0)
            base_shares = player_info.get('shares', {})

            # Simulate player performance across different scripts
            player_scores = []

            for i in range(n_simulations):
                # Determine which script applies to this player's team
                if team == 'home':
                    script = home_scripts[i]
                else:
                    script = away_scripts[i]

                # Adjust shares based on script
                adjusted_shares = adjust_player_shares_by_script(base_shares, script, position)

                # Sample from posterior distribution (placeholder)
                # In practice, this would use the Bayesian posterior
                player_score = np.random.normal(base_projection, base_projection * 0.3)
                player_score = max(0, player_score)  # Ensure non-negative

                player_scores.append(player_score)

            # Calculate percentiles for DraftKings points
            player_scores = np.array(player_scores)
            player_results[player_id] = {
                'mean': np.mean(player_scores),
                'p50': np.percentile(player_scores, 50),
                'p75': np.percentile(player_scores, 75),
                'p90': np.percentile(player_scores, 90),
                'std': np.std(player_scores)
            }

        results['player_projections'] = player_results

    return results


def calculate_team_records(simulation_results: pd.DataFrame) -> pd.DataFrame:
    """
    Calculate projected team records from simulation results.
    
    Args:
        simulation_results: DataFrame from simulate_season
        
    Returns:
        DataFrame with team records
    """
    team_stats = {}
    
    for _, game in simulation_results.iterrows():
        home_team = game['home_team']
        away_team = game['away_team']
        
        # Initialize teams if not seen before
        for team in [home_team, away_team]:
            if team not in team_stats:
                team_stats[team] = {
                    'games': 0,
                    'expected_wins': 0.0,
                    'expected_losses': 0.0,
                    'expected_points_for': 0.0,
                    'expected_points_against': 0.0
                }
        
        # Update home team stats
        team_stats[home_team]['games'] += 1
        team_stats[home_team]['expected_wins'] += game['home_win_prob']
        team_stats[home_team]['expected_losses'] += game['away_win_prob']
        team_stats[home_team]['expected_points_for'] += game['avg_home_score']
        team_stats[home_team]['expected_points_against'] += game['avg_away_score']
        
        # Update away team stats
        team_stats[away_team]['games'] += 1
        team_stats[away_team]['expected_wins'] += game['away_win_prob']
        team_stats[away_team]['expected_losses'] += game['home_win_prob']
        team_stats[away_team]['expected_points_for'] += game['avg_away_score']
        team_stats[away_team]['expected_points_against'] += game['avg_home_score']
    
    # Convert to DataFrame
    records_df = pd.DataFrame.from_dict(team_stats, orient='index')
    records_df.index.name = 'team'
    records_df = records_df.reset_index()
    
    # Calculate additional metrics
    records_df['win_percentage'] = records_df['expected_wins'] / records_df['games']
    records_df['point_differential'] = (records_df['expected_points_for'] - 
                                       records_df['expected_points_against'])
    
    # Sort by expected wins
    records_df = records_df.sort_values('expected_wins', ascending=False)
    
    return records_df
