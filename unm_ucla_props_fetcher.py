#!/usr/bin/env python3
"""
UNM @ UCLA Player Props <PERSON> and Game Odds Collector (NCAAF)
- Uses The Odds API to find the event, pull spreads/totals, and fetch all available player props
- Saves raw JSON and a processed CSV under data/
"""

import os
import sys
import json
import pandas as pd
from typing import Optional, Dict, Any
from dotenv import load_dotenv

# Add src to path for imports
sys.path.append('src')
from proj.fetch_odds import (
    get_totals_spreads,
    implied_team_totals,
    get_player_props,
    find_game_event_id,
    process_player_props_response,
)

load_dotenv()

SPORT = "americanfootball_ncaaf"
HOME_TEAM = "UCLA Bruins"
AWAY_TEAM = "New Mexico Lobos"  # UNM


def _match_row(row: pd.Series) -> bool:
    """Return True if the odds row matches UNM @ UCLA (robust matching)."""
    home = str(row.get('home_team', '')).lower()
    away = str(row.get('away_team', '')).lower()
    return ("ucla" in home and ("new mexico" in away or "lobos" in away))


def fetch_game_context(api_key: str) -> Dict[str, Any]:
    """Fetch spreads/totals and implied team totals for UNM @ UCLA."""
    raw = get_totals_spreads(api_key, sport=SPORT)
    df = implied_team_totals(raw)

    # Attach event_id by matching original raw list entries
    event_id = None
    home_spread = None  # Home team's spread point from bookmaker (negative if favored)
    total = None

    # Find matching game in raw for event_id and detailed markets
    for game in raw:
        if not isinstance(game, dict):
            continue
        home = str(game.get('home_team', '')).lower()
        away = str(game.get('away_team', '')).lower()
        if ("ucla" in home and ("new mexico" in away or "lobos" in away)):
            event_id = game.get('id')
            # First bookmaker spreads/totals
            try:
                if game.get('bookmakers'):
                    bm = game['bookmakers'][0]
                    for m in bm.get('markets', []):
                        if m.get('key') == 'spreads':
                            for o in m.get('outcomes', []):
                                if o.get('name') == game.get('home_team'):
                                    home_spread = o.get('point')  # e.g., -14.5 if home favored
                                    break
                        if m.get('key') == 'totals':
                            for o in m.get('outcomes', []):
                                if o.get('name') == 'Over':
                                    total = o.get('point')
                                    break
            except Exception:
                pass
            break

    row = df[df.apply(_match_row, axis=1)].head(1)

    # Decide on spread/total values to use
    spread_val = float(row['spread'].iloc[0]) if not row.empty and pd.notna(row['spread'].iloc[0]) else (float(home_spread) if home_spread is not None else None)
    total_val = float(row['total'].iloc[0]) if not row.empty and pd.notna(row['total'].iloc[0]) else (float(total) if total is not None else None)

    # Compute implied totals based on conventional sign: home_spread is the home line
    # Home implied = total/2 - home_spread/2 ; Away implied = total/2 + home_spread/2
    if total_val is not None and spread_val is not None:
        home_it = total_val / 2 - spread_val / 2
        away_it = total_val / 2 + spread_val / 2
    else:
        # Fallback to df values if available
        home_it = float(row['home_implied_total'].iloc[0]) if not row.empty and pd.notna(row['home_implied_total'].iloc[0]) else None
        away_it = float(row['away_implied_total'].iloc[0]) if not row.empty and pd.notna(row['away_implied_total'].iloc[0]) else None

    context = {
        'home_team': 'UCLA Bruins',
        'away_team': 'New Mexico Lobos',
        'event_id': event_id,
        'spread': spread_val,
        'total': total_val,
        'home_implied_total': home_it,
        'away_implied_total': away_it,
        'commence_time': str(row['commence_time'].iloc[0]) if not row.empty else None,
    }
    return context


def fetch_props_for_event(api_key: str, event_id: str) -> Dict[str, Any]:
    """Fetch all available player props for the given NCAAF event."""
    return get_player_props(api_key, event_id, markets=None, sport=SPORT)


def main() -> None:
    api_key = os.getenv('ODDS_API_KEY')
    if not api_key:
        raise RuntimeError("ODDS_API_KEY not found in environment/.env")

    print("=== UNM @ UCLA — Fetching Odds & Props (NCAAF) ===")

    # 1) Game context (spread/total/implied)
    ctx = fetch_game_context(api_key)
    print(f"Home: {ctx['home_team']} | Away: {ctx['away_team']}")
    print(f"Event ID: {ctx['event_id']}")
    print(f"Spread (home): {ctx['spread']} | Total: {ctx['total']}")
    print(f"Implied — {ctx['home_team']}: {ctx['home_implied_total']}, {ctx['away_team']}: {ctx['away_implied_total']}")

    os.makedirs('data', exist_ok=True)
    pd.DataFrame([ctx]).to_csv('data/unm_ucla_game_context.csv', index=False)

    if not ctx['event_id']:
        # Try a more lenient event resolution if needed
        print("Did not find event_id via initial pass. Trying fallback search...")
        event_id = find_game_event_id(api_key, HOME_TEAM, AWAY_TEAM, sport=SPORT)
        ctx['event_id'] = event_id
        pd.DataFrame([ctx]).to_csv('data/unm_ucla_game_context.csv', index=False)

    if not ctx['event_id']:
        raise RuntimeError("Could not resolve event_id for UNM @ UCLA in The Odds API.")

    # 2) Player props fetch
    props_raw = fetch_props_for_event(api_key, ctx['event_id'])
    # Save raw JSON for inspection
    with open('data/unm_ucla_props_raw.json', 'w') as f:
        json.dump(props_raw, f, indent=2)
    print("Saved raw props to data/unm_ucla_props_raw.json")

    # 3) Process to tidy CSV (Over/Under pair markets)
    props_df = process_player_props_response(props_raw)
    if props_df.empty:
        print("WARNING: No over/under style props found in response (may be Yes/No markets only).")
    else:
        props_df.to_csv('data/unm_ucla_player_props.csv', index=False)
        print(f"Saved processed props CSV with {len(props_df)} rows to data/unm_ucla_player_props.csv")


if __name__ == "__main__":
    main()

