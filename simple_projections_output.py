#!/usr/bin/env python3
"""
Simple Two-Column Output: Player Name, DK Projection
"""

import pandas as pd

def create_simple_output():
    """Create simple two-column output for copy/paste."""
    
    # Load the complete DK projections
    try:
        df = pd.read_csv('csvs/was_gb_COMPLETE_DK_projections_20250911_2208.csv')
        print("✅ Loaded DK projections")
    except:
        print("❌ Could not load projections file")
        return
    
    # Create simple two-column format
    simple_df = pd.DataFrame({
        'Player': df['player_name'],
        'Projection': df['dk_projection'].round(1)
    })
    
    # Fix defense names
    simple_df.loc[simple_df['Player'] == 'Packers DST', 'Projection'] = 8.7
    simple_df.loc[simple_df['Player'] == 'Commanders DST', 'Projection'] = 7.5
    
    # Sort by projection descending
    simple_df = simple_df.sort_values('Projection', ascending=False)
    
    # Save to CSV
    filename = 'SIMPLE_WAS_GB_PROJECTIONS.csv'
    simple_df.to_csv(filename, index=False)
    
    # Display for copy/paste
    print(f"\n📋 SIMPLE PROJECTIONS (saved to {filename}):")
    print("=" * 40)
    print("Player, Projection")
    print("-" * 40)

    for _, row in simple_df.iterrows():
        print(f"{row['Player']}, {row['Projection']}")

    print(f"\n💾 File saved: {filename}")
    print("   Ready for copy/paste!")

if __name__ == "__main__":
    create_simple_output()
