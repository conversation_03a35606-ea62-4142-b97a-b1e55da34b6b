#!/usr/bin/env python3
"""
Analyzing What the Prop Odds Were Actually Telling Us
Focus on reading the market signals we missed, not arbitrary adjustments
"""

import pandas as pd
import numpy as np


def analyze_prop_market_signals():
    """Analyze what the prop markets were telling us that we missed."""
    
    print("🔍 ANALYZING WHAT THE PROP ODDS WERE ACTUALLY TELLING US")
    print("=" * 60)
    
    # Load our prop analysis
    try:
        props_df = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')
        review_df = pd.read_csv('csvs/was_gb_PROP_REVIEW_20250911_2202.csv')
    except Exception as e:
        print(f"Error loading data: {e}")
        return
    
    # Key actual results for comparison
    actual_results = {
        '<PERSON><PERSON>': {'pass_yds': 200, 'rush_yds': 17, 'pass_tds': 2},
        'Jordan Love': {'pass_yds': 292, 'rush_yds': 12, 'pass_tds': 2},
        '<PERSON>': {'rush_yds': 84, 'receptions': 0, 'rec_yds': 0},
        '<PERSON>': {'rec_yds': 124, 'receptions': 6},
        '<PERSON> McLaurin': {'rec_yds': 48, 'receptions': 5},
        'Deebo Samuel': {'rec_yds': 44, 'receptions': 7},
        'Romeo Doubs': {'rec_yds': 28, 'receptions': 3}
    }
    
    print("\n📊 MARKET SIGNAL ANALYSIS:")
    print("-" * 40)
    
    # 1. Line Disagreement Analysis
    print("\n1. LINE DISAGREEMENT SIGNALS WE MISSED:")
    high_disagreement = props_df[props_df['line_std'] > 1.5].copy()
    
    for _, row in high_disagreement.iterrows():
        player = row['player_name']
        market = row['market']
        line_std = row['line_std']
        sharp_public_diff = row['sharp_public_diff']
        
        print(f"   • {player} {market}: Line STD = {line_std:.2f}")
        print(f"     Sharp vs Public: {sharp_public_diff:.2f}")
        print(f"     → Market was UNCERTAIN - should have been more cautious")
    
    # 2. Sharp vs Public Money Analysis
    print("\n2. SHARP VS PUBLIC MONEY SIGNALS:")
    
    # Jayden Daniels rushing - what did the market know?
    jd_rush = props_df[
        (props_df['player_name'] == 'Jayden Daniels') & 
        (props_df['market'] == 'player_rush_yds')
    ]
    
    if not jd_rush.empty:
        row = jd_rush.iloc[0]
        print(f"\n   JAYDEN DANIELS RUSHING:")
        print(f"   • Line consensus: {row['line_consensus']}")
        print(f"   • Sharp line: {row['sharp_line']}")  
        print(f"   • Public line: {row['public_line']}")
        print(f"   • Sharp vs Public diff: {row['sharp_public_diff']}")
        print(f"   • Actual result: 17 yards")
        print(f"   → Sharp money was LOWER than public - they knew something!")
    
    # Jordan Love passing - market was right
    jl_pass = props_df[
        (props_df['player_name'] == 'Jordan Love') & 
        (props_df['market'] == 'player_pass_yds')
    ]
    
    if not jl_pass.empty:
        row = jl_pass.iloc[0]
        print(f"\n   JORDAN LOVE PASSING:")
        print(f"   • Line consensus: {row['line_consensus']}")
        print(f"   • Sharp line: {row['sharp_line']}")
        print(f"   • Public line: {row['public_line']}")
        print(f"   • Sharp vs Public diff: {row['sharp_public_diff']}")
        print(f"   • Actual result: 292 yards")
        print(f"   → Sharp money was HIGHER - they were right!")
    
    # 3. Line Range Analysis
    print("\n3. LINE RANGE SIGNALS (Market Uncertainty):")
    
    wide_ranges = props_df[props_df['line_range_max'] - props_df['line_range_min'] > 3].copy()
    
    for _, row in wide_ranges.iterrows():
        player = row['player_name']
        market = row['market']
        range_width = row['line_range_max'] - row['line_range_min']
        
        print(f"   • {player} {market}: Range = {range_width:.1f} points")
        print(f"     ({row['line_range_min']:.1f} to {row['line_range_max']:.1f})")
        print(f"     → Wide range = market uncertainty - avoid or bet smaller")
    
    # 4. Book Count Analysis
    print("\n4. BOOK COUNT SIGNALS (Market Confidence):")
    
    low_book_count = props_df[props_df['book_count'] <= 3].copy()
    
    for _, row in low_book_count.iterrows():
        player = row['player_name']
        market = row['market']
        book_count = row['book_count']
        
        print(f"   • {player} {market}: Only {book_count} books offering")
        print(f"     → Low book count = less market confidence")
    
    # 5. What We Should Have Learned from Confidence Scores
    print("\n5. CONFIDENCE SCORE ANALYSIS:")
    
    # Our confidence vs actual accuracy
    confidence_analysis = []
    
    key_props = [
        ('Jayden Daniels', 'player_rush_yds', 46.2, 17),
        ('Jayden Daniels', 'player_pass_yds', 227.2, 200),
        ('Jordan Love', 'player_pass_yds', 237.8, 292),
        ('Josh Jacobs', 'player_rush_yds', 78.3, 84),
        ('Tucker Kraft', 'player_reception_yds', 40.5, 124),
        ('Terry McLaurin', 'player_reception_yds', 51.9, 48),
        ('Deebo Samuel', 'player_reception_yds', 51.7, 44)
    ]
    
    for player, market, line, actual in key_props:
        prop_row = props_df[
            (props_df['player_name'] == player) & 
            (props_df['market'] == market)
        ]
        
        if not prop_row.empty:
            confidence = prop_row.iloc[0]['confidence_score']
            line_std = prop_row.iloc[0]['line_std']
            sharp_public_diff = prop_row.iloc[0]['sharp_public_diff']
            
            # Was our confidence justified?
            line_error = abs(actual - line)
            
            print(f"\n   {player} {market}:")
            print(f"   • Our confidence: {confidence:.3f}")
            print(f"   • Line STD: {line_std:.2f}")
            print(f"   • Sharp vs Public: {sharp_public_diff:.2f}")
            print(f"   • Line vs Actual error: {line_error:.1f}")
            
            if confidence > 0.9 and line_error > 10:
                print(f"   → HIGH CONFIDENCE, BIG MISS - overconfident!")
            elif confidence < 0.8 and line_error < 5:
                print(f"   → LOW CONFIDENCE, GOOD LINE - underconfident!")
    
    return analyze_market_reading_improvements()


def analyze_market_reading_improvements():
    """Identify specific improvements for reading prop markets."""
    
    print("\n\n🧠 HOW TO BETTER READ PROP ODDS:")
    print("=" * 40)
    
    print("\n1. SHARP VS PUBLIC MONEY SIGNALS:")
    print("   ✅ When sharp line < public line → Sharp money sees UNDER")
    print("   ✅ When sharp line > public line → Sharp money sees OVER") 
    print("   ✅ Difference >2 points = strong signal")
    print("   ❌ We ignored Jayden Daniels rush: Sharp 45.5 vs Public 46.2")
    print("      → Sharp money was right (actual: 17)")
    
    print("\n2. LINE STANDARD DEVIATION SIGNALS:")
    print("   ✅ Low STD (<1.0) = market consensus, more reliable")
    print("   ✅ High STD (>2.0) = market disagreement, less reliable")
    print("   ❌ We bet high STD props with same confidence as low STD")
    print("      → Should reduce bet size on high disagreement props")
    
    print("\n3. LINE RANGE ANALYSIS:")
    print("   ✅ Narrow range (1-2 points) = market confidence")
    print("   ✅ Wide range (4+ points) = market uncertainty")
    print("   ❌ We treated all props equally regardless of range")
    print("      → Avoid or reduce bets on wide range props")
    
    print("\n4. BOOK COUNT SIGNALS:")
    print("   ✅ 5+ books = liquid market, reliable pricing")
    print("   ✅ 2-3 books = thin market, less reliable")
    print("   ❌ We didn't weight by market liquidity")
    print("      → Reduce confidence on thin markets")
    
    print("\n5. CORRELATION ANALYSIS:")
    print("   ✅ Related props should move together")
    print("   ✅ If QB pass yards up, WR rec yards should be up")
    print("   ❌ We projected Daniels 317 pass yds but McLaurin only got 48")
    print("      → Check for internal consistency")
    
    print("\n6. GAME SCRIPT EMBEDDED IN LINES:")
    print("   ✅ Lines already factor in expected game flow")
    print("   ✅ If RB line seems low, market expects trailing/passing")
    print("   ❌ We added our own game script on top of market pricing")
    print("      → Trust the market's game script more")
    
    print("\n\n🎯 SPECIFIC IMPLEMENTATION:")
    print("-" * 30)
    
    print("\n• CONFIDENCE ADJUSTMENTS:")
    print("  - Line STD > 2.0: Reduce confidence by 20%")
    print("  - Sharp vs Public diff > 2: Follow sharp direction")
    print("  - Book count < 4: Reduce confidence by 15%")
    print("  - Line range > 4: Reduce confidence by 25%")
    
    print("\n• SHARP MONEY WEIGHTING:")
    print("  - Sharp line 2+ points different: 70% weight to sharp")
    print("  - Sharp line 1-2 points different: 60% weight to sharp")
    print("  - Sharp line <1 point different: 50% weight to sharp")
    
    print("\n• MARKET LIQUIDITY SCORING:")
    print("  - 6+ books: Full confidence")
    print("  - 4-5 books: 90% confidence")
    print("  - 2-3 books: 75% confidence")
    print("  - 1 book: 50% confidence")
    
    print("\n• CORRELATION CHECKS:")
    print("  - QB pass yards vs WR rec yards correlation")
    print("  - RB rush attempts vs team total correlation")
    print("  - Game total vs individual player totals")
    
    return True


if __name__ == "__main__":
    analyze_prop_market_signals()
