"""
Generate true "My Proj" values for Bears vs Vikings players using the original pipeline.
"""

import pandas as pd
import numpy as np
from typing import Dict, List
import sys
import os

# Add src to path
sys.path.append('src')

from proj.enhanced_projections import EnhancedProjectionE<PERSON><PERSON>

def get_bears_vikings_my_projections():
    """Generate My Proj values for Bears vs Vikings players."""
    print("=== GENERATING TRUE 'MY PROJ' VALUES FOR BEARS VS VIKINGS ===")
    
    # Initialize enhanced projection engine
    engine = EnhancedProjectionEngine()
    
    # Load data
    try:
        engine.load_data(
            depth_file='data/depth_week1.parquet',
            props_file='data/player_props.parquet', 
            odds_file='data/odds_week1.json'
        )
        print("✅ Data loaded successfully")
    except Exception as e:
        print(f"❌ Error loading data: {e}")
        return None
    
    # Generate projections for CHI and MIN
    try:
        projections_df = engine.generate_projections(['CHI', 'MIN'])
        print(f"✅ Generated {len(projections_df)} projections")
        
        # Load DraftKings players to match names
        dk_df = pd.read_csv('csvs/draftkings_showdown_NFL_2025-week-1_players (3).csv', skiprows=1)
        
        # Create mapping of DK players to My Proj values
        my_proj_mapping = {}
        
        for _, dk_player in dk_df.iterrows():
            if pd.isna(dk_player['Player']):
                continue
                
            dk_name = dk_player['Player']
            dk_team = dk_player['Team']
            
            # Find matching projection
            matching_proj = projections_df[
                (projections_df['team'] == dk_team) & 
                (projections_df['player_name'].str.contains(dk_name.split()[0], case=False, na=False))
            ]
            
            if len(matching_proj) > 0:
                my_proj_value = matching_proj['proj_mean'].iloc[0]
                my_proj_mapping[dk_name] = my_proj_value
                print(f"  {dk_name} ({dk_team}): {my_proj_value:.2f}")
            else:
                # Use FC Proj as fallback
                fc_proj = dk_player['FC Proj'] if pd.notna(dk_player['FC Proj']) else dk_player['VegasPts']
                my_proj_mapping[dk_name] = fc_proj
                print(f"  {dk_name} ({dk_team}): {fc_proj:.2f} (fallback)")
        
        return my_proj_mapping
        
    except Exception as e:
        print(f"❌ Error generating projections: {e}")
        return None

def main():
    """Main function."""
    my_proj_values = get_bears_vikings_my_projections()
    
    if my_proj_values:
        print(f"\n✅ Successfully generated My Proj values for {len(my_proj_values)} players")
        
        # Save to file for use in elite projections
        import json
        with open('bears_vikings_my_proj.json', 'w') as f:
            json.dump(my_proj_values, f, indent=2)
        print("💾 Saved to bears_vikings_my_proj.json")
    else:
        print("❌ Failed to generate My Proj values")

if __name__ == "__main__":
    main()
