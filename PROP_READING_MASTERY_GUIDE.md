# 🏈 NFL PROP READING MASTERY GUIDE
## Based on Ravens-Bills Week 1 2024 Analysis

### 🎯 **KEY DISCOVERY: Props Beat Vegas Points by 84% Correlation**

**Projection Accuracy Results:**
- **FC Projections**: 84.4% correlation, 6.3 MAE ✅
- **My Projections**: 81.6% correlation, 7.4 MAE ✅  
- **Vegas Points**: -8.3% correlation, 19.6 MAE ❌

---

## 💎 **THE 5 PILLARS OF PROP MASTERY**

### 1. **QB RUSHING PROPS = GOLD MINE**
**Ravens-Bills Evidence:**
- <PERSON>: 31.5 rush yards line → 41.8 fantasy points (1.7x multiplier)
- <PERSON>: 47.5 rush yards line → 29.4 fantasy points (1.2x multiplier)

**✅ Action Items:**
- Weight mobile QB rushing props 2x normal projection weight
- In competitive games (spread < 3), boost QB rushing TD probability by 40%
- Use rushing yards line as proxy for designed runs + scramble rate

### 2. **TD ANYTIME HIERARCHY WORKS**
**Validation Results:**
- TD Scorers averaged **37.8%** implied probability
- Non-scorers averaged **22.1%** implied probability
- **15.7% spread** = strong predictive signal

**Top Performers vs TD Probability:**
```
<PERSON>: 57.6% → 33.2 pts ✅
<PERSON> <PERSON>: 48.7% → 41.8 pts ✅
<PERSON> <PERSON>: 47.3% → 21.2 pts ✅
Zay Flowers: 33.0% → 31.1 pts ✅
Keon <PERSON>: 26.1% → 28.2 pts ✅
```

**✅ Action Items:**
- Rank all players by fair TD probability (remove vig)
- Captain priority: >40% TD probability
- Fade players with <20% TD probability in tournaments

### 3. **DEPTH CHART POSITION = VOLUME KING**
**Position Performance Hierarchy:**
```
QB1: 35.6 avg fantasy points
RB1: 27.2 avg fantasy points  
WR1: 21.8 avg fantasy points
WR2: 15.6 avg fantasy points
TE1: 8.2 avg fantasy points
```

**✅ Action Items:**
- Always check DraftKings pDepth column (Column 5)
- WR1/RB1 depth = automatic volume consideration
- WR2 can spike if WR1 struggles (Keon Coleman example)

### 4. **SHARP MONEY DETECTION**
**Sharp Money Indicators Found:**
- Low vig (<5%) = sharp market
- Balanced odds (within 20 points) = sharp action
- Half-point lines = sharp positioning

**Top Sharp Props from Ravens-Bills:**
```
Dalton Kincaid rec_yards: 4.8% vig
Josh Allen rush_yards: 4.7% vig  
Derrick Henry rush_yards: 4.7% vig
```

**✅ Action Items:**
- Calculate vig: (prob_over + prob_under) - 1
- Target props with <5% vig for sharp signals
- Fade round-number lines (50, 100 yards) = public money

### 5. **VOLUME PROPS = FANTASY CORRELATION**
**Strongest Correlations:**
- Receiving yards props → Fantasy output
- Rush attempts → RB ceiling games  
- Pass attempts → QB volume floors

**Evidence:**
- Zay Flowers: 57.5 rec yards line → 31.1 fantasy points
- Derrick Henry: 17.5 rush attempts → 33.2 fantasy points
- Josh Allen: 6.5 rush attempts → 41.8 fantasy points

**✅ Action Items:**
- Use volume props as projection anchors
- Receiving yards lines = target share expectations
- Rush attempt props = game script indicators

---

## 🚀 **TONIGHT'S GAME FRAMEWORK**

### **Step 1: Parse Props Hierarchy**
1. **QB Rushing Lines** (highest leverage)
2. **TD Anytime Probabilities** (scoring expectations)  
3. **Volume Props** (attempts, targets, yards)
4. **Sharp Money Signals** (low vig props)

### **Step 2: Build Projections**
**Weighting System:**
- **50% Props-Based** (volume + efficiency + scoring signals)
- **30% Model-Based** (FC Proj > My Proj > Vegas)
- **20% Game Context** (pace, script, weather)

### **Step 3: Captain Selection Priority**
1. **Mobile QBs** in competitive games (Josh Allen model)
2. **RB1s** with goal-line work (Derrick Henry model)  
3. **WR1s** with target dominance (Zay Flowers model)
4. **Rookie WRs** with usage (Keon Coleman model)

### **Step 4: Correlation Strategy**
- **Stack QB + Pass Catchers** from same team
- **Bring-back opposing WR1** for game script hedge
- **Include kicker** in 47+ total games
- **Fade DST** in high-total shootouts

---

## ⚡ **IMMEDIATE IMPLEMENTATION**

### **For Tonight's Slate:**

1. **Find QB rushing props first**
   - Mobile QBs with 25+ yard lines = ceiling plays
   - Competitive spread = rushing TD boost

2. **Rank by TD probability**
   - Remove vig: fair_prob = prob_over / (prob_over + prob_under)
   - >40% = Captain consideration
   - 25-40% = Flex consideration  
   - <25% = Fade in tournaments

3. **Check depth charts**
   - DraftKings pDepth column
   - WR1/RB1 = volume locks
   - WR2/TE1 = leverage plays

4. **Identify sharp props**
   - Calculate vig on all props
   - <5% vig = sharp money signal
   - Balanced odds = professional action

5. **Build correlations**
   - QB + 2 pass catchers + opposing WR1 + kicker
   - Test multiple Captain scenarios
   - Weight by TD probability hierarchy

---

## 🎯 **SUCCESS METRICS TO TRACK**

- **Prop Accuracy**: How often volume props predict performance
- **TD Hierarchy**: Correlation between implied probability and actual TDs
- **Sharp Money**: Performance of low-vig props vs high-vig props  
- **Depth Chart**: Volume correlation by position depth
- **Projection Blend**: Optimal weighting of props vs models vs context

---

**🏆 Bottom Line: Props contain more signal than Vegas points. Use this framework to extract maximum edge from betting markets for tonight's slate.**
