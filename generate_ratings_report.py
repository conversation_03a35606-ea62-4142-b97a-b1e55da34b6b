#!/usr/bin/env python3
"""
Generate NFL Team Ratings Report
Creates offensive/defensive rankings and exploitables summary
"""

import pandas as pd
import json
from pathlib import Path

def load_data():
    """Load the generated ratings data"""
    df_ranks = pd.read_parquet('models/team_ranks.parquet')
    df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
    return df_ranks, df_holes_levers

def generate_offensive_rankings(df_ranks):
    """Generate offensive rankings report"""
    
    print("🏈 NFL OFFENSIVE RANKINGS (Through Week 2)")
    print("=" * 60)
    
    # Overall offensive ranking by points per drive
    off_cols = ['team', 'OFF_ppd_rating_rank', 'OFF_ppd_rating_z', 
                'OFF_nyd_rating_rank', 'OFF_nyd_rating_z',
                'OFF_rush_ypa_rating_rank', 'OFF_rush_ypa_rating_z',
                'OFF_explosive_rate_rating_rank', 'OFF_explosive_rate_rating_z']
    
    available_cols = [col for col in off_cols if col in df_ranks.columns]
    df_off = df_ranks[available_cols].copy()
    
    # Sort by points per drive rank (1 = best)
    ppd_rank_col = 'OFF_ppd_rating_rank' if 'OFF_ppd_rating_rank' in df_off.columns else None
    if ppd_rank_col:
        df_off = df_off.sort_values(ppd_rank_col)
    
    print("\n📊 TOP OFFENSES (Points Per Drive)")
    print("-" * 40)
    for i, (_, row) in enumerate(df_off.head(10).iterrows(), 1):
        team = row['team']
        ppd_rank = int(row.get('OFF_ppd_rating_rank', 99)) if 'OFF_ppd_rating_rank' in row else 99
        ppd_z = row.get('OFF_ppd_rating_z', 0) if 'OFF_ppd_rating_z' in row else 0
        print(f"{i:2d}. {team:12s} (Rank: {ppd_rank:2d}, Z: {ppd_z:+.2f})")
    
    print("\n🎯 PASS OFFENSE RANKINGS")
    print("-" * 30)
    nyd_rank_col = 'OFF_nyd_rating_rank' if 'OFF_nyd_rating_rank' in df_off.columns else None
    if nyd_rank_col:
        df_pass = df_off.sort_values(nyd_rank_col)
        for i, (_, row) in enumerate(df_pass.head(10).iterrows(), 1):
            team = row['team']
            nyd_rank = int(row.get('OFF_nyd_rating_rank', 99))
            nyd_z = row.get('OFF_nyd_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {nyd_rank:2d}, Z: {nyd_z:+.2f})")
    
    print("\n🏃 RUSH OFFENSE RANKINGS")
    print("-" * 30)
    rush_rank_col = 'OFF_rush_ypa_rating_rank' if 'OFF_rush_ypa_rating_rank' in df_off.columns else None
    if rush_rank_col:
        df_rush = df_off.sort_values(rush_rank_col)
        for i, (_, row) in enumerate(df_rush.head(10).iterrows(), 1):
            team = row['team']
            rush_rank = int(row.get('OFF_rush_ypa_rating_rank', 99))
            rush_z = row.get('OFF_rush_ypa_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {rush_rank:2d}, Z: {rush_z:+.2f})")
    
    print("\n💥 EXPLOSIVE OFFENSE RANKINGS")
    print("-" * 35)
    exp_rank_col = 'OFF_explosive_rate_rating_rank' if 'OFF_explosive_rate_rating_rank' in df_off.columns else None
    if exp_rank_col:
        df_exp = df_off.sort_values(exp_rank_col)
        for i, (_, row) in enumerate(df_exp.head(10).iterrows(), 1):
            team = row['team']
            exp_rank = int(row.get('OFF_explosive_rate_rating_rank', 99))
            exp_z = row.get('OFF_explosive_rate_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {exp_rank:2d}, Z: {exp_z:+.2f})")

def generate_defensive_rankings(df_ranks):
    """Generate defensive rankings report"""
    
    print("\n\n🛡️  NFL DEFENSIVE RANKINGS (Through Week 2)")
    print("=" * 60)
    
    # Defensive ranking columns
    def_cols = ['team', 'DEF_ppd_allowed_rating_rank', 'DEF_ppd_allowed_rating_z',
                'DEF_nyd_allowed_rating_rank', 'DEF_nyd_allowed_rating_z',
                'DEF_rush_ypa_allowed_rating_rank', 'DEF_rush_ypa_allowed_rating_z',
                'DEF_explosive_rate_allowed_rating_rank', 'DEF_explosive_rate_allowed_rating_z']
    
    available_def_cols = [col for col in def_cols if col in df_ranks.columns]
    df_def = df_ranks[available_def_cols].copy()
    
    print("\n📊 TOP DEFENSES (Points Per Drive Allowed)")
    print("-" * 45)
    ppd_def_rank_col = 'DEF_ppd_allowed_rating_rank' if 'DEF_ppd_allowed_rating_rank' in df_def.columns else None
    if ppd_def_rank_col:
        df_def_ppd = df_def.sort_values(ppd_def_rank_col)
        for i, (_, row) in enumerate(df_def_ppd.head(10).iterrows(), 1):
            team = row['team']
            ppd_rank = int(row.get('DEF_ppd_allowed_rating_rank', 99))
            ppd_z = row.get('DEF_ppd_allowed_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {ppd_rank:2d}, Z: {ppd_z:+.2f})")
    
    print("\n🎯 PASS DEFENSE RANKINGS")
    print("-" * 30)
    nyd_def_rank_col = 'DEF_nyd_allowed_rating_rank' if 'DEF_nyd_allowed_rating_rank' in df_def.columns else None
    if nyd_def_rank_col:
        df_pass_def = df_def.sort_values(nyd_def_rank_col)
        for i, (_, row) in enumerate(df_pass_def.head(10).iterrows(), 1):
            team = row['team']
            nyd_rank = int(row.get('DEF_nyd_allowed_rating_rank', 99))
            nyd_z = row.get('DEF_nyd_allowed_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {nyd_rank:2d}, Z: {nyd_z:+.2f})")
    
    print("\n🏃 RUSH DEFENSE RANKINGS")
    print("-" * 30)
    rush_def_rank_col = 'DEF_rush_ypa_allowed_rating_rank' if 'DEF_rush_ypa_allowed_rating_rank' in df_def.columns else None
    if rush_def_rank_col:
        df_rush_def = df_def.sort_values(rush_def_rank_col)
        for i, (_, row) in enumerate(df_rush_def.head(10).iterrows(), 1):
            team = row['team']
            rush_rank = int(row.get('DEF_rush_ypa_allowed_rating_rank', 99))
            rush_z = row.get('DEF_rush_ypa_allowed_rating_z', 0)
            print(f"{i:2d}. {team:12s} (Rank: {rush_rank:2d}, Z: {rush_z:+.2f})")

def generate_exploitables_report(df_holes_levers):
    """Generate exploitables (defense holes) report"""
    
    print("\n\n🎯 EXPLOITABLE DEFENSES (Holes ≥ +0.7σ)")
    print("=" * 50)
    
    hole_cols = [col for col in df_holes_levers.columns if col.startswith('hole_')]
    
    exploitables = []
    
    for _, row in df_holes_levers.iterrows():
        team = row['team']
        team_holes = []
        
        for hole_col in hole_cols:
            hole_value = row.get(hole_col, 0)
            if hole_value >= 0.7:  # Exploitable threshold
                hole_name = hole_col.replace('hole_', '').replace('_', ' ').title()
                team_holes.append({
                    'hole': hole_name,
                    'z_score': hole_value
                })
        
        if team_holes:
            exploitables.append({
                'team': team,
                'holes': team_holes,
                'total_holes': len(team_holes)
            })
    
    # Sort by number of exploitable holes
    exploitables.sort(key=lambda x: x['total_holes'], reverse=True)
    
    if exploitables:
        for item in exploitables:
            team = item['team']
            holes = item['holes']
            print(f"\n🔴 {team} ({len(holes)} exploitable areas):")
            for hole in sorted(holes, key=lambda x: x['z_score'], reverse=True):
                print(f"   • {hole['hole']:20s} (+{hole['z_score']:.2f}σ)")
    else:
        print("\n✅ No teams with exploitable holes ≥ +0.7σ found")
    
    print("\n\n🚀 OFFENSIVE LEVERS (Strengths ≥ +0.5σ)")
    print("=" * 45)
    
    lever_cols = [col for col in df_holes_levers.columns if col.startswith('lever_')]
    
    strengths = []
    
    for _, row in df_holes_levers.iterrows():
        team = row['team']
        team_levers = []
        
        for lever_col in lever_cols:
            lever_value = row.get(lever_col, 0)
            if lever_value >= 0.5:  # Strength threshold
                lever_name = lever_col.replace('lever_', '').replace('_', ' ').title()
                team_levers.append({
                    'lever': lever_name,
                    'z_score': lever_value
                })
        
        if team_levers:
            strengths.append({
                'team': team,
                'levers': team_levers,
                'total_levers': len(team_levers)
            })
    
    # Sort by number of strengths
    strengths.sort(key=lambda x: x['total_levers'], reverse=True)
    
    if strengths:
        for item in strengths:
            team = item['team']
            levers = item['levers']
            print(f"\n🟢 {team} ({len(levers)} key strengths):")
            for lever in sorted(levers, key=lambda x: x['z_score'], reverse=True):
                print(f"   • {lever['lever']:20s} (+{lever['z_score']:.2f}σ)")
    else:
        print("\n⚪ No teams with significant offensive levers ≥ +0.5σ found")

def main():
    """Generate complete ratings report"""
    
    try:
        df_ranks, df_holes_levers = load_data()
        
        print("🏈 NFL TEAM RATINGS & EXPLOITABLES REPORT")
        print("=" * 60)
        print(f"📅 Data through Week {df_ranks['as_of_week'].max()}")
        print(f"🏈 {len(df_ranks)} teams analyzed")
        print(f"🎯 Using λ={df_ranks['recency_weight'].iloc[0]} recency weighting")
        
        # Generate reports
        generate_offensive_rankings(df_ranks)
        generate_defensive_rankings(df_ranks)
        generate_exploitables_report(df_holes_levers)
        
        print("\n\n" + "=" * 60)
        print("📊 Report complete! Data saved in:")
        print("   • models/team_ranks.parquet")
        print("   • models/holes_and_levers.parquet")
        print("   • findings/asof_week_*/team_*.json")
        
    except Exception as e:
        print(f"❌ Error generating report: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
