#!/usr/bin/env python3
"""
Final Cowboys vs Giants Projections with Manual TD Props
Incorporates the anytime TD odds you provided to complete the projections.
"""

import pandas as pd
import numpy as np

def american_to_prob(odds):
    """Convert American odds to implied probability."""
    if odds > 0:
        return 100 / (odds + 100)
    else:
        return abs(odds) / (abs(odds) + 100)

def create_final_projections():
    """Create final projections with TD props included."""
    
    # Load the base projections from our elite system (latest with receiving yards)
    base_projections = pd.read_csv('cowboys_giants_elite_projections_20250912_2205.csv')
    
    # Manual anytime TD odds from your data
    anytime_td_odds = {
        'CeeDee <PERSON>': -105,
        '<PERSON><PERSON><PERSON>': +105,
        '<PERSON>': +160,
        '<PERSON>.': +135,
        '<PERSON>': +135,
        '<PERSON>': +205,
        '<PERSON>': +390,
        '<PERSON><PERSON><PERSON>ae <PERSON>rpin': +450,
        '<PERSON>': +390,
        '<PERSON>\'<PERSON>': +380,
        '<PERSON><PERSON><PERSON>': +550,
        '<PERSON>': +425,
        '<PERSON>': +500,
        '<PERSON><PERSON>': +700,
        '<PERSON>': +500,
        '<PERSON>': +900,
        '<PERSON>': +1000
    }
    
    print("🏈 ADDING ANYTIME TD PROJECTIONS...")
    print("=" * 45)
    
    # Create final projections
    final_projections = []
    
    for _, row in base_projections.iterrows():
        player = row['Player']
        base_projection = row['Projection']
        
        # Add TD projection if available
        td_points = 0.0
        if player in anytime_td_odds:
            odds = anytime_td_odds[player]
            implied_prob = american_to_prob(odds)
            
            # Convert to expected TDs using elite methodology
            # Anytime TD probability ≈ expected TDs for most players
            expected_tds = implied_prob
            
            # Apply position-specific adjustments
            if any(pos in player.lower() for pos in ['prescott', 'wilson']):  # QBs
                # QBs: mostly rushing TDs, some receiving
                expected_tds *= 0.8  # Slightly conservative for QB rushing TDs
                td_points = expected_tds * 6.0  # 6 pts per TD
            else:
                # Skill position players: full TD value
                td_points = expected_tds * 6.0
            
            print(f"{player:<20} {odds:>+4d} → {implied_prob:.1%} → {expected_tds:.3f} TDs → +{td_points:.2f} pts")
        
        final_projection = base_projection + td_points
        
        final_projections.append({
            'Player': player,
            'Projection': round(final_projection, 2)
        })
    
    # Convert to DataFrame and sort
    final_df = pd.DataFrame(final_projections)
    final_df = final_df.sort_values('Projection', ascending=False)
    
    return final_df

def main():
    """Generate final projections with TD props."""
    
    print("🚀 FINAL COWBOYS VS GIANTS ELITE PROJECTIONS")
    print("=" * 50)
    print("Combining elite prop analysis + manual TD odds")
    print()
    
    # Generate final projections
    projections = create_final_projections()
    
    print(f"\n🏆 FINAL TOP 15 PROJECTIONS:")
    print("-" * 45)
    for i, (_, player) in enumerate(projections.head(15).iterrows(), 1):
        print(f"{i:2d}. {player['Player']:<25} {player['Projection']:>6.2f} pts")
    
    # Save final projections
    filename = 'cowboys_giants_FINAL_elite_projections.csv'
    projections.to_csv(filename, index=False)
    
    print(f"\n💾 Saved final projections to: {filename}")
    print(f"📊 Total players: {len(projections)}")
    
    return projections

if __name__ == "__main__":
    main()
