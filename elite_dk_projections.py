#!/usr/bin/env python3
"""
Elite DraftKings NFL Projections - Browns @ Ravens
Uses team ratings, market data, and elite prop reading methodology
"""

import pandas as pd
import numpy as np
import requests
import json
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

class EliteDKProjections:
    def __init__(self):
        self.api_key = None  # Will need to set this for live odds
        self.matchup = "Browns @ Ravens"
        self.away_team = "Browns"
        self.home_team = "Ravens"
        
        # Load team ratings data
        self.load_team_ratings()
        
        # Load player pool
        self.load_player_pool()
        
        # DraftKings scoring
        self.dk_scoring = {
            'pass_yard': 0.04,
            'pass_td': 4,
            'pass_int': -1,
            'rush_yard': 0.1,
            'rush_td': 6,
            'rec_yard': 0.1,
            'reception': 1,
            'rec_td': 6,
            'fumble_lost': -1,
            'fg_made': 3,
            'xp_made': 1,
            'dst_sack': 1,
            'dst_int': 2,
            'dst_fumble_rec': 2,
            'dst_safety': 2,
            'dst_td': 6,
            'dst_pts_allowed_0': 10,
            'dst_pts_allowed_1_6': 7,
            'dst_pts_allowed_7_13': 4,
            'dst_pts_allowed_14_20': 1,
            'dst_pts_allowed_21_27': 0,
            'dst_pts_allowed_28_34': -1,
            'dst_pts_allowed_35_plus': -4
        }
    
    def load_team_ratings(self):
        """Load team ratings and holes/levers data"""
        try:
            self.df_ranks = pd.read_parquet('models/team_ranks.parquet')
            self.df_holes_levers = pd.read_parquet('models/holes_and_levers.parquet')
            print("✅ Loaded team ratings data")
        except Exception as e:
            print(f"❌ Error loading team ratings: {e}")
            self.df_ranks = pd.DataFrame()
            self.df_holes_levers = pd.DataFrame()
    
    def load_player_pool(self):
        """Load DraftKings player pool for Browns @ Ravens"""
        try:
            df_all = pd.read_csv('csvs/draftkings_NFL_2025-week-2_players.csv')

            # Check column names
            print(f"📋 Available columns: {list(df_all.columns)}")

            # Filter for Browns and Ravens players using correct column names
            if 'Team' in df_all.columns:
                team_col = 'Team'
            else:
                # Find the team column (might be different name)
                possible_team_cols = [col for col in df_all.columns if 'team' in col.lower()]
                team_col = possible_team_cols[0] if possible_team_cols else None

            if team_col:
                self.df_players = df_all[
                    df_all[team_col].isin(['CLE', 'BAL', 'Browns', 'Ravens'])
                ].copy()

                if self.df_players.empty:
                    # Try with Opp column
                    opp_col = 'Opp' if 'Opp' in df_all.columns else None
                    if opp_col:
                        self.df_players = df_all[
                            (df_all[team_col].str.contains('CLE|BAL', na=False)) |
                            (df_all[opp_col].str.contains('CLE|BAL', na=False))
                        ].copy()
            else:
                self.df_players = pd.DataFrame()

            print(f"✅ Loaded {len(self.df_players)} players for Browns @ Ravens")

            # Show sample of loaded players
            if not self.df_players.empty:
                print("📋 Sample players loaded:")
                for _, row in self.df_players.head(5).iterrows():
                    player_name = row.get('Player', 'Unknown')
                    team = row.get(team_col, 'Unknown')
                    pos = row.get('Pos', 'Unknown')
                    print(f"   • {player_name} ({team}, {pos})")

        except Exception as e:
            print(f"❌ Error loading player pool: {e}")
            self.df_players = pd.DataFrame()
    
    def get_team_data(self, team_name: str) -> Tuple[Optional[pd.Series], Optional[pd.Series]]:
        """Get team ratings and holes/levers data"""
        if self.df_ranks.empty or self.df_holes_levers.empty:
            return None, None
        
        # Find team in ratings data
        team_variants = {
            'Browns': ['Browns', 'CLE', 'Cleveland Browns'],
            'Ravens': ['Ravens', 'BAL', 'Baltimore Ravens']
        }
        
        team_row = None
        hl_row = None
        
        for variant in team_variants.get(team_name, [team_name]):
            team_matches = self.df_ranks[
                self.df_ranks['team'].str.contains(variant, case=False, na=False)
            ]
            if not team_matches.empty:
                team_row = team_matches.iloc[0]
                break
        
        for variant in team_variants.get(team_name, [team_name]):
            hl_matches = self.df_holes_levers[
                self.df_holes_levers['team'].str.contains(variant, case=False, na=False)
            ]
            if not hl_matches.empty:
                hl_row = hl_matches.iloc[0]
                break
        
        return team_row, hl_row
    
    def compute_matchup_edges(self) -> Dict[str, float]:
        """Compute matchup edges using team ratings"""
        print("\n🎯 Computing matchup edges...")
        
        # Get team data
        browns_ratings, browns_hl = self.get_team_data('Browns')
        ravens_ratings, ravens_hl = self.get_team_data('Ravens')
        
        if any(x is None for x in [browns_ratings, browns_hl, ravens_ratings, ravens_hl]):
            print("❌ Missing team data, using default edges")
            return self.get_default_edges()
        
        edges = {}
        
        # Browns offensive edges vs Ravens defense
        edges['browns_pass_edge'] = (
            browns_hl.get('lever_explosive_pass', 0) - 
            ravens_hl.get('hole_pass_eff', 0)
        )
        
        edges['browns_rush_edge'] = (
            browns_hl.get('lever_ppd', 0) - 
            ravens_hl.get('hole_rush_eff', 0)
        )
        
        edges['browns_rz_edge'] = (
            browns_hl.get('lever_rz', 0) - 
            ravens_hl.get('hole_rz', 0)
        )
        
        edges['browns_explosive_edge'] = (
            browns_hl.get('lever_explosive_pass', 0) - 
            ravens_hl.get('hole_explosive_pass', 0)
        )
        
        edges['browns_protection_edge'] = (
            browns_hl.get('lever_protection', 0) - 
            ravens_hl.get('hole_pressure', 0)
        )
        
        # Ravens offensive edges vs Browns defense
        edges['ravens_pass_edge'] = (
            ravens_hl.get('lever_explosive_pass', 0) - 
            browns_hl.get('hole_pass_eff', 0)
        )
        
        edges['ravens_rush_edge'] = (
            ravens_hl.get('lever_ppd', 0) - 
            browns_hl.get('hole_rush_eff', 0)
        )
        
        edges['ravens_rz_edge'] = (
            ravens_hl.get('lever_rz', 0) - 
            browns_hl.get('hole_rz', 0)
        )
        
        edges['ravens_explosive_edge'] = (
            ravens_hl.get('lever_explosive_pass', 0) - 
            browns_hl.get('hole_explosive_pass', 0)
        )
        
        edges['ravens_protection_edge'] = (
            ravens_hl.get('lever_protection', 0) - 
            browns_hl.get('hole_pressure', 0)
        )
        
        # Print key edges
        print(f"🔥 Ravens Pass Edge: {edges['ravens_pass_edge']:+.2f}σ")
        print(f"🔥 Ravens Rush Edge: {edges['ravens_rush_edge']:+.2f}σ")
        print(f"🔥 Ravens Explosive Edge: {edges['ravens_explosive_edge']:+.2f}σ")
        print(f"⚡ Browns Pass Edge: {edges['browns_pass_edge']:+.2f}σ")
        print(f"⚡ Browns Rush Edge: {edges['browns_rush_edge']:+.2f}σ")
        
        return edges
    
    def get_default_edges(self) -> Dict[str, float]:
        """Default edges based on known team strengths"""
        return {
            'ravens_pass_edge': 1.2,  # Ravens strong passing vs Browns weak pass D
            'ravens_rush_edge': 0.8,  # Ravens strong rushing
            'ravens_rz_edge': 0.6,
            'ravens_explosive_edge': 1.5,  # Ravens explosive offense
            'ravens_protection_edge': 0.4,
            'browns_pass_edge': -0.5,  # Browns weaker passing
            'browns_rush_edge': -0.3,
            'browns_rz_edge': -0.2,
            'browns_explosive_edge': -0.4,
            'browns_protection_edge': -0.6
        }
    
    def get_mock_market_data(self) -> Dict:
        """Mock market data for Browns @ Ravens"""
        return {
            'game_total': 47.5,
            'spread': -9.5,  # Ravens favored by 9.5
            'ravens_implied': 28.5,
            'browns_implied': 19.0,
            'market_strength': 0.8,  # High confidence
            'market_uncertainty': 0.2,
            'player_props': {
                'Lamar Jackson': {
                    'pass_yards': 275.5,
                    'pass_tds': 2.5,
                    'rush_yards': 65.5,
                    'rush_tds': 0.5
                },
                'Derrick Henry': {
                    'rush_yards': 85.5,
                    'rush_tds': 1.5
                },
                'Zay Flowers': {
                    'rec_yards': 65.5,
                    'receptions': 5.5
                },
                'Mark Andrews': {
                    'rec_yards': 45.5,
                    'receptions': 3.5
                },
                'Joe Flacco': {
                    'pass_yards': 225.5,
                    'pass_tds': 1.5
                },
                'Jerry Jeudy': {
                    'rec_yards': 55.5,
                    'receptions': 4.5
                },
                'David Njoku': {
                    'rec_yards': 40.5,
                    'receptions': 3.5
                }
            }
        }
    
    def model_team_totals(self, edges: Dict[str, float], market_data: Dict) -> Dict:
        """Model pre-market team totals using edges"""
        print("\n📈 Modeling team totals...")
        
        # Base projections (league average)
        base_ravens = {
            'plays': 65,
            'pass_attempts': 35,
            'pass_yards': 250,
            'pass_tds': 2.0,
            'rush_attempts': 28,
            'rush_yards': 120,
            'rush_tds': 1.2,
            'points': 24
        }
        
        base_browns = {
            'plays': 62,
            'pass_attempts': 38,
            'pass_yards': 230,
            'pass_tds': 1.8,
            'rush_attempts': 22,
            'rush_yards': 95,
            'rush_tds': 0.8,
            'points': 20
        }
        
        # Apply edges to Ravens
        ravens_adj = base_ravens.copy()
        ravens_adj['pass_yards'] *= (1 + 0.06 * edges['ravens_pass_edge'])
        ravens_adj['pass_tds'] *= (1 + 0.08 * edges['ravens_rz_edge'])
        ravens_adj['rush_yards'] *= (1 + 0.05 * edges['ravens_rush_edge'])
        ravens_adj['rush_tds'] *= (1 + 0.07 * edges['ravens_rz_edge'])
        ravens_adj['points'] *= (1 + 0.04 * (edges['ravens_pass_edge'] + edges['ravens_rush_edge']))
        
        # Apply edges to Browns
        browns_adj = base_browns.copy()
        browns_adj['pass_yards'] *= (1 + 0.06 * edges['browns_pass_edge'])
        browns_adj['pass_tds'] *= (1 + 0.08 * edges['browns_rz_edge'])
        browns_adj['rush_yards'] *= (1 + 0.05 * edges['browns_rush_edge'])
        browns_adj['rush_tds'] *= (1 + 0.07 * edges['browns_rz_edge'])
        browns_adj['points'] *= (1 + 0.04 * (edges['browns_pass_edge'] + edges['browns_rush_edge']))
        
        print(f"🔥 Ravens Model: {ravens_adj['points']:.1f} pts, {ravens_adj['pass_yards']:.0f} pass yds")
        print(f"⚡ Browns Model: {browns_adj['points']:.1f} pts, {browns_adj['pass_yards']:.0f} pass yds")
        
        return {'ravens': ravens_adj, 'browns': browns_adj}

    def blend_with_props(self, model_totals: Dict, market_data: Dict) -> Dict:
        """Blend model projections with prop market data"""
        print("\n⚖️ Blending model with props...")

        # Alpha blending factor (0.35 base, adjust based on market strength)
        alpha = 0.35 + 0.3 * market_data['market_strength']
        print(f"📊 Using α = {alpha:.2f} (market strength: {market_data['market_strength']:.2f})")

        blended = {}

        # Ravens blending
        ravens_model = model_totals['ravens']
        ravens_props = market_data['player_props']

        # Lamar Jackson
        lamar_model_pass_yds = ravens_model['pass_yards'] * 0.85  # QB gets ~85% of team pass yards
        lamar_model_rush_yds = ravens_model['rush_yards'] * 0.25  # QB gets ~25% of team rush yards

        lamar_final_pass_yds = (1 - alpha) * lamar_model_pass_yds + alpha * ravens_props['Lamar Jackson']['pass_yards']
        lamar_final_rush_yds = (1 - alpha) * lamar_model_rush_yds + alpha * ravens_props['Lamar Jackson']['rush_yards']

        blended['Lamar Jackson'] = {
            'pass_yards': lamar_final_pass_yds,
            'pass_tds': (1 - alpha) * ravens_model['pass_tds'] * 0.9 + alpha * ravens_props['Lamar Jackson']['pass_tds'],
            'rush_yards': lamar_final_rush_yds,
            'rush_tds': (1 - alpha) * ravens_model['rush_tds'] * 0.3 + alpha * ravens_props['Lamar Jackson']['rush_tds'],
            'pass_attempts': ravens_model['pass_attempts'],
            'interceptions': 0.8  # Model estimate
        }

        # Derrick Henry
        henry_model_rush_yds = ravens_model['rush_yards'] * 0.65  # RB1 gets ~65% of team rush yards
        blended['Derrick Henry'] = {
            'rush_yards': (1 - alpha) * henry_model_rush_yds + alpha * ravens_props['Derrick Henry']['rush_yards'],
            'rush_tds': (1 - alpha) * ravens_model['rush_tds'] * 0.6 + alpha * ravens_props['Derrick Henry']['rush_tds'],
            'rush_attempts': ravens_model['rush_attempts'] * 0.65,
            'targets': 3,
            'receptions': 2.5,
            'rec_yards': 18
        }

        # Zay Flowers
        flowers_model_rec_yds = lamar_final_pass_yds * 0.25  # WR1 gets ~25% of pass yards
        blended['Zay Flowers'] = {
            'targets': 8,
            'receptions': (1 - alpha) * 6 + alpha * ravens_props['Zay Flowers']['receptions'],
            'rec_yards': (1 - alpha) * flowers_model_rec_yds + alpha * ravens_props['Zay Flowers']['rec_yards'],
            'rec_tds': ravens_model['pass_tds'] * 0.3
        }

        # Mark Andrews
        andrews_model_rec_yds = lamar_final_pass_yds * 0.18  # TE1 gets ~18% of pass yards
        blended['Mark Andrews'] = {
            'targets': 6,
            'receptions': (1 - alpha) * 4 + alpha * ravens_props['Mark Andrews']['receptions'],
            'rec_yards': (1 - alpha) * andrews_model_rec_yds + alpha * ravens_props['Mark Andrews']['rec_yards'],
            'rec_tds': ravens_model['pass_tds'] * 0.25
        }

        # Browns blending
        browns_model = model_totals['browns']

        # Joe Flacco
        flacco_model_pass_yds = browns_model['pass_yards'] * 0.85
        blended['Joe Flacco'] = {
            'pass_yards': (1 - alpha) * flacco_model_pass_yds + alpha * ravens_props['Joe Flacco']['pass_yards'],
            'pass_tds': (1 - alpha) * browns_model['pass_tds'] * 0.9 + alpha * ravens_props['Joe Flacco']['pass_tds'],
            'pass_attempts': browns_model['pass_attempts'],
            'interceptions': 1.2,  # Higher INT rate vs Ravens D
            'rush_yards': 5,
            'rush_tds': 0.1
        }

        # Jerry Jeudy
        jeudy_model_rec_yds = flacco_model_pass_yds * 0.28  # WR1 gets larger share
        blended['Jerry Jeudy'] = {
            'targets': 9,
            'receptions': (1 - alpha) * 5.5 + alpha * ravens_props['Jerry Jeudy']['receptions'],
            'rec_yards': (1 - alpha) * jeudy_model_rec_yds + alpha * ravens_props['Jerry Jeudy']['rec_yards'],
            'rec_tds': browns_model['pass_tds'] * 0.35
        }

        # David Njoku
        njoku_model_rec_yds = flacco_model_pass_yds * 0.20
        blended['David Njoku'] = {
            'targets': 6,
            'receptions': (1 - alpha) * 4 + alpha * ravens_props['David Njoku']['receptions'],
            'rec_yards': (1 - alpha) * njoku_model_rec_yds + alpha * ravens_props['David Njoku']['rec_yards'],
            'rec_tds': browns_model['pass_tds'] * 0.25
        }

        # Add other key players
        blended['Rashod Bateman'] = {
            'targets': 5, 'receptions': 3.2, 'rec_yards': 42, 'rec_tds': 0.3
        }

        blended['Justice Hill'] = {
            'rush_attempts': 8, 'rush_yards': 35, 'rush_tds': 0.2,
            'targets': 2, 'receptions': 1.8, 'rec_yards': 15
        }

        blended['Jerome Ford'] = {
            'rush_attempts': 12, 'rush_yards': 48, 'rush_tds': 0.4,
            'targets': 3, 'receptions': 2.5, 'rec_yards': 20
        }

        blended['Cedric Tillman'] = {
            'targets': 4, 'receptions': 2.8, 'rec_yards': 35, 'rec_tds': 0.2
        }

        # Add DST projections
        blended['Ravens DST'] = {
            'sacks': 3.2, 'interceptions': 1.1, 'fumble_recoveries': 0.8,
            'safeties': 0.1, 'tds': 0.3, 'points_allowed': 19
        }

        blended['Browns DST'] = {
            'sacks': 2.1, 'interceptions': 0.7, 'fumble_recoveries': 0.5,
            'safeties': 0.05, 'tds': 0.15, 'points_allowed': 28
        }

        # Add kickers (estimate from team points)
        blended['Justin Tucker'] = {
            'fg_attempts': 2.5, 'fg_made': 2.2, 'xp_attempts': 3.5, 'xp_made': 3.4
        }

        blended['Dustin Hopkins'] = {
            'fg_attempts': 2.0, 'fg_made': 1.7, 'xp_attempts': 2.2, 'xp_made': 2.1
        }

        return blended

    def calculate_dk_points(self, player_stats: Dict) -> Dict:
        """Calculate DraftKings fantasy points for each player"""
        print("\n🧮 Calculating DraftKings points...")

        dk_projections = {}

        for player, stats in player_stats.items():
            points = 0

            # Passing stats
            if 'pass_yards' in stats:
                points += stats['pass_yards'] * self.dk_scoring['pass_yard']
                points += stats['pass_tds'] * self.dk_scoring['pass_td']
                points += stats.get('interceptions', 0) * self.dk_scoring['pass_int']

            # Rushing stats
            if 'rush_yards' in stats:
                points += stats['rush_yards'] * self.dk_scoring['rush_yard']
                points += stats.get('rush_tds', 0) * self.dk_scoring['rush_td']

            # Receiving stats
            if 'rec_yards' in stats:
                points += stats['rec_yards'] * self.dk_scoring['rec_yard']
                points += stats.get('receptions', 0) * self.dk_scoring['reception']
                points += stats.get('rec_tds', 0) * self.dk_scoring['rec_td']

            # Kicker stats
            if 'fg_made' in stats:
                points += stats['fg_made'] * self.dk_scoring['fg_made']
                points += stats.get('xp_made', 0) * self.dk_scoring['xp_made']

            # Defense/Special Teams stats
            if 'sacks' in stats:
                points += stats['sacks'] * self.dk_scoring['dst_sack']
                points += stats.get('interceptions', 0) * self.dk_scoring['dst_int']
                points += stats.get('fumble_recoveries', 0) * self.dk_scoring['dst_fumble_rec']
                points += stats.get('safeties', 0) * self.dk_scoring['dst_safety']
                points += stats.get('tds', 0) * self.dk_scoring['dst_td']

                # Points allowed scoring
                pts_allowed = stats.get('points_allowed', 21)
                if pts_allowed == 0:
                    points += self.dk_scoring['dst_pts_allowed_0']
                elif pts_allowed <= 6:
                    points += self.dk_scoring['dst_pts_allowed_1_6']
                elif pts_allowed <= 13:
                    points += self.dk_scoring['dst_pts_allowed_7_13']
                elif pts_allowed <= 20:
                    points += self.dk_scoring['dst_pts_allowed_14_20']
                elif pts_allowed <= 27:
                    points += self.dk_scoring['dst_pts_allowed_21_27']
                elif pts_allowed <= 34:
                    points += self.dk_scoring['dst_pts_allowed_28_34']
                else:
                    points += self.dk_scoring['dst_pts_allowed_35_plus']

            # Calculate confidence score
            confidence = 0.5  # Base confidence

            # Adjust for market strength (from mock data)
            confidence += 0.2 * 0.8  # market_strength = 0.8
            confidence -= 0.2 * 0.2  # market_uncertainty = 0.2

            # Adjust for player role (starters get higher confidence)
            if player in ['Lamar Jackson', 'Derrick Henry', 'Joe Flacco', 'Jerry Jeudy', 'Ravens DST', 'Browns DST']:
                confidence += 0.15

            confidence = max(0.1, min(0.95, confidence))  # Cap between 0.1 and 0.95

            dk_projections[player] = {
                'projection': round(points, 2),
                'confidence': round(confidence, 2)
            }

        return dk_projections

    def generate_final_projections(self) -> pd.DataFrame:
        """Generate final ranked projections"""
        print("\n🏈 ELITE DRAFTKINGS PROJECTIONS - BROWNS @ RAVENS")
        print("=" * 60)

        # Run full pipeline
        edges = self.compute_matchup_edges()
        market_data = self.get_mock_market_data()
        team_totals = self.model_team_totals(edges, market_data)
        blended_stats = self.blend_with_props(team_totals, market_data)
        dk_projections = self.calculate_dk_points(blended_stats)

        # Create DataFrame
        results = []
        for player, proj_data in dk_projections.items():
            results.append({
                'Player': player,
                'Projection': proj_data['projection'],
                'Confidence': proj_data['confidence']
            })

        df_results = pd.DataFrame(results)
        df_results = df_results.sort_values('Projection', ascending=False).reset_index(drop=True)

        # Display results
        print("\n📊 TOP PROJECTIONS:")
        print("-" * 40)
        for i, row in df_results.head(15).iterrows():
            print(f"{i+1:2d}. {row['Player']:15s} {row['Projection']:6.2f} pts (conf: {row['Confidence']:.2f})")

        return df_results

    def save_projections_csv(self, df_projections: pd.DataFrame):
        """Save projections to CSV"""
        output_df = df_projections[['Player', 'Projection']].copy()
        output_df.to_csv('browns_ravens_dk_projections.csv', index=False)
        print(f"\n💾 Saved projections to browns_ravens_dk_projections.csv")

if __name__ == "__main__":
    projector = EliteDKProjections()

    # Generate complete projections
    final_projections = projector.generate_final_projections()

    # Save to CSV
    projector.save_projections_csv(final_projections)
