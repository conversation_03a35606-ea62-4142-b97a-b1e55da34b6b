#!/usr/bin/env python3
"""
Improved Prop Reading Engine
Focus on reading market signals better, not arbitrary adjustments
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any


class ImprovedPropReader:
    """Enhanced prop reading engine that better interprets market signals."""
    
    def __init__(self):
        # Market signal thresholds
        self.thresholds = {
            'high_std': 2.0,           # High line disagreement
            'wide_range': 4.0,         # Wide line range
            'strong_sharp_signal': 2.0, # Strong sharp vs public difference
            'min_books': 4,            # Minimum books for confidence
            'correlation_threshold': 0.7 # Minimum correlation for consistency
        }
        
        # Confidence adjustments based on market signals
        self.confidence_adjustments = {
            'high_std': 0.80,          # 20% reduction for high disagreement
            'wide_range': 0.75,        # 25% reduction for wide range
            'low_book_count': 0.85,    # 15% reduction for thin markets
            'strong_sharp_signal': 1.2  # 20% increase for strong sharp signal
        }
    
    def analyze_market_uncertainty(self, prop_data: pd.DataFrame) -> Dict[str, Any]:
        """Analyze market uncertainty signals from prop data."""
        
        uncertainty_signals = {}
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            signals = {
                'line_std': row['line_std'],
                'line_range': row['line_range_max'] - row['line_range_min'],
                'book_count': row['book_count'],
                'sharp_public_diff': row['sharp_public_diff'],
                'confidence_score': row['confidence_score']
            }
            
            # Calculate uncertainty score
            uncertainty_score = 0
            
            # High standard deviation = uncertainty
            if signals['line_std'] > self.thresholds['high_std']:
                uncertainty_score += 0.3
            
            # Wide range = uncertainty  
            if signals['line_range'] > self.thresholds['wide_range']:
                uncertainty_score += 0.3
            
            # Low book count = uncertainty
            if signals['book_count'] < self.thresholds['min_books']:
                uncertainty_score += 0.2
            
            # Strong sharp signal = more certainty (negative uncertainty)
            if abs(signals['sharp_public_diff']) > self.thresholds['strong_sharp_signal']:
                uncertainty_score -= 0.2
            
            signals['uncertainty_score'] = max(0, min(1, uncertainty_score))
            uncertainty_signals[key] = signals
        
        return uncertainty_signals
    
    def detect_sharp_money_signals(self, prop_data: pd.DataFrame) -> Dict[str, Dict]:
        """Detect and interpret sharp money signals."""
        
        sharp_signals = {}
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            sharp_line = row['sharp_line']
            public_line = row['public_line']
            diff = sharp_line - public_line
            
            if abs(diff) >= self.thresholds['strong_sharp_signal']:
                sharp_signals[key] = {
                    'sharp_line': sharp_line,
                    'public_line': public_line,
                    'difference': diff,
                    'direction': 'OVER' if diff > 0 else 'UNDER',
                    'strength': min(abs(diff) / 5.0, 1.0),  # Scale 0-1
                    'confidence': 'HIGH' if abs(diff) > 3 else 'MEDIUM'
                }
        
        return sharp_signals
    
    def check_prop_correlations(self, prop_data: pd.DataFrame, projections: Dict) -> Dict[str, List]:
        """Check for correlation inconsistencies in our projections."""
        
        inconsistencies = []
        
        # QB Pass Yards vs WR Receiving Yards correlation
        qb_pass_props = prop_data[prop_data['market'] == 'player_pass_yds']
        wr_rec_props = prop_data[prop_data['market'] == 'player_reception_yds']
        
        for _, qb_row in qb_pass_props.iterrows():
            qb_name = qb_row['player_name']
            qb_projection = projections.get(qb_name, 0)
            qb_line = qb_row['line_consensus']
            
            # Find WRs on same team
            team = self.get_player_team(qb_name)  # Would need team mapping
            team_wrs = wr_rec_props[wr_rec_props['player_name'].str.contains('|'.join(['McLaurin', 'Samuel', 'Doubs', 'Reed']), case=False, na=False)]
            
            for _, wr_row in team_wrs.iterrows():
                wr_name = wr_row['player_name']
                wr_projection = projections.get(wr_name, 0)
                wr_line = wr_row['line_consensus']
                
                # Check if projections are consistent
                qb_over_line = (qb_projection - qb_line) / qb_line
                wr_over_line = (wr_projection - wr_line) / wr_line
                
                # If QB is way over but WR is under, that's inconsistent
                if qb_over_line > 0.2 and wr_over_line < -0.1:
                    inconsistencies.append({
                        'type': 'QB_WR_MISMATCH',
                        'qb': qb_name,
                        'wr': wr_name,
                        'qb_over_line': qb_over_line,
                        'wr_over_line': wr_over_line,
                        'issue': f"QB projected {qb_over_line:.1%} over line but WR {wr_over_line:.1%} under line"
                    })
        
        return {'inconsistencies': inconsistencies}
    
    def get_player_team(self, player_name: str) -> str:
        """Get player team (simplified mapping)."""
        was_players = ['Jayden Daniels', 'Terry McLaurin', 'Austin Ekeler', 'Deebo Samuel']
        gb_players = ['Jordan Love', 'Josh Jacobs', 'Romeo Doubs', 'Tucker Kraft', 'Jayden Reed']
        
        if any(name in player_name for name in was_players):
            return 'WAS'
        elif any(name in player_name for name in gb_players):
            return 'GB'
        return 'UNKNOWN'
    
    def calculate_market_adjusted_confidence(self, 
                                           prop_data: pd.DataFrame,
                                           uncertainty_signals: Dict,
                                           sharp_signals: Dict) -> Dict[str, float]:
        """Calculate confidence adjusted for market signals."""
        
        adjusted_confidence = {}
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            
            base_confidence = row['confidence_score']
            
            # Start with base confidence
            final_confidence = base_confidence
            
            # Apply uncertainty adjustments
            if key in uncertainty_signals:
                uncertainty = uncertainty_signals[key]
                
                # Reduce confidence for high uncertainty
                if uncertainty['line_std'] > self.thresholds['high_std']:
                    final_confidence *= self.confidence_adjustments['high_std']
                
                if uncertainty['line_range'] > self.thresholds['wide_range']:
                    final_confidence *= self.confidence_adjustments['wide_range']
                
                if uncertainty['book_count'] < self.thresholds['min_books']:
                    final_confidence *= self.confidence_adjustments['low_book_count']
            
            # Apply sharp money adjustments
            if key in sharp_signals:
                sharp_signal = sharp_signals[key]
                if sharp_signal['confidence'] == 'HIGH':
                    final_confidence *= self.confidence_adjustments['strong_sharp_signal']
            
            # Cap confidence between 0.1 and 1.0
            final_confidence = max(0.1, min(1.0, final_confidence))
            
            adjusted_confidence[key] = {
                'original_confidence': base_confidence,
                'adjusted_confidence': final_confidence,
                'adjustment_factor': final_confidence / base_confidence
            }
        
        return adjusted_confidence
    
    def generate_market_aware_recommendations(self, 
                                            prop_data: pd.DataFrame,
                                            projections: Dict) -> List[Dict]:
        """Generate recommendations that properly read market signals."""
        
        # Analyze market signals
        uncertainty_signals = self.analyze_market_uncertainty(prop_data)
        sharp_signals = self.detect_sharp_money_signals(prop_data)
        correlation_check = self.check_prop_correlations(prop_data, projections)
        adjusted_confidence = self.calculate_market_adjusted_confidence(
            prop_data, uncertainty_signals, sharp_signals
        )
        
        recommendations = []
        
        for _, row in prop_data.iterrows():
            player = row['player_name']
            market = row['market']
            key = f"{player}_{market}"
            line = row['line_consensus']
            
            # Get our projection
            projection = projections.get(player, line)
            
            # Calculate edge
            edge = abs(projection - line) / line
            
            # Get adjusted confidence
            confidence_data = adjusted_confidence.get(key, {})
            confidence = confidence_data.get('adjusted_confidence', 0.5)
            
            # Only recommend if edge > 8% and confidence > 0.6
            if edge > 0.08 and confidence > 0.6:
                
                recommendation = {
                    'player': player,
                    'market': market,
                    'line': line,
                    'projection': projection,
                    'edge': edge,
                    'original_confidence': confidence_data.get('original_confidence', 0.5),
                    'adjusted_confidence': confidence,
                    'recommendation': 'OVER' if projection > line else 'UNDER'
                }
                
                # Add market signal context
                if key in uncertainty_signals:
                    recommendation['uncertainty_score'] = uncertainty_signals[key]['uncertainty_score']
                    recommendation['line_std'] = uncertainty_signals[key]['line_std']
                    recommendation['book_count'] = uncertainty_signals[key]['book_count']
                
                if key in sharp_signals:
                    recommendation['sharp_signal'] = sharp_signals[key]['direction']
                    recommendation['sharp_strength'] = sharp_signals[key]['strength']
                
                recommendations.append(recommendation)
        
        # Sort by adjusted confidence * edge
        recommendations.sort(key=lambda x: x['adjusted_confidence'] * x['edge'], reverse=True)
        
        return recommendations
    
    def print_market_analysis(self, prop_data: pd.DataFrame):
        """Print detailed market analysis."""
        
        print("🔍 MARKET SIGNAL ANALYSIS")
        print("=" * 40)
        
        uncertainty_signals = self.analyze_market_uncertainty(prop_data)
        sharp_signals = self.detect_sharp_money_signals(prop_data)
        
        print(f"\n📊 HIGH UNCERTAINTY PROPS:")
        high_uncertainty = {k: v for k, v in uncertainty_signals.items() 
                          if v['uncertainty_score'] > 0.5}
        
        for key, signals in high_uncertainty.items():
            print(f"  • {key}: Uncertainty = {signals['uncertainty_score']:.2f}")
            print(f"    Line STD: {signals['line_std']:.2f}, Range: {signals['line_range']:.1f}, Books: {signals['book_count']}")
        
        print(f"\n💰 STRONG SHARP MONEY SIGNALS:")
        for key, signal in sharp_signals.items():
            print(f"  • {key}: {signal['direction']} (Sharp: {signal['sharp_line']:.1f} vs Public: {signal['public_line']:.1f})")
            print(f"    Strength: {signal['strength']:.2f}, Confidence: {signal['confidence']}")


def main():
    """Example usage of improved prop reader."""
    
    try:
        prop_data = pd.read_csv('csvs/was_gb_detailed_prop_analysis_20250911_2159.csv')
        
        reader = ImprovedPropReader()
        reader.print_market_analysis(prop_data)
        
        print(f"\n✅ Improved prop reading engine ready!")
        print(f"   Focus: Market signals, not arbitrary adjustments")
        
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
