#!/usr/bin/env python3
"""
The Odds API Credit Checker
Check remaining API credits and usage information
"""

import requests
import json
from datetime import datetime


class OddsAPIChecker:
    """Check The Odds API credit usage and remaining balance."""
    
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.the-odds-api.com/v4"
    
    def check_credits(self) -> dict:
        """Check remaining API credits using the /sports endpoint (free call)."""

        try:
            # The Odds API sports endpoint - does NOT count against quota
            url = f"{self.base_url}/sports"
            params = {
                'apiKey': self.api_key  # Note: parameter name is 'apiKey' not 'api_key'
            }

            # Make a minimal request to check credits
            response = requests.get(url, params=params)

            if response.status_code == 200:
                # Extract credit information from headers
                credits_info = {
                    'status': 'success',
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                    'remaining_requests': response.headers.get('x-requests-remaining'),
                    'used_requests': response.headers.get('x-requests-used'),
                    'last_request_cost': response.headers.get('x-requests-last'),
                    'response_status': response.status_code,
                    'api_response_size': len(response.json()) if response.content else 0
                }

                return credits_info
                
            else:
                return {
                    'status': 'error',
                    'error_code': response.status_code,
                    'error_message': response.text,
                    'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                }
                
        except Exception as e:
            return {
                'status': 'error',
                'error_message': str(e),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def get_detailed_usage(self) -> dict:
        """Get detailed usage information with cost breakdown."""
        
        credits_info = self.check_credits()
        
        if credits_info['status'] == 'success':
            remaining = int(credits_info.get('remaining_requests', 0)) if credits_info.get('remaining_requests') else 0
            used = int(credits_info.get('used_requests', 0)) if credits_info.get('used_requests') else 0

            # Calculate total from remaining + used (no total header provided)
            total = remaining + used if remaining > 0 or used > 0 else 0

            # Calculate usage statistics
            usage_percentage = (used / total * 100) if total > 0 else 0
            
            detailed_info = {
                **credits_info,
                'usage_stats': {
                    'usage_percentage': round(usage_percentage, 1),
                    'credits_left': remaining,
                    'credits_used': used,
                    'total_credits': total
                },
                'cost_estimates': {
                    'cost_per_request': 0.002,  # $0.002 per request for most endpoints
                    'estimated_spent': round(used * 0.002, 2),
                    'estimated_remaining_value': round(remaining * 0.002, 2)
                }
            }
            
            return detailed_info
        
        return credits_info
    
    def print_credit_status(self):
        """Print formatted credit status."""
        
        print("🔍 CHECKING THE ODDS API CREDITS...")
        print("=" * 45)
        
        info = self.get_detailed_usage()
        
        if info['status'] == 'success':
            print(f"✅ API Status: Active")
            print(f"📅 Checked: {info['timestamp']}")
            print()
            
            usage = info['usage_stats']
            print(f"📊 CREDIT USAGE:")
            print(f"   • Credits Used: {usage['credits_used']:,}")
            print(f"   • Credits Remaining: {usage['credits_left']:,}")
            print(f"   • Total Credits: {usage['total_credits']:,}")
            print(f"   • Usage: {usage['usage_percentage']:.1f}%")
            print()
            
            costs = info['cost_estimates']
            print(f"💰 COST BREAKDOWN:")
            print(f"   • Cost per request: ${costs['cost_per_request']:.3f}")
            print(f"   • Estimated spent: ${costs['estimated_spent']:.2f}")
            print(f"   • Remaining value: ${costs['estimated_remaining_value']:.2f}")
            print()
            
            # Usage warnings
            if usage['usage_percentage'] > 90:
                print("⚠️  WARNING: Over 90% of credits used!")
            elif usage['usage_percentage'] > 75:
                print("⚠️  CAUTION: Over 75% of credits used")
            elif usage['usage_percentage'] > 50:
                print("ℹ️  INFO: Over 50% of credits used")
            else:
                print("✅ GOOD: Plenty of credits remaining")
            
            # Reset time info
            if info.get('reset_time'):
                print(f"🔄 Credits reset: {info['reset_time']}")
        
        else:
            print(f"❌ ERROR: {info.get('error_message', 'Unknown error')}")
            if 'error_code' in info:
                print(f"   Status Code: {info['error_code']}")
    
    def estimate_projection_costs(self, num_games: int = 1) -> dict:
        """Estimate costs for running projections on games."""
        
        # Typical API calls for one game projection:
        # 1. Get sports/events (1 request)
        # 2. Get game odds (1 request) 
        # 3. Get player props - multiple markets (5-10 requests)
        # Total: ~7-12 requests per game
        
        requests_per_game = 10  # Conservative estimate
        total_requests = num_games * requests_per_game
        estimated_cost = total_requests * 0.002
        
        current_info = self.get_detailed_usage()
        
        if current_info['status'] == 'success':
            remaining = current_info['usage_stats']['credits_left']
            can_afford = remaining >= total_requests
            
            return {
                'num_games': num_games,
                'requests_per_game': requests_per_game,
                'total_requests_needed': total_requests,
                'estimated_cost': estimated_cost,
                'current_remaining': remaining,
                'can_afford': can_afford,
                'games_possible': remaining // requests_per_game
            }
        
        return {'error': 'Could not get current credit info'}


def main():
    """Example usage of credit checker."""
    
    print("🔑 THE ODDS API CREDIT CHECKER")
    print("=" * 35)
    
    # You would put your actual API key here
    api_key = input("Enter your Odds API key (or press Enter to skip): ").strip()
    
    if not api_key:
        print("\n📝 USAGE INSTRUCTIONS:")
        print("=" * 25)
        print("1. Get your API key from: https://the-odds-api.com/")
        print("2. Run this script with your key:")
        print("   checker = OddsAPIChecker('your_api_key_here')")
        print("   checker.print_credit_status()")
        print("\n3. Or integrate into the main system:")
        print("   system = IntegratedNFLSystem(odds_api_key='your_key')")
        print("   checker = OddsAPIChecker('your_key')")
        print("   checker.print_credit_status()")
        return
    
    # Check credits
    checker = OddsAPIChecker(api_key)
    checker.print_credit_status()
    
    # Estimate projection costs
    print(f"\n💡 PROJECTION COST ESTIMATES:")
    print("-" * 30)
    
    for games in [1, 5, 10]:
        estimate = checker.estimate_projection_costs(games)
        if 'error' not in estimate:
            print(f"   {games} game{'s' if games > 1 else ''}: "
                  f"{estimate['total_requests_needed']} requests, "
                  f"${estimate['estimated_cost']:.2f}")
    
    # Show max games possible
    estimate = checker.estimate_projection_costs(1)
    if 'error' not in estimate:
        max_games = estimate['games_possible']
        print(f"\n🎯 With current credits: ~{max_games} games possible")


if __name__ == "__main__":
    main()
