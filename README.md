# NFL Projections

A Python package for NFL game projections, odds analysis, and simulation.

## Features

- **CSV Data Loading**: Auto-detect delimiters, normalize headers, and clean data
- **Odds Integration**: Fetch live NFL odds from The Odds API
- **Projection Models**: Build and train models for game predictions
- **Game Simulation**: Monte Carlo simulation for game outcomes
- **Command Line Interface**: Easy-to-use CLI for common tasks

## Quick Start

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd nfl-projections
```

2. Install dependencies:
```bash
pip install -r requirements.txt
```

3. Set up environment variables:
```bash
cp .env.example .env
# Edit .env and add your ODDS_API_KEY from https://the-odds-api.com/
```

### Usage

#### Load and analyze CSV data:
```bash
python -m src.proj.cli load --csv path/to/your/data.csv
```

#### Fetch NFL odds:
```bash
python -m src.proj.cli odds --out data/odds_week1.json
```

### Python API

```python
from src.proj.io_csv import load_csv
from src.proj.fetch_odds import get_totals_spreads, implied_team_totals
from src.proj.simulate import simulate_game

# Load CSV data
df, schema = load_csv('data/player_stats.csv')

# Fetch odds
import os
from dotenv import load_dotenv
load_dotenv()

odds_data = get_totals_spreads(os.getenv('ODDS_API_KEY'))
totals_df = implied_team_totals(odds_data)

# Simulate a game
results = simulate_game(home_total=24.5, away_total=21.0)
print(f"Home win probability: {results['home_win_prob']:.2%}")
```

## Project Structure

```
nfl_projections/
├── src/proj/
│   ├── __init__.py
│   ├── cli.py           # Command line interface
│   ├── io_csv.py        # CSV loading and normalization
│   ├── fetch_odds.py    # Odds API integration
│   ├── model.py         # Projection models
│   └── simulate.py      # Game simulation
├── tests/
│   ├── test_io_csv.py
│   └── test_fetch_odds.py
├── requirements.txt
├── .env.example
├── .gitignore
└── README.md
```

## Testing

Run tests with pytest:
```bash
pytest tests/
```

## API Key Setup

Get a free API key from [The Odds API](https://the-odds-api.com/) and add it to your `.env` file:
```
ODDS_API_KEY=your_api_key_here
```

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Run tests to ensure everything works
6. Submit a pull request