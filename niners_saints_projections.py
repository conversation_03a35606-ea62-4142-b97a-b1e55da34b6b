#!/usr/bin/env python3
"""
Elite NFL Projection System: San Francisco 49ers vs New Orleans Saints
- Fetches 20+ player prop markets from The Odds API
- Applies elite prop reading (market strength, sharp vs public, implied probs)
- Processes DK scoring (pass/rush/rec yards, receptions, TDs, INTs)
- Applies team context edges from holes/levers model
- Adds anytime TD for skill players
- Builds D/ST projections from implied totals + edges (+ sacks from markets when available)
- Outputs Player,Projection CSV sorted descending
"""

from __future__ import annotations
import os
import requests
import pandas as pd
from dataclasses import dataclass
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

# ----------------------------- Data Models -----------------------------

@dataclass
class GameContext:
    home_team: str
    away_team: str
    total: float
    spread: float  # positive => home favored
    home_implied: float
    away_implied: float

# ----------------------- Elite Projection System -----------------------

class EliteProjectionSystem:
    def __init__(self, api_key: Optional[str] = None):
        self.api_key = api_key or os.getenv('ODDS_API_KEY')
        if not self.api_key:
            raise RuntimeError("ODDS_API_KEY missing in environment")
        self.dk = {
            'pass_yard': 0.04, 'pass_td': 4, 'pass_int': -1,
            'rush_yard': 0.1, 'rush_td': 6,
            'rec_yard': 0.1, 'reception': 1, 'rec_td': 6,
            'dst_sack': 1, 'dst_int': 2, 'dst_fumble_rec': 2, 'dst_td': 6,
            'dst_pts_0': 10, 'dst_pts_1_6': 7, 'dst_pts_7_13': 4,
            'dst_pts_14_20': 1, 'dst_pts_21_27': 0, 'dst_pts_28_34': -1
        }
        self.edges = self._get_team_edges()

    # --------------------------- Odds Fetching --------------------------

    def _find_game_and_context(self) -> Tuple[Dict[str, Any], GameContext]:
        url = "https://api.the-odds-api.com/v4/sports/americanfootball_nfl/odds"
        params = {'api_key': self.api_key, 'regions': 'us', 'markets': 'spreads,totals', 'oddsFormat': 'american'}
        r = requests.get(url, params=params, timeout=30)
        r.raise_for_status()
        games = r.json()

        target = None
        for g in games:
            teams = (g.get('home_team',''), g.get('away_team',''))
            if (('49ers' in teams[0] or 'San Francisco' in teams[0]) and ('Saints' in teams[1] or 'New Orleans' in teams[1])) or \
               (('49ers' in teams[1] or 'San Francisco' in teams[1]) and ('Saints' in teams[0] or 'New Orleans' in teams[0])):
                target = g
                break
        if not target:
            raise RuntimeError('49ers vs Saints game not found in odds feed')

        # Extract spread/total from first bookmaker
        spread = None; total = None
        if target.get('bookmakers'):
            bm = target['bookmakers'][0]
            for m in bm.get('markets', []):
                if m.get('key') == 'spreads':
                    for o in m.get('outcomes', []):
                        if o.get('name') == target['home_team']:
                            spread = float(o.get('point'))
                if m.get('key') == 'totals':
                    for o in m.get('outcomes', []):
                        if o.get('name') == 'Over':
                            total = float(o.get('point'))
        if spread is None or total is None:
            raise RuntimeError('Failed to read spread/total for game')

        home_implied = total/2 + spread/2
        away_implied = total/2 - spread/2
        gc = GameContext(
            home_team=target['home_team'],
            away_team=target['away_team'],
            total=total,
            spread=spread,
            home_implied=home_implied,
            away_implied=away_implied,
        )
        return target, gc

    def _fetch_props_for_event(self, event_id: str) -> Dict[str, Any]:
        markets = [
            'player_pass_yds','player_pass_tds','player_pass_completions','player_pass_attempts',
            'player_pass_interceptions','player_rush_yds','player_rush_tds','player_rush_attempts',
            'player_receptions','player_reception_yds','player_reception_tds',
            'player_rush_reception_yds','player_anytime_td','player_1st_td',
            'player_tackles_assists','player_sacks','player_defensive_interceptions',
            'player_kicking_points','player_longest_reception','player_longest_rush','player_longest_pass',
            'player_pass_yds_alternate','player_rush_yds_alternate','player_reception_yds_alternate','player_receptions_alternate','player_pass_tds_alternate','player_rush_tds_alternate'
        ]
        base_url = f"https://api.the-odds-api.com/v4/sports/americanfootball_nfl/events/{event_id}/odds"
        combined: Dict[str, Any] = {'bookmakers': []}
        by_book: Dict[str, Dict[str, Any]] = {}

        # chunk requests to avoid 422 for overly large market lists
        chunk_size = 6
        for i in range(0, len(markets), chunk_size):
            chunk = markets[i:i+chunk_size]
            params = {'api_key': self.api_key, 'regions': 'us', 'markets': ','.join(chunk), 'oddsFormat': 'american'}
            try:
                r = requests.get(base_url, params=params, timeout=30)
                r.raise_for_status()
                data = r.json()
            except requests.HTTPError as e:
                # skip chunk if provider rejects
                continue

            for b in data.get('bookmakers', []):
                key = b.get('key','').lower()
                if not key:
                    continue
                entry = by_book.setdefault(key, {'key': key, 'title': b.get('title', key), 'markets': []})
                # merge markets; avoid duplicates by key+point
                seen_pairs = {(m.get('key'), str(m.get('outcomes'))) for m in entry['markets']}
                for m in b.get('markets', []):
                    pair = (m.get('key'), str(m.get('outcomes')))
                    if pair not in seen_pairs:
                        entry['markets'].append(m)
                        seen_pairs.add(pair)

        combined['bookmakers'] = list(by_book.values())
        return combined

    # ----------------------- Market Processing -------------------------

    def _process_sharp_props(self, props_data: Dict[str, Any]) -> Dict[str, Dict[str, Any]]:
        sharp_books = ['pinnacle','circa','fanduel','draftkings','betmgm','caesars']
        book_by_key = {b['key'].lower(): b for b in props_data.get('bookmakers', []) if b.get('key')}
        ordered_books = [book_by_key[k] for k in sharp_books if k in book_by_key]

        player_props: Dict[str, Dict[str, Any]] = {}
        for book in ordered_books:
            for market in book.get('markets', []):
                mkey = market.get('key')
                for outcome in market.get('outcomes', []):
                    player = (outcome.get('description') or '').replace(' (','').replace(')','')
                    if not player:
                        continue
                    player_props.setdefault(player, {})
                    # keep first (sharpest) book we see for each market
                    if mkey not in player_props[player]:
                        player_props[player][mkey] = {
                            'line': outcome.get('point'),
                            'over_odds': outcome.get('price') if outcome.get('name')=='Over' else None,
                            'under_odds': outcome.get('price') if outcome.get('name')=='Under' else None,
                            'book': book['key'].lower(),
                            'market_strength': self._market_strength(outcome)
                        }
        return player_props

    def _market_strength(self, outcome: Dict[str, Any]) -> float:
        price = abs(outcome.get('price', -110))
        if price <= 105: return 0.95
        if price <= 115: return 0.85
        if price <= 125: return 0.70
        return 0.50

    def _market_overview(self, props_data: Dict[str, Any]) -> Dict[str, Any]:
        books = [b.get('key','').lower() for b in props_data.get('bookmakers', [])]
        return {
            'overall_strength': 0.85 if 'pinnacle' in books else 0.70,
            'sharp_book_count': sum(1 for b in books if b in ('pinnacle','circa')),
            'market_confidence': 0.80
        }

    # ---------------------- Edges and Context --------------------------

    def _get_team_edges(self) -> Dict[str, float]:
        # Use models/holes_and_levers.parquet when available
        try:
            df = pd.read_parquet('models/holes_and_levers.parquet')
            niners_row = df[df['team'].str.contains('49ers|SF', case=False, na=False)].iloc[0]
            saints_row = df[df['team'].str.contains('Saints|NO', case=False, na=False)].iloc[0]
            niners_pass_edge = niners_row.get('lever_explosive_pass', 0) - saints_row.get('hole_pass_eff', 0)
            niners_rz_edge   = niners_row.get('lever_rz', 0) - saints_row.get('hole_rz', 0)
            saints_rush_edge = saints_row.get('lever_ppd', 0) - niners_row.get('hole_rush_eff', 0)
            saints_protect   = saints_row.get('lever_protection', 0) - niners_row.get('hole_pressure', 0)
            return {
                'niners_pass_edge': float(niners_pass_edge),
                'niners_rz_edge': float(niners_rz_edge),
                'saints_rush_edge': float(saints_rush_edge),
                'saints_protection_edge': float(saints_protect),
                'home_advantage': 0.15
            }
        except Exception as e:
            # Fallback to modest neutral edges derived conservatively
            return {'niners_pass_edge': 0.5, 'niners_rz_edge': 0.5, 'saints_rush_edge': 0.3, 'saints_protection_edge': 0.2, 'home_advantage': 0.1}

    # --------------------- Projection Calculations ---------------------

    def _elite_prop_read(self, player: str, props: Dict[str, Any]) -> Dict[str, float]:
        analysis = {'market_strength_factor': 1.0, 'sharp_money_factor': 1.0, 'psychological_factor': 1.0, 'line_value_factor': 1.0, 'confidence': 0.5}
        if not props: return analysis
        # Pinnacle/Circa premium
        if any(v.get('book')=='pinnacle' for v in props.values()):
            analysis['market_strength_factor'] = 1.15; analysis['confidence'] += 0.2
        elif any(v.get('book')=='circa' for v in props.values()):
            analysis['market_strength_factor'] = 1.10; analysis['confidence'] += 0.15
        # Implied prob efficiency
        for v in props.values():
            over, under = v.get('over_odds', -110), v.get('under_odds', -110)
            over_p = self._american_to_prob(over); under_p = self._american_to_prob(under)
            s = over_p + under_p
            if s < 1.06: analysis['sharp_money_factor'] *= 1.12; analysis['confidence'] += 0.1
            elif s > 1.12: analysis['line_value_factor'] *= 1.08
        # Psychological bias
        stars = {'Brock Purdy','Christian McCaffrey','Deebo Samuel','Brandon Aiyuk','George Kittle','Derek Carr','Alvin Kamara','Chris Olave'}
        if player in stars:
            analysis['psychological_factor'] = 0.96; analysis['confidence'] += 0.05
        return analysis

    def _american_to_prob(self, odds: int) -> float:
        return 100/(odds+100) if odds>0 else abs(odds)/(abs(odds)+100)

    def _anytime_td_prob(self, td_prop: Dict[str, Any]) -> float:
        o = td_prop.get('over_odds', +200)
        p = 100/(o+100) if o>0 else abs(o)/(abs(o)+100)
        if td_prop.get('book')=='pinnacle': return p*1.05
        if td_prop.get('book')=='circa': return p*1.03
        return p

    def _apply_context_multiplier(self, player: str, pos: str, team: str, home_team: str) -> float:
        m = 1.0
        home_bonus = self.edges.get('home_advantage', 0.1) if team == home_team else 0.0
        if team in ('SF','49ers','San Francisco 49ers'):
            if pos=='QB': m *= (1 + 0.08*self.edges['niners_pass_edge'])
            if pos in ('WR','TE'): m *= (1 + 0.12*self.edges['niners_pass_edge']); m *= (1 + 0.06*self.edges['niners_rz_edge'])
            if pos=='RB': m *= (1 + 0.10*self.edges['niners_rz_edge'])
        else:  # Saints
            if pos=='QB': m *= (1 + 0.06*self.edges['saints_protection_edge'])
            if pos=='RB': m *= (1 + 0.12*self.edges['saints_rush_edge'])
            if pos in ('WR','TE'): m *= (1 + 0.04*self.edges['saints_protection_edge'])
        return m * (1 + home_bonus)

    def _calc_from_props(self, player: str, pos: str, props: Dict[str, Any], read: Dict[str, float]) -> float:
        proj = 0.0
        f = read['market_strength_factor']*read['sharp_money_factor']*read['psychological_factor']*read['line_value_factor']
        if pos=='QB':
            if 'player_pass_yds' in props: proj += props['player_pass_yds']['line']*f*self.dk['pass_yard']
            if 'player_pass_tds' in props: proj += props['player_pass_tds']['line']*f*self.dk['pass_td']
            proj += 1.0*self.dk['pass_int']
            if 'player_rush_yds' in props: proj += props['player_rush_yds']['line']*f*self.dk['rush_yard']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rush_td']
        elif pos in ('WR','TE'):
            if 'player_reception_yds' in props: proj += props['player_reception_yds']['line']*f*self.dk['rec_yard']
            if 'player_receptions' in props: proj += props['player_receptions']['line']*f*self.dk['reception']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rec_td']
        elif pos=='RB':
            if 'player_rush_yds' in props: proj += props['player_rush_yds']['line']*f*self.dk['rush_yard']
            if 'player_reception_yds' in props: proj += props['player_reception_yds']['line']*f*0.8*self.dk['rec_yard']
            # modest reception estimate for pass-catching RBs when receptions market absent
            if 'player_receptions' in props:
                proj += props['player_receptions']['line']*f*self.dk['reception']
            else:
                if player in {'Christian McCaffrey','Alvin Kamara'}: proj += 3.5*self.dk['reception']
                else: proj += 1.5*self.dk['reception']
            if 'player_anytime_td' in props: proj += self._anytime_td_prob(props['player_anytime_td'])*self.dk['rush_td']
        elif pos=='K':
            # Kicker approx from implied team total handled later via baseline
            pass
        return max(proj, 0.0)

    # ---------------------------- Roster -------------------------------

    def _dk_players(self) -> List[Dict[str,str]]:
        return [
            # 49ers
            {'name':'Brock Purdy','position':'QB','team':'SF'},
            {'name':'Christian McCaffrey','position':'RB','team':'SF'},
            {'name':'Elijah Mitchell','position':'RB','team':'SF'},
            {'name':'Jordan Mason','position':'RB','team':'SF'},
            {'name':'Deebo Samuel','position':'WR','team':'SF'},
            {'name':'Brandon Aiyuk','position':'WR','team':'SF'},
            {'name':'Jauan Jennings','position':'WR','team':'SF'},
            {'name':'George Kittle','position':'TE','team':'SF'},
            {'name':'Kyle Juszczyk','position':'TE','team':'SF'},
            {'name':'Jake Moody','position':'K','team':'SF'},
            # Saints
            {'name':'Derek Carr','position':'QB','team':'NO'},
            {'name':'Alvin Kamara','position':'RB','team':'NO'},
            {'name':'Kendre Miller','position':'RB','team':'NO'},
            {'name':'Jamaal Williams','position':'RB','team':'NO'},
            {'name':'Chris Olave','position':'WR','team':'NO'},
            {'name':'Rashid Shaheed','position':'WR','team':'NO'},
            {'name':'A.T. Perry','position':'WR','team':'NO'},
            {'name':'Juwan Johnson','position':'TE','team':'NO'},
            {'name':'Foster Moreau','position':'TE','team':'NO'},
            {'name':'Blake Grupe','position':'K','team':'NO'},
        ]

    def _baseline_for(self, player: str, pos: str) -> float:
        if pos=='QB': return 18.0 if player in {'Brock Purdy','Derek Carr'} else 8.0
        if pos=='WR':
            if player in {'Brandon Aiyuk','Chris Olave','Deebo Samuel'}: return 12.0
            if player in {'Rashid Shaheed','Jauan Jennings'}: return 8.5
            return 5.0
        if pos=='RB':
            if player in {'Christian McCaffrey','Alvin Kamara'}: return 13.0
            if player in {'Elijah Mitchell','Jamaal Williams','Kendre Miller'}: return 7.0
            return 4.0
        if pos=='TE':
            if player in {'George Kittle','Juwan Johnson'}: return 8.5
            return 5.0
        if pos=='K': return 8.0
        return 5.0

    # -------------------------- Defense/DST ----------------------------

    def _defense_projection(self, defense_team: str, gc: GameContext, player_props: Dict[str, Dict[str, Any]]) -> float:
        # Opponent implied points
        opp_pts = gc.away_implied if defense_team == gc.home_team else gc.home_implied
        # Points allowed DK bucket
        if opp_pts <= 6: pts_allowed = self.dk['dst_pts_0']
        elif opp_pts <= 13: pts_allowed = self.dk['dst_pts_1_6']
        elif opp_pts <= 20: pts_allowed = self.dk['dst_pts_7_13']
        elif opp_pts <= 27: pts_allowed = self.dk['dst_pts_14_20']
        elif opp_pts <= 34: pts_allowed = self.dk['dst_pts_21_27']
        else: pts_allowed = self.dk['dst_pts_28_34']

        # Base sacks/turnovers scaled by edges + available sack props
        # Sum any player_sacks markets for that defense (rough proxy)
        sack_sum = 0.0
        for name, p in player_props.items():
            if 'player_sacks' in p:
                # attribute to team by last known roster pattern (simple heuristic)
                if defense_team in ('San Francisco 49ers','49ers','SF') and any(t in name for t in ['Bosa','Hargrave','Armstead','Greenlaw','Warner']):
                    sack_sum += float(p['player_sacks'].get('line') or 0)
                if defense_team in ('New Orleans Saints','Saints','NO') and any(t in name for t in ['Jordan','Granderson','Breezy','Demario']):
                    sack_sum += float(p['player_sacks'].get('line') or 0)
        # Edges
        if defense_team in ('San Francisco 49ers','49ers','SF'):
            base_sacks = 2.4 + 0.6*self.edges['niners_pass_edge']
        else:
            base_sacks = 2.2 + 0.5*self.edges['saints_protection_edge']
        sacks = max(base_sacks, sack_sum)
        # Turnovers modestly tied to sacks
        ints = 0.8 if defense_team in ('San Francisco 49ers','49ers','SF') else 0.7
        fumbles = 0.5
        tds = 0.18 if defense_team in ('San Francisco 49ers','49ers','SF') else 0.15

        return sacks*self.dk['dst_sack'] + ints*self.dk['dst_int'] + fumbles*self.dk['dst_fumble_rec'] + tds*self.dk['dst_td'] + pts_allowed

    # ----------------------------- Orchestration -----------------------

    def run(self) -> List[Tuple[str,float]]:
        target, gc = self._find_game_and_context()
        props_raw = self._fetch_props_for_event(target['id'])
        player_props = self._process_sharp_props(props_raw)
        market_overview = self._market_overview(props_raw)

        players = self._dk_players()
        projections: Dict[str, float] = {}

        # Map team names to simple tags used in context multiplier
        home_tag = 'SF' if '49ers' in gc.home_team else 'NO'
        away_tag = 'SF' if '49ers' in gc.away_team else 'NO'

        for p in players:
            name, pos, team = p['name'], p['position'], p['team']
            props = player_props.get(name, {})

            # Only include players with actual props (no baselines)
            relevant_keys = {
                'player_pass_yds','player_pass_tds','player_pass_completions','player_pass_attempts',
                'player_pass_interceptions','player_rush_yds','player_rush_tds','player_rush_attempts',
                'player_receptions','player_reception_yds','player_reception_tds','player_anytime_td',
                'player_kicking_points','player_tackles_assists','player_sacks','player_defensive_interceptions',
                'player_longest_reception','player_longest_rush','player_longest_pass'
            }
            if (not props) or (len(set(props.keys()) & relevant_keys) == 0):
                continue
            # For kickers, require an actual kicking points market
            if pos == 'K' and 'player_kicking_points' not in props:
                continue

            read = self._elite_prop_read(name, props)
            base = self._calc_from_props(name, pos, props, read)
            ctx = self._apply_context_multiplier(name, pos, team, home_tag)
            proj = base * ctx
            if proj <= 0:
                continue
            projections[name] = round(proj, 2)

        # Add defenses
        projections['49ers'] = round(self._defense_projection(gc.home_team if '49ers' in gc.home_team else gc.away_team, gc, player_props), 2)
        projections['Saints'] = round(self._defense_projection(gc.home_team if 'Saints' in gc.home_team else gc.away_team, gc, player_props), 2)

        ranked = sorted(projections.items(), key=lambda x: x[1], reverse=True)
        return ranked


def main() -> None:
    system = EliteProjectionSystem()
    ranked = system.run()
    print("Player,Projection")
    for name, pts in ranked:
        print(f"{name},{pts:.2f}")
    # Save to CSV
    ts = datetime.now().strftime('%Y%m%d_%H%M')
    out = f"niners_saints_elite_projections_{ts}.csv"
    with open(out, 'w') as f:
        f.write("Player,Projection\n")
        for name, pts in ranked:
            f.write(f"{name},{pts:.2f}\n")


if __name__ == '__main__':
    main()

