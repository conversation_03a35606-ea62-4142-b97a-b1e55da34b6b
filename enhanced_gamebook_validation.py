#!/usr/bin/env python3
"""
🧠 ENHANCED GAMEBOOK VALIDATION SYSTEM
Deep integrity checks with proper gamebook format parsing
"""

import os
import re
import pandas as pd
from typing import Dict, List, Any, Tuple, Optional
from dataclasses import dataclass
from gamebook_intelligence import GamebookIntelligence


@dataclass
class ParsedGameStats:
    """Parsed statistics from raw gamebook."""
    team_name: str
    score_by_quarters: List[int]
    total_score: int
    third_down_efficiency: Tuple[int, int, float]  # made, attempts, percentage
    fourth_down_efficiency: Tuple[int, int, float]
    total_net_yards: int
    total_plays: int
    avg_per_play: float
    net_rushing_yards: int
    rushing_plays: int
    avg_per_rush: float
    net_passing_yards: int
    pass_attempts: int
    pass_completions: int
    pass_interceptions: int
    time_of_possession: str


@dataclass
class ValidationCheck:
    """Individual validation check result."""
    check_name: str
    category: str
    passed: bool
    expected: Any
    actual: Any
    tolerance: float = 0.0
    error_message: str = ""


class EnhancedGamebookValidator:
    """🧠 Enhanced Gamebook Validation with Deep Parsing"""
    
    def __init__(self):
        self.intelligence = GamebookIntelligence()
        self.validation_checks: List[ValidationCheck] = []
        self.parsed_games: Dict[str, Tuple[ParsedGameStats, ParsedGameStats]] = {}
    
    def run_comprehensive_validation(self) -> None:
        """🚀 Run comprehensive validation with deep parsing."""
        print("🧠 ENHANCED GAMEBOOK VALIDATION - DEEP INTEGRITY CHECK")
        print("=" * 60)
        
        # Load gamebook intelligence
        self.intelligence.run_full_analysis()
        
        # Parse and validate key games
        key_games = [
            ("Vikings", "Bears", "Vikings vs Bears.md"),
            ("Chiefs", "Chargers", "chiefs chargers.md"),
            ("Ravens", "Bills", "ravens bills.md")
        ]
        
        for away_team, home_team, filename in key_games:
            print(f"\n📊 DEEP VALIDATION: {away_team} @ {home_team}")
            self._validate_game_deeply(away_team, home_team, filename)
        
        # Generate comprehensive integrity report
        print(f"\n✅ COMPREHENSIVE INTEGRITY REPORT")
        self._generate_integrity_report()
    
    def _validate_game_deeply(self, away_team: str, home_team: str, filename: str) -> None:
        """Perform deep validation on a single game."""
        
        # Parse raw gamebook
        away_stats, home_stats = self._parse_raw_gamebook(filename)
        if not away_stats or not home_stats:
            print(f"   🚨 CRITICAL: Could not parse {filename}")
            return
        
        self.parsed_games[f"{away_team} @ {home_team}"] = (away_stats, home_stats)
        
        # Find corresponding game in intelligence data
        game_data = None
        for game in self.intelligence.games_data:
            if game.away_team == away_team and game.home_team == home_team:
                game_data = game
                break
        
        if not game_data:
            print(f"   🚨 CRITICAL: Game not found in intelligence data")
            return
        
        # Run validation checks
        checks_passed = 0
        total_checks = 0
        
        # 1. ✅ Core Parsing Checks
        core_checks = self._validate_core_parsing_deep(game_data, away_stats, home_stats)
        checks_passed += sum(1 for c in core_checks if c.passed)
        total_checks += len(core_checks)
        self.validation_checks.extend(core_checks)
        
        # 2. 📊 Statistical Accuracy Checks
        stat_checks = self._validate_statistical_accuracy(game_data, away_stats, home_stats)
        checks_passed += sum(1 for c in stat_checks if c.passed)
        total_checks += len(stat_checks)
        self.validation_checks.extend(stat_checks)
        
        # 3. 🧮 Derived Metrics Validation
        metric_checks = self._validate_derived_metrics_deep(game_data, away_stats, home_stats)
        checks_passed += sum(1 for c in metric_checks if c.passed)
        total_checks += len(metric_checks)
        self.validation_checks.extend(metric_checks)
        
        # Print game summary
        status = "✅ EXCELLENT" if checks_passed == total_checks else "⚠️ ISSUES FOUND"
        print(f"   {status} - {checks_passed}/{total_checks} checks passed")
        
        # Show failed checks
        failed_checks = [c for c in (core_checks + stat_checks + metric_checks) if not c.passed]
        if failed_checks:
            print("   🚨 FAILED CHECKS:")
            for check in failed_checks[:3]:  # Show first 3
                print(f"      • {check.check_name}: Expected {check.expected}, got {check.actual}")
    
    def _parse_raw_gamebook(self, filename: str) -> Tuple[Optional[ParsedGameStats], Optional[ParsedGameStats]]:
        """Parse raw gamebook into structured statistics."""
        filepath = f"csvs/Gamebook Results/{filename}"
        if not os.path.exists(filepath):
            return None, None
        
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()
            
            lines = content.split('\n')
            
            # Find the statistics section
            visitor_stats = None
            home_stats = None
            
            for i, line in enumerate(lines):
                line = line.strip()
                
                # Look for visitor team stats
                if "**Visitor**" in line and i + 10 < len(lines):
                    visitor_stats = self._parse_team_stats_block(lines[i:i+15], "visitor")
                
                # Look for home team stats
                if "**Home**" in line and i + 10 < len(lines):
                    home_stats = self._parse_team_stats_block(lines[i:i+15], "home")
            
            return visitor_stats, home_stats
            
        except Exception as e:
            print(f"Error parsing {filepath}: {e}")
            return None, None
    
    def _parse_team_stats_block(self, lines: List[str], team_type: str) -> Optional[ParsedGameStats]:
        """Parse a team's statistics block."""
        try:
            # Extract team name
            team_name = ""
            for line in lines[:3]:
                if "Vikings" in line or "Bears" in line or "Chiefs" in line or "Chargers" in line or "Ravens" in line or "Bills" in line:
                    if "Vikings" in line:
                        team_name = "Vikings"
                    elif "Bears" in line:
                        team_name = "Bears"
                    elif "Chiefs" in line:
                        team_name = "Chiefs"
                    elif "Chargers" in line:
                        team_name = "Chargers"
                    elif "Ravens" in line:
                        team_name = "Ravens"
                    elif "Bills" in line:
                        team_name = "Bills"
                    break
            
            # Parse score by quarters (line with 4 numbers)
            score_by_quarters = []
            total_score = 0
            for line in lines:
                line = line.strip().replace('>', '').strip()
                if re.match(r'^\d+\s+\d+\s+\d+\s+\d+$', line):
                    quarters = [int(x) for x in line.split()]
                    score_by_quarters = quarters
                    total_score = sum(quarters)
                    break
            
            # Parse third down efficiency (format: 3-12-25.0%)
            third_down = (0, 0, 0.0)
            fourth_down = (0, 0, 0.0)
            total_net_yards = 0
            
            for line in lines:
                line = line.strip().replace('>', '').strip()
                
                # Third down efficiency pattern
                third_match = re.search(r'(\d+)-(\d+)-(\d+\.\d+)%', line)
                if third_match:
                    made = int(third_match.group(1))
                    attempts = int(third_match.group(2))
                    percentage = float(third_match.group(3))
                    third_down = (made, attempts, percentage)
                
                # Total net yards (3-digit number)
                yards_match = re.search(r'\b(\d{3})\b', line)
                if yards_match and total_net_yards == 0:
                    total_net_yards = int(yards_match.group(1))
            
            # Parse total plays and averages (format: 49 5.2 120 26 4.6 3-6 134 3-9 143)
            total_plays = 0
            avg_per_play = 0.0
            net_rushing_yards = 0
            rushing_plays = 0
            avg_per_rush = 0.0
            net_passing_yards = 0
            
            for line in lines:
                line = line.strip().replace('>', '').strip()
                
                # Look for the main stats line (starts with plays count)
                if re.match(r'^\d{2}\s+\d\.\d', line):
                    parts = line.split()
                    if len(parts) >= 5:
                        total_plays = int(parts[0])
                        avg_per_play = float(parts[1])
                        net_rushing_yards = int(parts[2])
                        rushing_plays = int(parts[3])
                        avg_per_rush = float(parts[4])
                    break
            
            # Parse passing stats (format: 20-13-1 5.8)
            pass_attempts = 0
            pass_completions = 0
            pass_interceptions = 0
            
            for line in lines:
                line = line.strip().replace('>', '').strip()
                
                # Passing stats pattern
                pass_match = re.search(r'(\d+)-(\d+)-(\d+)\s+\d\.\d', line)
                if pass_match:
                    pass_attempts = int(pass_match.group(1))
                    pass_completions = int(pass_match.group(2))
                    pass_interceptions = int(pass_match.group(3))
                    break
            
            # Calculate net passing yards
            net_passing_yards = total_net_yards - net_rushing_yards
            
            # Parse time of possession (format: 27:07)
            time_of_possession = ""
            for line in lines:
                line = line.strip().replace('>', '').strip()
                time_match = re.search(r'(\d{1,2}:\d{2})', line)
                if time_match:
                    time_of_possession = time_match.group(1)
                    break
            
            return ParsedGameStats(
                team_name=team_name,
                score_by_quarters=score_by_quarters,
                total_score=total_score,
                third_down_efficiency=third_down,
                fourth_down_efficiency=fourth_down,
                total_net_yards=total_net_yards,
                total_plays=total_plays,
                avg_per_play=avg_per_play,
                net_rushing_yards=net_rushing_yards,
                rushing_plays=rushing_plays,
                avg_per_rush=avg_per_rush,
                net_passing_yards=net_passing_yards,
                pass_attempts=pass_attempts,
                pass_completions=pass_completions,
                pass_interceptions=pass_interceptions,
                time_of_possession=time_of_possession
            )
            
        except Exception as e:
            print(f"Error parsing team stats: {e}")
            return None
    
    def _validate_core_parsing_deep(self, game_data, away_stats: ParsedGameStats, 
                                   home_stats: ParsedGameStats) -> List[ValidationCheck]:
        """Deep validation of core parsing accuracy."""
        checks = []
        
        # Validate final scores
        checks.append(ValidationCheck(
            check_name="Away Team Final Score",
            category="Core Parsing",
            passed=away_stats.total_score == game_data.away_score,
            expected=away_stats.total_score,
            actual=game_data.away_score,
            error_message=f"Score mismatch for {away_stats.team_name}"
        ))
        
        checks.append(ValidationCheck(
            check_name="Home Team Final Score",
            category="Core Parsing",
            passed=home_stats.total_score == game_data.home_score,
            expected=home_stats.total_score,
            actual=game_data.home_score,
            error_message=f"Score mismatch for {home_stats.team_name}"
        ))
        
        return checks
    
    def _validate_statistical_accuracy(self, game_data, away_stats: ParsedGameStats, 
                                     home_stats: ParsedGameStats) -> List[ValidationCheck]:
        """Validate statistical accuracy."""
        checks = []
        
        # Validate total plays
        expected_total_plays = away_stats.total_plays + home_stats.total_plays
        actual_total_plays = game_data.total_plays
        
        checks.append(ValidationCheck(
            check_name="Total Plays Accuracy",
            category="Statistical Accuracy",
            passed=abs(expected_total_plays - actual_total_plays) <= 5,  # Allow 5 play tolerance
            expected=expected_total_plays,
            actual=actual_total_plays,
            tolerance=5.0,
            error_message=f"Total plays mismatch: expected {expected_total_plays}, got {actual_total_plays}"
        ))
        
        # Validate rushing statistics
        for team_stats, team_name in [(away_stats, game_data.away_team), (home_stats, game_data.home_team)]:
            if team_name in game_data.rushing_stats:
                parsed_rush_yards = game_data.rushing_stats[team_name].get('yards', 0)
                expected_rush_yards = team_stats.net_rushing_yards
                
                checks.append(ValidationCheck(
                    check_name=f"{team_name} Rushing Yards",
                    category="Statistical Accuracy",
                    passed=abs(expected_rush_yards - parsed_rush_yards) <= 10,  # Allow 10 yard tolerance
                    expected=expected_rush_yards,
                    actual=parsed_rush_yards,
                    tolerance=10.0,
                    error_message=f"Rushing yards mismatch for {team_name}"
                ))
        
        return checks
    
    def _validate_derived_metrics_deep(self, game_data, away_stats: ParsedGameStats, 
                                     home_stats: ParsedGameStats) -> List[ValidationCheck]:
        """Validate derived metrics calculations."""
        checks = []
        
        # Validate yards per play calculations
        for team_stats, team_name in [(away_stats, game_data.away_team), (home_stats, game_data.home_team)]:
            expected_ypp = team_stats.avg_per_play
            calculated_ypp = team_stats.total_net_yards / team_stats.total_plays if team_stats.total_plays > 0 else 0
            
            checks.append(ValidationCheck(
                check_name=f"{team_name} Yards Per Play Consistency",
                category="Derived Metrics",
                passed=abs(expected_ypp - calculated_ypp) <= 0.2,  # Allow 0.2 YPP tolerance
                expected=expected_ypp,
                actual=calculated_ypp,
                tolerance=0.2,
                error_message=f"YPP calculation inconsistent for {team_name}"
            ))
        
        # Validate completion percentage
        for team_stats, team_name in [(away_stats, game_data.away_team), (home_stats, game_data.home_team)]:
            if team_stats.pass_attempts > 0:
                expected_comp_pct = team_stats.pass_completions / team_stats.pass_attempts
                
                checks.append(ValidationCheck(
                    check_name=f"{team_name} Completion Percentage Logic",
                    category="Derived Metrics",
                    passed=0.0 <= expected_comp_pct <= 1.0,
                    expected="0.0-1.0 range",
                    actual=f"{expected_comp_pct:.3f}",
                    error_message=f"Completion percentage out of range for {team_name}"
                ))
        
        return checks
    
    def _generate_integrity_report(self) -> None:
        """Generate comprehensive integrity report."""
        total_checks = len(self.validation_checks)
        passed_checks = sum(1 for c in self.validation_checks if c.passed)
        failed_checks = total_checks - passed_checks
        
        print("=" * 60)
        print(f"📊 COMPREHENSIVE VALIDATION SUMMARY:")
        print(f"   Total Checks: {total_checks}")
        print(f"   Passed: {passed_checks} ({passed_checks/total_checks*100:.1f}%)")
        print(f"   Failed: {failed_checks} ({failed_checks/total_checks*100:.1f}%)")
        
        # Group by category
        categories = {}
        for check in self.validation_checks:
            if check.category not in categories:
                categories[check.category] = {'passed': 0, 'total': 0}
            categories[check.category]['total'] += 1
            if check.passed:
                categories[check.category]['passed'] += 1
        
        print(f"\n📋 BY CATEGORY:")
        for category, stats in categories.items():
            pct = stats['passed'] / stats['total'] * 100 if stats['total'] > 0 else 0
            print(f"   {category}: {stats['passed']}/{stats['total']} ({pct:.1f}%)")
        
        # Show critical failures
        critical_failures = [c for c in self.validation_checks if not c.passed and 'Score' in c.check_name]
        if critical_failures:
            print(f"\n🚨 CRITICAL FAILURES:")
            for failure in critical_failures:
                print(f"   • {failure.check_name}: {failure.error_message}")
        
        # Overall assessment
        integrity_score = passed_checks / total_checks if total_checks > 0 else 0
        print(f"\n🏆 OVERALL INTEGRITY SCORE: {integrity_score:.1%}")
        
        if integrity_score >= 0.95:
            print("✅ EXCELLENT - Gamebook parsing is highly accurate")
            print("🎯 READY FOR PROJECTION ENGINE: ✅ YES")
        elif integrity_score >= 0.85:
            print("✅ GOOD - Minor issues detected, system ready for production")
            print("🎯 READY FOR PROJECTION ENGINE: ✅ YES")
        elif integrity_score >= 0.70:
            print("⚠️ FAIR - Some issues detected, review recommended")
            print("🎯 READY FOR PROJECTION ENGINE: ⚠️ CONDITIONAL")
        else:
            print("🚨 POOR - Significant issues detected, parsing needs improvement")
            print("🎯 READY FOR PROJECTION ENGINE: ❌ NO")
        
        # Show sample parsed data
        print(f"\n📊 SAMPLE PARSED DATA:")
        for game_name, (away_stats, home_stats) in list(self.parsed_games.items())[:1]:
            print(f"   {game_name}:")
            print(f"   {away_stats.team_name}: {away_stats.total_score} pts, {away_stats.total_net_yards} yds, {away_stats.total_plays} plays")
            print(f"   {home_stats.team_name}: {home_stats.total_score} pts, {home_stats.total_net_yards} yds, {home_stats.total_plays} plays")


def main():
    """Run enhanced gamebook validation."""
    validator = EnhancedGamebookValidator()
    validator.run_comprehensive_validation()


if __name__ == "__main__":
    main()
