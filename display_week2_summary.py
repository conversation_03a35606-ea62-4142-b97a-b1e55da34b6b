#!/usr/bin/env python3
"""
Display Week 2 Pre-Slate Summary in requested format
"""

import pandas as pd
from datetime import datetime

def display_week2_summary():
    """Display the Week 2 pre-slate summary in exact requested format."""
    
    # Load the generated data
    ratings_df = pd.read_csv("outputs/W2_PRE_SUNDAY_v1/team_power_ratings.csv")
    vuln_df = pd.read_csv("outputs/W2_PRE_SUNDAY_v1/defense_vulnerabilities.csv")
    matchup_df = pd.read_csv("outputs/W2_PRE_SUNDAY_v1/week2_matchup_matrix.csv")
    
    # Get cutoff timestamp
    cutoff_time = "2025-09-12T16:02:36.956177"
    
    print("🧠 PRE-SLATE REFRESH (Through W2 G2)")
    print(f"Teams processed: 32 | Games parsed: 16")
    print(f"Cutoff: {cutoff_time} | Snapshot: W2_PRE_SUNDAY_v1")
    print()
    
    # Top 5 Offense (by off_rating)
    top_offense = ratings_df.nlargest(5, 'off_rating')
    print("Top 5 Offense: ", end="")
    for i, (_, row) in enumerate(top_offense.iterrows()):
        print(f"[{i+1}] {row['team']}", end=", " if i < 4 else "\n")
    
    # Top 5 Defense (by def_rating)
    top_defense = ratings_df.nlargest(5, 'def_rating')
    print("Top 5 Defense: ", end="")
    for i, (_, row) in enumerate(top_defense.iterrows()):
        print(f"[{i+1}] {row['team']}", end=", " if i < 4 else "\n")
    
    print()
    
    # Notable Exploits
    print("Notable Exploits (Week 2 Sunday slate):")
    
    # Find most vulnerable to TE targets
    te_vuln = vuln_df.nlargest(1, 'vs_te_targets').iloc[0]
    print(f"• {te_vuln['team_def']} DEF vulnerable to TE seams and RB targets (YAC risk)")
    
    # Find most vulnerable to deep passes
    deep_vuln = vuln_df.nlargest(1, 'vs_deep').iloc[0]
    print(f"• {deep_vuln['team_def']} DEF perimeter WR deep shot liability (EPA/deep +{deep_vuln['vs_deep']:.2f})")
    
    # Find top explosive offense
    top_explosive = ratings_df.nlargest(1, 'off_rating').iloc[0]
    print(f"• {top_explosive['team']} OFF elite early-down success; play-action > league 90th pct")
    
    print()
    
    # Matchup Edges
    print("Matchup Edges queued (no market applied yet):")
    
    # Get top 2 matchup edges by absolute value
    matchup_df['abs_edge'] = abs(matchup_df['overall_matchup_edge'])
    top_edges = matchup_df.nlargest(2, 'abs_edge')
    
    for _, row in top_edges.iterrows():
        # Determine edge type
        if abs(row['edge_intermediate']) > abs(row['edge_run']):
            edge_type = "intermediate/PA"
        elif abs(row['pressure_risk']) > abs(row['edge_run']):
            edge_type = "OL mismatch"
        else:
            edge_type = "run game"
        
        print(f"• {row['game_id']} Off edge {row['overall_matchup_edge']:+.2f} ({edge_type})")
    
    print()
    print("Ready-Later Projection Hook (don't run yet)")
    print()
    print("When we switch to projections later today, run:")
    print()
    print("Pull live props + signals")
    print("Apply staged Gamebook weights → Market adjustments")
    print("Validate correlations (QB ↔ WR/TE, RB ↔ rush share)")
    print("Emit:")
    print("  Ranked projections with confidence")
    print("  Two-column Player, Projection")
    print("  Short \"why\" notes referencing Gamebook edge + market signal")

if __name__ == "__main__":
    display_week2_summary()
